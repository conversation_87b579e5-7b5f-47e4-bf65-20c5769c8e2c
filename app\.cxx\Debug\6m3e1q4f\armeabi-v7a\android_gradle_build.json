{"buildFiles": ["C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"adnan::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "<PERSON>nan", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\build\\intermediates\\cxx\\Debug\\6m3e1q4f\\obj\\armeabi-v7a\\libadnan.so", "runtimeFiles": []}, "mupdfcore::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "mupdfcore", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\armeabi-v7a\\libmupdfcore.a"}, "mupdfthirdparty::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "mupdfthirdparty", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\armeabi-v7a\\libmupdfthirdparty.a"}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": ["c"], "cppFileExtensions": []}