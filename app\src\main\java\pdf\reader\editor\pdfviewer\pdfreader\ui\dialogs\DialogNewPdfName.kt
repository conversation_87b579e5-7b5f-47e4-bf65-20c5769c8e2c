package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogNewpdfNameBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import java.util.regex.Matcher
import java.util.regex.Pattern

class DialogNewPdfName : BaseDialog<DialogNewpdfNameBinding>() {

    companion object {
        //arguments
        private var callback: ((ViewPdfActions, String) -> Unit)? = null
        private var fileName: String? = null
        fun getInstance(
            fileName: String?,
            callback: ((ViewPdfActions, String) -> Unit)
        ): DialogNewPdfName {
            this.callback = callback
            this.fileName = fileName
            return DialogNewPdfName()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Handler(Looper.getMainLooper()).postDelayed({
            view.showKeyboard()
            binding.edtRename.requestFocus()
        }, 500)
        this.isCancelable = false

        if (!fileName.isNullOrEmpty()) {
            binding.edtRename.setText(fileName)
        }

        binding.btnSave.setOnClickListener {

            val pass: String = binding.edtRename.text?.trim().toString()
            if (pass.isEmpty()) {
                callback?.invoke(ViewPdfActions.NULL_PDF_PASSWORD, "")
            } else if (checkSpecialCharacters(pass)) {
                callback?.invoke(ViewPdfActions.SPECIAL_CHARACTER_VOILATION, "")
            } else if (checkExtension(pass)) {
                callback?.invoke(ViewPdfActions.INVALID_EXTENSION, "")
            } else {
                it.hideKeyboard()
                callback?.invoke(
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED,
                    binding.edtRename.text?.trim().toString()
                )
                dismiss()
            }


        }

        binding.btnCancel.setOnClickListener {
            it.hideKeyboard()
            dismiss()
        }
    }

//    override fun onStart() {
//        super.onStart()
////        setTopAnimation()
//    }

    private fun checkSpecialCharacters(str: String): Boolean {
        val special: Pattern = Pattern.compile("[!@#$%&*+=|<>?{}\\[\\]~]")
        val hasSpecial: Matcher = special.matcher(str)
        return hasSpecial.find()
    }


    private fun checkExtension(str: String): Boolean {
        val pdfExt: Pattern = Pattern.compile("[.]pdf")
        val hasSpecial: Matcher = pdfExt.matcher(str)
        return hasSpecial.find()
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogNewpdfNameBinding =
        DialogNewpdfNameBinding.inflate(LayoutInflater.from(requireContext()), null, false)

}