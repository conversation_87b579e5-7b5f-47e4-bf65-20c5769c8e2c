package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.editpdf

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.WebView
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.itextpdf.html2pdf.HtmlConverter
import com.itextpdf.kernel.crypto.BadPasswordException
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfReader
import com.itextpdf.kernel.pdf.ReaderProperties
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentEditPdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.managepdf.AnnotatePdf
import pdf.reader.editor.pdfviewer.pdfreader.managepdf.AnnotationConstants
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview.PDFViewerViewModel
import java.io.File
import java.io.FileOutputStream

@AndroidEntryPoint
class EditPDFFragment : BaseFragment<FragmentEditPdfBinding, EditViewModel>() {

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentEditPdfBinding = if (mViewModel.view == null)
        FragmentEditPdfBinding.inflate(inflater, container, false)
    else
        FragmentEditPdfBinding.bind(mViewModel.view!!)

    override val viewModel: Class<EditViewModel>
        get() = EditViewModel::class.java

    private val args: EditPDFFragmentArgs by navArgs()
    private var documentModel: DocumentsModel? = null
    private var password = ""
    private val pdfViewModel: PDFViewerViewModel by navGraphViewModels(R.id.pdfViewFragment)
    private var renderedFile: File? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        documentModel = args.pdfFile

        try {
            PdfDocument(
                PdfReader(
                    documentModel?.absolutePath,
                )
            )
            renderPDF(File(documentModel?.absolutePath!!), password)

        } catch (e: BadPasswordException) {
            getPasswordDialog()

        } catch (e: Exception) {
            showToast("File is corrupt or not PDF.")
        }

        initViews()
    }

    private fun initViews() {
        mViewDataBinding.renderedPdfView.apply {

            settings.javaScriptEnabled = true
            settings.allowFileAccess = true
            settings.allowUniversalAccessFromFileURLs = true
            settings.builtInZoomControls = true
            settings.displayZoomControls = false
            settings.loadWithOverviewMode = true
            settings.useWideViewPort = true
            scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            isScrollbarFadingEnabled = true

        }

        mViewDataBinding.apply {
            imgEditUnder.setOnClickListener {
                AnnotatePdf.drawUnderline(renderedPdfView)
            }

            imgEditBold.setOnClickListener {
                AnnotatePdf.makeFontsBold(renderedPdfView)
            }
            imgEditHigh.setOnClickListener {
                AnnotatePdf.highlightPdfContent(renderedPdfView)
            }
            imgEditItalic.setOnClickListener {
                AnnotatePdf.textStyle(renderedPdfView)
            }
            imgEditDone.setOnClickListener {
//                AnnotatePdf.getHtml(mViewDataBinding.renderedPdfView) {
////                    executeNewPdfSaving("html test", it)
//                }

                loadingDialog.show()
//                SavePdfChanges.save(
//                    mViewDataBinding.renderedPdfView,
//                    requireActivity(),
//                    documentModel?.fileName!!,
//                    documentModel?.parentFile!!
//                ) { savedFilePath, savedFileName ->
////                    pdfViewModel.listData.postValue(0)
//                    loadingDialog.hide()
//                    findNavController().popBackStack()
//                }
            }
        }


    }

    private fun executeNewPdfSaving(fileName: String, pdfString: String) {
        CoroutineScope(IO).launch {
            activity?.runOnUiThread {
                loadingDialog.show()
            }
            val savePath = Environment.getExternalStorageDirectory()
                .absolutePath + AnnotationConstants.NEW_PDF_FILE
            val dir = File(savePath);
            if (!dir.exists()) {
                dir.mkdirs()
            }
            val p = "$savePath/$fileName.pdf"
            HtmlConverter.convertToPdf(
                pdfString,
                FileOutputStream(p)
            )
            activity?.sendBroadcast(
                Intent(
                    Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                        File(p)
                    )
                )
            )

            activity?.runOnUiThread {
                loadingDialog.hide()
            }
        }
    }

    private fun getScale(): Int {
        val PIC_WIDTH: Int =
            mViewDataBinding.renderedPdfView.right - mViewDataBinding.renderedPdfView.left
        val display: Display = (requireContext().getSystemService(
            Context.WINDOW_SERVICE
        ) as WindowManager).defaultDisplay
        val width: Int = display.width
        var `val`: Int = width / PIC_WIDTH
        `val` = (`val` * 120.0).toInt()
        return `val`
    }


    private fun initWebView() {
        AnnotatePdf.selfInvokingFun(mViewDataBinding.renderedPdfView)
        AnnotatePdf.removeEditingBorders(mViewDataBinding.renderedPdfView)
        AnnotatePdf.enablePdfEditing(mViewDataBinding.renderedPdfView)
    }

    private fun renderPDF(file: File, password: String = "") {
        lifecycleScope.launch(Dispatchers.IO) {
//            PdfRendring.renderWithPassword(requireActivity(), file, password)?.let { renderedFile ->
//                withContext(Dispatchers.Main) {
//
//                    mViewDataBinding.renderedPdfView.loadUrl(renderedFile.toString())
//
//                    mViewDataBinding.renderedPdfView.setInitialScale(<EMAIL>())
//                }
//
//            }
        }

    }

    private fun getPasswordDialog() {
        DialogPdfPassword.getInstance { action, password ->
            when (action) {
                ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                    try {
                        PdfDocument(
                            PdfReader(
                                documentModel?.absolutePath,
                                ReaderProperties().setPassword(password.toByteArray())
                            )
                        )
                        this.password = password
                        renderPDF(File(documentModel?.absolutePath), this.password)

                    } catch (e: BadPasswordException) {
                        getPasswordDialog()
                    }

                }
                ViewPdfActions.NULL_PDF_PASSWORD -> {
                    showToast(getString(R.string.txt_pdf_null_password_warning))
                }
                ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {

                }

                else -> {}
            }
        }.show(childFragmentManager, "Password Dialog")
    }

}