{"buildFiles": ["C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\n4j61447\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\n4j61447\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"adnan::@6890427a1f51a3e7e1df": {"artifactName": "<PERSON>nan", "abi": "x86", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\build\\intermediates\\cxx\\Debug\\n4j61447\\obj\\x86\\libadnan.so", "runtimeFiles": ["C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\libgs.so"]}, "mupdfcore::@6890427a1f51a3e7e1df": {"artifactName": "mupdfcore", "abi": "x86", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\n4j61447\\x86\\libmupdfcore.a", "runtimeFiles": []}, "mupdfthirdparty::@6890427a1f51a3e7e1df": {"artifactName": "mupdfthirdparty", "abi": "x86", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\n4j61447\\x86\\libmupdfthirdparty.a", "runtimeFiles": []}}}