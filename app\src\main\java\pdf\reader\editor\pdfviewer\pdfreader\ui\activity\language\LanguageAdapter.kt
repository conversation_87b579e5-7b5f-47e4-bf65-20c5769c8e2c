package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.language

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ItemLanguageLayoutBinding

class LanguageAdapter(
    private val language: List<String>,
    private var selected: Int = 0,
    private val onClick: (Int) -> Unit
) :
    RecyclerView.Adapter<LanguageAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = ViewHolder(
        ItemLanguageLayoutBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = language[position]

        holder.binding.apply {

            rbLanguage.apply {
                text = item
                isChecked = selected == position
            }

            root.setOnClickListener {
                selected = position
                onClick(selected)
                notifyDataSetChanged()
            }
        }


    }


    override fun getItemCount() = language.size


    inner class ViewHolder(val binding: ItemLanguageLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)
}