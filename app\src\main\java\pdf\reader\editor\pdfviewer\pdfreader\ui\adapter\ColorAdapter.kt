package pdf.reader.editor.pdfviewer.pdfreader.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.artifex.mupdfdemo.ColorPalette
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ColorItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show

class ColorAdapter(
    private val type: com.artifex.mupdfdemo.Annotation.Type,
    private val colorList: Int,
    private val selectedPos: Int,
    private val onClick: (Int) -> Unit
) :
    RecyclerView.Adapter<ColorAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding: ColorItemLayoutBinding =
            ColorItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        if (position == selectedPos)
            holder.binding.imgSelectedIcon.show()
        else
            holder.binding.imgSelectedIcon.hide()

        if (position != colorList - 1)
            holder.binding.imgColorItem.setColorFilter(
                ColorPalette.getHex(position, type)
            )

        holder.binding.imgColorParent.setOnClickListener {
            onClick(position)
        }

    }

    override fun getItemCount(): Int = colorList

    class ViewHolder(val binding: ColorItemLayoutBinding) : RecyclerView.ViewHolder(binding.root)
}