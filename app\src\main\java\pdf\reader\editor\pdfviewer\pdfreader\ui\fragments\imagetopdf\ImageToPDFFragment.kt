package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.imagetopdf

import android.content.Intent
import android.graphics.pdf.PdfDocument
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentImageToPdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getScreenHeight
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getScreenWidth
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.loadBitmapForPdf
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.GridSpacingItemDecoration
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.imagetopdf.adapter.ImagePickerAdapter
import java.io.File
import java.io.FileOutputStream

class ImageToPDFFragment : BaseFragment<FragmentImageToPdfBinding, ImageToPDFViewModel>() {
    override val viewModel: Class<ImageToPDFViewModel>
        get() = ImageToPDFViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentImageToPdfBinding = FragmentImageToPdfBinding.inflate(inflater, container, false)

    private var selectedPhotosList: ArrayList<PhotoModel> = ArrayList()
    private var imagePickerAdapter: ImagePickerAdapter? = null


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

    }

    private fun initViews() {
        isAlive {
            imagePickerAdapter = ImagePickerAdapter(0) { photo ->
                selectedPhotosList = photo
            }

            mViewDataBinding.imgBack.setOnClickListener {
                isAlive {
                    findNavController().popBackStack()
                }

            }

            mViewDataBinding.imagesRecyclerView.apply {
                layoutManager = GridLayoutManager(it, 2)
                adapter = imagePickerAdapter
                addItemDecoration(
                    GridSpacingItemDecoration(
                        2,
                        50,
                        true
                    )
                )
            }

            mViewDataBinding.btnCreatePDF.setOnClickListener {
                if (selectedPhotosList.size > 0) {

                    DialogNewPdfName.getInstance(null) { action, fileName ->
                        when (action) {
                            ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                                lifecycleScope.launch(Dispatchers.IO) {

                                    create(fileName)
                                }
                            }
                            ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                                Toast.makeText(
                                    activity,
                                    getString(R.string.txt_pdf_special_charac_normal),
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                            ViewPdfActions.INVALID_EXTENSION -> {
                                Toast.makeText(
                                    activity,
                                    getString(R.string.txt_pdf_invalid_extension),
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                            ViewPdfActions.NULL_PDF_PASSWORD -> {
                                showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                            }

                            else -> {}
                        }
                    }.show(childFragmentManager, "Merge Files")

                } else
                    showToast(getString(R.string.please_select_photos_first))
            }

            mViewModel.imagesList.observe(viewLifecycleOwner) { photosList ->
                if (photosList != null)
                    imagePickerAdapter?.setData(photosList)
            }

            mViewModel.loadImages(requireActivity())
        }

    }

    private fun create(fileName: String) {
        isAlive {
            it.runOnUiThread {
                loadingDialog.show()
            }

            val pdfDocument = PdfDocument()
            var file: File? = null

            it.runOnUiThread {
                file = FileUtils.saveFileToExternalStorage(fileName)
            }

            selectedPhotosList.forEachIndexed { index, photoModel ->

                it.loadBitmapForPdf(
                    photoModel.path,
                    width = it.getScreenWidth(),
                    height = it.getScreenHeight()
                ) { bitmap ->
                    bitmap?.let { rotatedbmp ->
                        val pageInfo =
                            PdfDocument.PageInfo.Builder(
                                rotatedbmp.width,
                                rotatedbmp.height,
                                index + 1
                            ).create()
                        val page = pdfDocument.startPage(pageInfo)
                        val canvas = page.canvas
                        val centreX = ((canvas.width - rotatedbmp.width) / 2)
                        val centreY = ((canvas.height - rotatedbmp.height) / 2)
                        canvas.drawBitmap(
                            rotatedbmp,
                            centreX.toFloat(),
                            centreY.toFloat(),
                            null
                        )
                        pdfDocument.finishPage(page)
                        rotatedbmp.recycle()
                        if (index == selectedPhotosList.lastIndex) {
//                        runOnUiThread {
//                            showToast("Finishing....")
//                        }
                            val fos = FileOutputStream(file)
                            pdfDocument.writeTo(fos)
                            fos.flush()
                            fos.close()
                            activity?.runOnUiThread {

                                activity?.sendBroadcast(
                                    Intent(
                                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                                            file
                                        )
                                    )
                                )
                                isAlive { con ->
                                    sharedViewModel.loadFiles(requireActivity())
                                    loadingDialog.dismiss()
                                    findNavController().popBackStack()
                                }

                            }
                        }
//                    runOnUiThread {
//                        showToast("Page ${index + 1} of ${selectedPhotosList.size}")
//
//
//                    }
                    }
                }
            }
        }

    }

}