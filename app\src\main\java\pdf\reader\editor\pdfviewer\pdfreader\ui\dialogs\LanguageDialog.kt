package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.app.Dialog
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.core.content.edit
import androidx.fragment.app.DialogFragment
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.language.LanguageData

class LanguageDialog : DialogFragment() {


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.select_language))
            .setCancelable(false)
            .setSingleChoiceItems(
                LanguageData.getAppLanguageList().toTypedArray(),
                selected
            ) { _, id ->
                selected = id

            }
            .setPositiveButton(getString(R.string.done)) { _, _ ->
                if (selected == 0) {
                    selected = 100
                }

                Storage(requireContext()).preferences.edit {
                    putInt("localization", selected)
                }
                dismiss()
                callback?.invoke(true)
            }
            .setNegativeButton(getString(R.string.cancel)) { _, _ ->
                dismiss()
                callback?.invoke(false)
            }
            .create()
    }


    companion object {

        private var selected = 0
        private var callback: ((Boolean) -> Unit)? = null
        fun create(selected: Int, callback: (Boolean) -> Unit): LanguageDialog {
            this.selected = selected
            this.callback = callback
            return LanguageDialog()
        }
    }
}