package pdf.reader.editor.pdfviewer.pdfreader.baseclasses

import androidx.lifecycle.ViewModel
import java.lang.Math.log10
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.pow


open class BaseViewModel : ViewModel() {

//    var pdfEventBus: PdfEventBus = PdfEventBus()

    fun getReadableSize(size: Long): String {
        try {
            val symbols: DecimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.US)

            if (size <= 0) return "0"
            val units = arrayOf("B", "kB", "MB", "GB", "TB")
            val digitGroups = (log10(size.toDouble()) / log10(1024.0)).toInt()
            return DecimalFormat(
                "#,##0.#",
                symbols
            ).format(size / 1024.0.pow(digitGroups.toDouble()))
                .toString() + " " + units[digitGroups]

        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return ""
    }

    fun getFormattedDate(dateVal: Long): String {
        try {
            var date = dateVal
            date *= 1000L
            return SimpleDateFormat("dd-MM-yyyy", Locale.US).format(Date(date))
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return ""
    }


}