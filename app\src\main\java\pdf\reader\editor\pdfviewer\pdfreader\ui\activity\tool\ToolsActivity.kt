package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.tool

import android.os.Bundle
import android.util.Log
import androidx.navigation.fragment.NavHostFragment
import com.artifex.mupdfdemo.ActionType
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseActivity
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivityToolsBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getHandledParcelableExtra
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview.PDFViewerViewModel

@AndroidEntryPoint
class ToolsActivity : BaseActivity<ActivityToolsBinding, PDFViewerViewModel>() {
    override fun getViewBinding() = ActivityToolsBinding.inflate(layoutInflater)

    override val viewModel: Class<PDFViewerViewModel>
        get() = PDFViewerViewModel::class.java


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val doc = intent.getHandledParcelableExtra("doc", DocumentsModel::class.java)

        Log.d("TAG", "onCreate: $doc")
        mViewModel.selectedDoc = doc
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.toolNavHostFragment) as NavHostFragment
        val inflater = navHostFragment.navController.navInflater
        val graph = inflater.inflate(R.navigation.tool_nav_graph)
        val destination = when (intent.getStringExtra("action")) {
            ActionType.SPLIT.name ->
                R.id.toolSplitFragment

            else ->
                R.id.toolMergeFragment
        }
        graph.setStartDestination(destination)
        val navController = navHostFragment.navController
        navController.setGraph(graph, intent.extras)
    }

}