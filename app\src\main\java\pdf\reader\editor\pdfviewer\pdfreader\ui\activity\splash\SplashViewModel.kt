package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.splash

import com.google.android.gms.ads.nativead.NativeAd
import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import javax.inject.Inject

@HiltViewModel
class SplashViewModel @Inject constructor(
    private val local: RepositoryLocal
) : BaseViewModel() {

    fun getRemoteValues() {
        local.getAllRemoteValues()
    }

    fun getNative() = local.getLanguageNativeAd()

    fun setNative(nativeAd: NativeAd) = local.setLanguageNative(nativeAd)
}