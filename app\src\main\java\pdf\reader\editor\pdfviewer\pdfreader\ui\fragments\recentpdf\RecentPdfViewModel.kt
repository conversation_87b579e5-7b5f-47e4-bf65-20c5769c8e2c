package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf

import android.view.View
import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.local.database.entity.PdfStoreItems
import javax.inject.Inject

@HiltViewModel
class RecentPdfViewModel @Inject constructor(
    private val repositoryLocal: RepositoryLocal
) : BaseViewModel() {
    var view: View? = null
    var isFirstTime = true
    fun getRecentFilesList(files: (ArrayList<PdfStoreItems>) -> Unit) =
        repositoryLocal.getRecentFilesList(files)

    fun getAllRecentFiles() = repositoryLocal.getAllRecentFiles()
}