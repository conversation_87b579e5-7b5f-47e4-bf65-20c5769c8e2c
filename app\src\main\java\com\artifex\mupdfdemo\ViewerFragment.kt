package com.artifex.mupdfdemo

import android.app.Activity
import android.app.Service
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.PopupWindow
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.toRect
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.artifex.mupdfdemo.MuPDFAlert.ButtonGroupType
import com.artifex.mupdfdemo.MuPDFAlert.ButtonPressed
import com.artifex.mupdfdemo.ReaderView.ViewMapper
import com.google.android.gms.ads.FullScreenContentCallback
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AnnotationDeleteLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentViewerBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.HorizontalThumbBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ThumbViewLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ViewerColorSheetLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.extensions.dateDifferenceInDays
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.invisible
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.keepScreenOn
import pdf.reader.editor.pdfviewer.pdfreader.extensions.openFile
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.extensions.setDeviceBrightness
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showCreateSheet
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.extensions.toPdfStoreItem
import pdf.reader.editor.pdfviewer.pdfreader.extensions.toString
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.manager.MyBottomSheet
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.shared.PDF_IMAGES
import pdf.reader.editor.pdfviewer.pdfreader.shared.PDF_PAGES_IMAGES
import pdf.reader.editor.pdfviewer.pdfreader.shared.Progress
import pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets.AnnotationColorSheet
import pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets.InkSettingFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.RatingsDialog
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.BottomSheetFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview.PDFViewerViewModel
import java.io.File
import java.util.concurrent.Executor
import kotlin.math.abs
import kotlin.math.max

enum class ActionType {
    MERGE, SPLIT, SORT, SHARE, PDF_EXPORT, IMAGES_EXPORT, ANNOTATION, INK, COMPRESSION, SCROLL, PRINT, READING_MODE, DARK_MODE, READING_SETTING, VIEW_MODE

}

class MyThreadExecutor : Executor {
    override fun execute(command: Runnable?) {
        Thread(command).start()
    }
}

@AndroidEntryPoint
class ViewerFragment : BaseFragment<FragmentViewerBinding, PDFViewerViewModel>(),
    MuPDFReaderViewListener {

    override val viewModel: Class<PDFViewerViewModel> get() = PDFViewerViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater, container: ViewGroup?
    ): FragmentViewerBinding = FragmentViewerBinding.inflate(inflater, container, false)

    private val args: ViewerFragmentArgs by navArgs()
    private var bottomSheetFragment: BottomSheetFragment? = null
    private var colorSheetFragment: AnnotationColorSheet? = null
    private var inkSettingSheetFragment: InkSettingFragment? = null
    private var fileUri: Uri? = null
    private var documentFile: File? = null
    private var core: MuPDFCore? = null
    private var dialog: AlertDialog? = null
    private var searchTask: SearchTask? = null
    private var pdfAdapter: MuPDFPageAdapter? = null
    private var windowManager: WindowManager? = null
    private var deleteAnnotBinding: AnnotationDeleteLayoutBinding? = null
    private var popupWindow: PopupWindow? = null
    private var isEdit = false
    private var isPDFEncrypted = false
    private var mAlertTask: AsyncTask<Void, Void, MuPDFAlert>? = null
    private var mAlertBuilder: AlertDialog.Builder? = null
    private var mAlertsActive = false
    private var mAlertDialog: AlertDialog? = null
    private var mContext: Context? = null
    private var readerView: MuPDFReaderView? = null
    private var actionType: ActionType? = null
    private var mPageSliderRes = 0
    private var verticalThumb: ThumbViewLayoutBinding? = null
    private var horizontalThumb: HorizontalThumbBinding? = null
    private var isReadingMode = false
    private var annotMode: Annotation.Type = Annotation.Type.UNKNOWN

//    private var isScroll = false
//    private var anim: TranslateAnimation? = null


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isAlive {
            mAlertBuilder = AlertDialog.Builder(it)
            windowManager = it.getSystemService(Service.WINDOW_SERVICE) as WindowManager
            deleteAnnotBinding = AnnotationDeleteLayoutBinding.inflate(
                LayoutInflater.from(it), null, false
            )
            verticalThumb =
                ThumbViewLayoutBinding.inflate(LayoutInflater.from(requireContext()), null, false)
            horizontalThumb =
                HorizontalThumbBinding.inflate(LayoutInflater.from(requireContext()), null, false)
            isEdit = args.isEdit
            if (sharedViewModel.selectedDoc != null) {
                mViewModel.documentModel = sharedViewModel.selectedDoc
                loadingDialog.show()
                Handler(activity?.mainLooper!!).postDelayed({

                    loadFile()
                }, 400)

//                loadPDFView()
            }
        }

//        loadFile()
    }

    private fun initViews() {
        if (core != null) {
            isAlive { context ->
                mViewModel.loadAd(context, storage.isConsentDone) { bannerAd ->
                    if (mViewDataBinding.adLayout.childCount > 0) {
                        mViewDataBinding.adLayout.removeAllViews()
                    }
                    if (bannerAd.parent != null) {
                        (bannerAd.parent as ViewGroup).removeView(bannerAd)
                    }
                    mViewDataBinding.adLayout.addView(bannerAd)

                }
                core?.onSharedPreferenceChanged()
                readerView = MuPDFReaderView(context)
                mViewDataBinding.readerContainer.addView(readerView)
                readerView?.setListener(this@ViewerFragment)

                val colorType = when (storage.backgroundColor) {
                    ColorType.GREY.name -> {
                        readerView?.setBackgroundColor(
                            ResourcesCompat.getColor(
                                resources, R.color.grey_400, null
                            )
                        )
                        ColorType.GREY
                    }

                    ColorType.YELLOW.name -> {
                        readerView?.setBackgroundColor(
                            ResourcesCompat.getColor(
                                resources, R.color.yellow_100, null
                            )
                        )
                        ColorType.YELLOW
                    }

                    ColorType.BLACK.name -> {
                        readerView?.setBackgroundColor(
                            ResourcesCompat.getColor(
                                resources, R.color.black, null
                            )
                        )
                        ColorType.BLACK
                    }

                    else -> {
                        readerView?.setBackgroundColor(
                            ResourcesCompat.getColor(
                                resources, R.color.white, null
                            )
                        )
                        ColorType.WHITE
                    }
                }
                pdfAdapter = MuPDFPageAdapter(
                    mContext, core, colorType
                )

                val scrolling = storage.getScrollDirection()

                when {
                    scrolling -> {
                        activity?.requestedOrientation = Configuration.ORIENTATION_LANDSCAPE
//
//                        mViewDataBinding.scrollHandle.hide()
//                        mViewDataBinding.horizontalScroll.show()
                    }

                    else -> {
                        activity?.requestedOrientation = Configuration.ORIENTATION_PORTRAIT
//                        mViewDataBinding.scrollHandle.show()
//                        mViewDataBinding.horizontalScroll.hide()
                    }
                }

                readerView?.setHorizontalScrolling(scrolling)
                readerView?.setContinuousScrolling(true)
                readerView?.adapter = pdfAdapter
                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                readerView?.setLinksEnabled(true)
                searchTask = object : SearchTask(context, core) {
                    override fun onTextFound(result: SearchTaskResult) {
                        SearchTaskResult.set(result)

                        readerView?.displayedViewIndex = result.pageNumber
                        readerView?.resetupChildren()
                    }
                }
                this.documentFile = File(mViewModel.documentModel?.absolutePath!!)
                mViewDataBinding.txtPDFName.text = mViewModel.documentModel?.fileName

                if (!storage.isBrightnessAuto) {
                    val brightness = abs(storage.brightness)
                    setBrightness(brightness)
                }

                mViewDataBinding.imgCloseReadMode.setOnClickListener {
                    isAlive {
                        mViewDataBinding.imgCloseReadMode.hide()
                        isReadingMode = false
                        requireActivity().keepScreenOn(false)
                        hideOrShow()
                    }
                }

                mViewDataBinding.btnBack.setOnClickListener {
                    isAlive { activity ->
                        activity.keepScreenOn(false)
                        activity.setDeviceBrightness()
                        activity.onBackPressed()
                    }
                }

                mViewDataBinding.btnPrintPDF.setOnClickListener {
                    isAlive {
                        FileUtils.printPDF(
                            mViewModel.documentModel?.absolutePath!!,
                            mViewModel.documentModel?.password!!,
                            it
                        )
                    }
//                        try {
//                            if (core != null && core?.hasChanges() == true) {
//
//                                showSaveDialog { flag ->
//                                    if (flag) {
//                                        actionType = ActionType.PRINT
//                                        savePDF()
//                                    } else {
//                                        FileUtils.shareFile(
//                                            mViewModel.documentModel?.absolutePath!!, mContext!!
//                                        )
//                                    }
//                                }
//
//                            } else {
//                                FileUtils.printPDF(
//                                    mViewModel.documentModel?.absolutePath!!,
//                                    mViewModel.documentModel?.password!!,
//                                    it
//                                )
//                            }
//                        } catch (e: UnsatisfiedLinkError) {
//
//                        }


                }

                mViewModel.insertRecentFiles(
                    mViewModel.documentModel?.toPdfStoreItem(
                        getLocalDate(), getLocalTimeStamp()
                    )!!
                )

                mViewDataBinding.btnOptions.setOnClickListener {

                    bottomSheetFragment = BottomSheetFragment(
                        readerView?.adapter?.count!!,
                        mViewModel.documentModel!!,
                        pages = { page ->
                            readerView?.displayedViewIndex = page

//                            mViewDataBinding.pdfView.jumpTo(page)
                        }) {
                        when (it) {
                            ActionType.SPLIT -> {

                                bottomSheetFragment?.dismiss()

                                try {
                                    if (core != null && core?.hasChanges() == true) {
                                        showSaveDialog { flag ->
                                            if (flag) {
                                                actionType = ActionType.SPLIT
                                                savePDF()
                                            } else {
                                                isAlive {
                                                    val direc =
                                                        ViewerFragmentDirections.actionPdfViewFragmentToSplitFragment()
                                                    findNavController().navigate(direc)
                                                }

                                            }
                                        }
                                    } else {
                                        isAlive {
                                            val direc =
                                                ViewerFragmentDirections.actionPdfViewFragmentToSplitFragment()
                                            findNavController().navigate(direc)
                                        }

                                    }
                                } catch (e: UnsatisfiedLinkError) {
                                }


                            }

                            ActionType.MERGE -> {
                                bottomSheetFragment?.dismiss()
                                try {
                                    if (core != null && core?.hasChanges() == true) {
                                        showSaveDialog { flag ->
                                            if (flag) {
                                                actionType = ActionType.MERGE
                                                savePDF()
                                            } else {
                                                isAlive {
                                                    val direc =
                                                        ViewerFragmentDirections.actionPdfViewFragmentToMergeFragment()
                                                    findNavController().navigate(direc)
                                                }

                                            }
                                        }
                                    } else {
                                        isAlive {
                                            val direc =
                                                ViewerFragmentDirections.actionPdfViewFragmentToMergeFragment()
                                            findNavController().navigate(direc)
                                        }

                                    }
                                } catch (
                                    e: UnsatisfiedLinkError
                                ) {
                                }


                            }

                            ActionType.ANNOTATION -> {
                                bottomSheetFragment?.dismiss()
                                if (!isPDFEncrypted) enableAnnotation()
                                else activity?.showToast("Protected file can't be annotate")


                            }

                            ActionType.INK -> {
                                bottomSheetFragment?.dismiss()
                                if (!isPDFEncrypted) {
                                    mViewDataBinding.mainToolbar.hide()
                                    mViewDataBinding.fabEdit.hide()
//                                    mViewDataBinding.pdfView.hide()
//                                    mViewDataBinding.readerContainer.show()
//                                    readerView?.displayedViewIndex =
//                                        mViewDataBinding.pdfView.currentPage
                                    readerView?.setMode(MuPDFReaderView.Mode.Drawing)
                                    mViewDataBinding.applyToolbar.show()
                                    updateInkSetting()
                                } else {
                                    activity?.showToast("Protected file can't be annotate")
                                }


//                            mViewDataBinding.mainToolbar.hideWithAnimation(object :
//                                Animation.AnimationListener {
//                                override fun onAnimationStart(animation: Animation) {}
//                                override fun onAnimationRepeat(animation: Animation) {}
//                                override fun onAnimationEnd(animation: Animation) {
//                                    mViewDataBinding.mainToolbar.hide()
//                                    readerView?.setMode(MuPDFReaderView.Mode.Drawing)
//                                    mViewDataBinding.applyToolbar.show()
//                                }
//                            })
                            }

                            ActionType.PDF_EXPORT -> {
                                bottomSheetFragment?.dismiss()
                                try {
                                    if (core != null && core?.hasChanges() == true) {
                                        showSaveDialog { flag ->
                                            if (flag) {
                                                actionType = ActionType.PDF_EXPORT
                                                savePDF()
                                            } else {
                                                isAlive {

                                                    PdfToImages()
                                                }
                                            }
                                        }
                                    } else {
                                        isAlive {
                                            PdfToImages()
                                        }
                                    }
                                } catch (e: UnsatisfiedLinkError) {
                                }


                            }

                            ActionType.IMAGES_EXPORT -> {
                                bottomSheetFragment?.dismiss()
                                loadingDialog.show()
                                lifecycleScope.launch(Dispatchers.IO) {
                                    kotlin.runCatching {
                                        FileUtils.extractPDFImages(
                                            mViewModel.documentModel?.absolutePath!!,
                                            mViewModel.documentModel?.fileName!!,
                                            mViewModel.documentModel?.password!!
                                        ) { flag ->
                                            activity?.runOnUiThread {
                                                loadingDialog.dismiss()
                                                val root = Environment.getExternalStorageDirectory()
                                                    .toString()
                                                val myDir =
                                                    root + "$PDF_PAGES_IMAGES${mViewModel.documentModel?.fileName}/"
                                                activity?.showCreateSheet(myDir) { flag ->
                                                    if (flag) {
                                                        isAlive { con ->
                                                            con.openFile(myDir)
                                                        }
                                                    }
                                                }
                                                if (flag) activity?.showToast("Images are saved!")
                                                else activity?.showToast("No images found to export!")
                                            }
                                        }
                                    }.onFailure {
                                        loadingDialog.dismiss()
                                        activity?.showToast("No images found to export!")
                                    }
                                }

                            }

                            ActionType.COMPRESSION -> {
                                bottomSheetFragment?.dismiss()
                                if (activity?.checkInternetConnection() == true) {
                                    try {
                                        if (core != null && core?.hasChanges() == true) {
                                            showSaveDialog { flag ->
                                                if (flag) {
                                                    actionType = ActionType.COMPRESSION
                                                    savePDF()
                                                } else {
                                                    compressPDF()
                                                }
                                            }
                                        } else {
                                            compressPDF()
                                        }
                                    } catch (e: UnsatisfiedLinkError) {
                                    }
                                } else activity?.showToast("This feature required internet!")
                            }

                            ActionType.SCROLL -> {
                                val scroll = storage.getScrollDirection()
                                destroyCore()
                                if (scroll) {
                                    activity?.requestedOrientation =
                                        ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
//                                    mViewDataBinding.scrollHandle.hide()
//                                    mViewDataBinding.horizontalScroll.show()
                                } else {
                                    activity?.requestedOrientation =
                                        ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//                                    mViewDataBinding.scrollHandle.show()
//                                    mViewDataBinding.horizontalScroll.hide()
                                }

                                bottomSheetFragment?.dismiss()

//                                try {
//                                    if (core?.countPages()!! == 1) {
//                                        mViewDataBinding.scrollHandle.hide()
//                                        mViewDataBinding.horizontalScroll.hide()
//                                    }
//                                } catch (e: UnsatisfiedLinkError) {
//                                }
//                                bottomSheetFragment?.dismiss()
//                                readerView?.setHorizontalScrolling(scroll)
//                                readerView?.requestLayout()
                            }

                            ActionType.SHARE -> {
                                bottomSheetFragment?.dismiss()
                                isAlive {
                                    try {
                                        if (core != null && core?.hasChanges() == true) {

                                            showSaveDialog { flag ->
                                                if (flag) {
                                                    actionType = ActionType.SHARE
                                                    savePDF()
                                                } else {
                                                    FileUtils.shareFile(
                                                        mViewModel.documentModel?.absolutePath!!,
                                                        mContext!!
                                                    )
                                                }
                                            }

                                        } else {
                                            FileUtils.shareFile(documentFile?.absolutePath!!, it)
                                        }
                                    } catch (e: UnsatisfiedLinkError) {

                                    }

                                }
                            }

                            ActionType.READING_MODE -> {
                                isAlive {
                                    isReadingMode = true
                                    requireActivity().keepScreenOn(true)
                                    hideOrShow()
                                    mViewDataBinding.imgCloseReadMode.show()
                                    bottomSheetFragment?.dismiss()
                                }
                            }

                            ActionType.DARK_MODE -> {
                                isAlive {
                                    bottomSheetFragment?.dismiss()
                                    val data = !storage.pdfNightMode
                                    storage.pdfNightMode = data
                                    if (data) {
                                        storage.backgroundColor = ColorType.BLACK.name
                                    } else {
                                        storage.backgroundColor = ColorType.WHITE.name
                                    }
//                                    pdfAdapter?.setDarkMode(storage.pdfNightMode)
                                    destroyCore()
                                    loadingDialog.show()
                                    Handler(activity?.mainLooper!!).postDelayed({
                                        loadFile()
                                    }, 500)
//                                    readerView?.invalidate()

                                }
                            }

                            ActionType.READING_SETTING -> {
                                bottomSheetFragment?.dismiss()
                                val settingBinding = ViewerColorSheetLayoutBinding.inflate(
                                    LayoutInflater.from(requireActivity()), null, false
                                )
                                MyBottomSheet.getInstance(activity)
                                    ?.setContentView(settingBinding.root, false)?.showDialog()

                                settingBinding.apply {
                                    imgSheetClose.setOnClickListener {
                                        MyBottomSheet.getInstance(activity)?.dismissDialog()
                                    }

                                    brightnessSeekBar.max = 230
                                    brightnessSeekBar.progress = abs(storage.brightness)
                                    brightnessSeekBar.setOnSeekBarChangeListener(object :
                                        OnSeekBarChangeListener {
                                        override fun onProgressChanged(
                                            seekBar: SeekBar?, progress: Int, fromUser: Boolean
                                        ) {
                                            if (fromUser) setBrightness(progress + 25)
                                        }

                                        override fun onStartTrackingTouch(seekBar: SeekBar?) {
                                            val brightness = abs(storage.brightness)
                                            storage.brightness = brightness
                                            setBrightness(brightness)
                                            storage.isBrightnessAuto = false
                                            switchAutoBright.isChecked = false


                                        }

                                        override fun onStopTrackingTouch(seekBar: SeekBar?) {
                                            val bright: Int = seekBar?.progress!!
                                            storage.brightness = bright + 25
                                        }
                                    })

                                    switchAutoBright.isChecked = storage.isBrightnessAuto
                                    switchAlways.isChecked = storage.isScreenOnEnabled

                                    switchAutoBright.setOnCheckedChangeListener { _, isChecked ->
                                        if (isChecked) {
                                            storage.isBrightnessAuto = true
                                            activity?.setDeviceBrightness()
                                        } else {
                                            storage.isBrightnessAuto = false
                                            val brightness = abs(storage.brightness)
                                            setBrightness(brightness)
                                        }
                                    }

                                    switchAlways.setOnCheckedChangeListener { _, isChecked ->
                                        storage.isScreenOnEnabled = isChecked
                                        activity?.keepScreenOn(isChecked)
                                    }
                                }


                                /*  settingBinding.imgColorWhite.setOnClickListener {
                                      if (storage.backgroundColor != ColorType.WHITE.name) {
                                          MyBottomSheet.getInstance(activity)?.dismissDialog()
                                          storage.backgroundColor = ColorType.WHITE.name
                                          destroyCore()
                                          loadingDialog.show()
                                          Handler(activity?.mainLooper!!).postDelayed({
                                              loadFile()
                                          }, 500)
                                      }

                                  }

                                  settingBinding.imgColorGrey.setOnClickListener {
                                      if (storage.backgroundColor != ColorType.GREY.name) {
                                          MyBottomSheet.getInstance(activity)?.dismissDialog()
                                          storage.backgroundColor = ColorType.GREY.name
                                          destroyCore()
                                          loadingDialog.show()
                                          Handler(activity?.mainLooper!!).postDelayed({
                                              loadFile()
                                          }, 500)
                                      }
                                  }

                                  settingBinding.imgColorYellow.setOnClickListener {
                                      if (storage.backgroundColor != ColorType.YELLOW.name) {
                                          MyBottomSheet.getInstance(activity)?.dismissDialog()
                                          storage.backgroundColor = ColorType.YELLOW.name
                                          destroyCore()
                                          loadingDialog.show()
                                          Handler(activity?.mainLooper!!).postDelayed({
                                              loadFile()
                                          }, 500)
                                      }
                                  }

                                  settingBinding.imgColorBlack.setOnClickListener {
                                      if (storage.backgroundColor != ColorType.BLACK.name) {
                                          MyBottomSheet.getInstance(activity)?.dismissDialog()
                                          storage.backgroundColor = ColorType.BLACK.name
                                          destroyCore()
                                          loadingDialog.show()
                                          Handler(activity?.mainLooper!!).postDelayed({
                                              loadFile()
                                          }, 500)
                                      }
                                  }*/


                            }

                            else -> {
                                bottomSheetFragment?.dismiss()

                                try {
                                    if (core != null && core?.hasChanges() == true) {
                                        showSaveDialog { flag ->
                                            if (flag) {
                                                actionType = ActionType.SORT
                                                savePDF()
                                            } else {
                                                isAlive {
                                                    val direc =
                                                        ViewerFragmentDirections.actionPdfViewFragmentToSortPDFFragment()
                                                    findNavController().navigate(direc)
                                                }

                                            }
                                        }
                                    } else {
                                        isAlive {
                                            val direc =
                                                ViewerFragmentDirections.actionPdfViewFragmentToSortPDFFragment()
                                            findNavController().navigate(direc)
                                        }

                                    }
                                } catch (e: UnsatisfiedLinkError) {
                                }

                            }
                        }
                    }


                    bottomSheetFragment?.show(childFragmentManager, "BottomSheetDialog")

                }

                mViewDataBinding.imgEditHigh.setOnClickListener {
                    if (annotMode != Annotation.Type.HIGHLIGHT) updateAnnotViews(Annotation.Type.HIGHLIGHT)
                }

                mViewDataBinding.imgEditUnder.setOnClickListener {
                    if (annotMode != Annotation.Type.UNDERLINE) updateAnnotViews(Annotation.Type.UNDERLINE)
                }

                mViewDataBinding.imgEditStrik.setOnClickListener {
                    if (annotMode != Annotation.Type.STRIKEOUT) updateAnnotViews(
                        Annotation.Type.STRIKEOUT
                    )

                }

                mViewDataBinding.imgEditCopy.setOnClickListener {
                    val pageView = readerView?.displayedView as MuPDFView?
                    val result = pageView?.copySelection()
                    if (result == true) activity?.showToast("Text copied!")
                }

                mViewDataBinding.imgEditDone.setOnClickListener {
                    readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                    val pageView = readerView?.displayedView as MuPDFView?
                    pageView?.cancelDraw()
                    mViewDataBinding.applyToolbar.hide()
                    disableAnnotation()

                }

                mViewDataBinding.imgEditInk.setOnClickListener {
                    if (!isPDFEncrypted) {
                        if (annotMode != Annotation.Type.INK) updateAnnotViews(Annotation.Type.INK)

                    } else {
                        activity?.showToast("Protected file can't be annotate")
                    }
                }

                mViewDataBinding.btnSearch.setOnClickListener {
                    enableSearch()
                }

                mViewDataBinding.searchBack.setOnClickListener {

                    disableSearch()
                }

                mViewDataBinding.edtSearch.addTextChangedListener(object : TextWatcher {
                    override fun beforeTextChanged(
                        s: CharSequence?, start: Int, count: Int, after: Int
                    ) {

                    }

                    override fun onTextChanged(
                        s: CharSequence?, start: Int, before: Int, count: Int
                    ) {
                        if (s != null && s.isNotEmpty()) {
                            mViewDataBinding.imgClearText.show()
                        } else {
                            mViewDataBinding.imgClearText.invisible()
                        }

                    }

                    override fun afterTextChanged(s: Editable?) {

                    }
                })

                mViewDataBinding.edtSearch.setOnEditorActionListener { _, i, _ ->
                    if (i == EditorInfo.IME_ACTION_SEARCH) {

                        val text = mViewDataBinding.edtSearch.text
                        mViewDataBinding.edtSearch.hideKeyboard()
                        if (text?.isNotEmpty() == true) {

                            search(0, text.toString())
                            true
                        } else false


                    } else false


                }

                mViewDataBinding.imgClearText.setOnClickListener {
                    mViewDataBinding.edtSearch.setText("")
                    SearchTaskResult.set(null)
                    readerView?.resetupChildren()
                }

                mViewDataBinding.btnApplyBack.setOnClickListener {
                    when {
                        mViewDataBinding.annotationLayout.isVisible -> {

                            readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                            val pageView = readerView?.displayedView as MuPDFView?
                            pageView?.deselectText()
                            pageView?.cancelDraw()
                            mViewDataBinding.applyToolbar.hide()
                            disableAnnotation()
                        }

                        else -> {
                            mViewDataBinding.mainToolbar.show()
                            mViewDataBinding.fabEdit.show()
                            mViewDataBinding.applyToolbar.hide()
                            readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                            val pageView = readerView?.displayedView as MuPDFView?
                            pageView?.deselectText()
                            pageView?.cancelDraw()
                        }
                    }


//                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation) {
//                        mViewDataBinding.mainToolbar.show()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation) {}
//                    override fun onAnimationEnd(animation: Animation) {
//                        mViewDataBinding.applyToolbar.hide()
//                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                        val pageView = readerView?.displayedView as MuPDFView
//                        pageView.deselectText()
//                        pageView.cancelDraw()
//                    }
//                })
                }

                mViewDataBinding.btnApply.setOnClickListener {

                    when {
                        mViewDataBinding.annotationLayout.isVisible -> {
                            val pageView = readerView?.displayedView as MuPDFView?
                            pageView?.saveDraw()
                        }

                        else -> {
                            mViewDataBinding.mainToolbar.show()
                            mViewDataBinding.fabEdit.show()
                            mViewDataBinding.applyToolbar.hide()
                            val pageView = readerView?.displayedView as MuPDFView?
                            pageView?.saveDraw()
                            readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                        }
                    }

                }

                mViewDataBinding.fabEdit.setOnClickListener {
                    enableAnnotation()
                }

                mViewDataBinding.btnAnnotBack.setOnClickListener {
                    disableAnnotation()
                }

                mViewDataBinding.btnAnnotDone.setOnClickListener {

                    addAnnotation(annotMode)
                }

                mViewDataBinding.imgAnnotColor.setOnClickListener {
                    when (annotMode) {
                        Annotation.Type.HIGHLIGHT -> {
                            colorSheetFragment = AnnotationColorSheet.getInstance(
                                Annotation.Type.HIGHLIGHT, storage.highlightColor
                            ) {
                                colorSheetFragment?.dismiss()
                                storage.highlightColor = it
                                mViewDataBinding.imgAnnotColor.setColorFilter(
                                    ColorPalette.getHex(
                                        it, Annotation.Type.HIGHLIGHT
                                    )
                                )
                                core?.setHighlightColor()

                                addAnnotation(annotMode)
                            }



                            colorSheetFragment?.show(childFragmentManager, "Color Fragment")
                        }

                        Annotation.Type.UNDERLINE -> {
                            colorSheetFragment = AnnotationColorSheet.getInstance(
                                Annotation.Type.UNDERLINE, storage.underlineColor
                            ) {
                                colorSheetFragment?.dismiss()
                                storage.underlineColor = it
//                                if (it == 0) {
//                                    mViewDataBinding.imgAnnotColor.setColorFilter(
//                                        Color.argb(
//                                            1,
//                                            0, 0, 1
//                                        )
//                                    )
//                                } else {
                                mViewDataBinding.imgAnnotColor.setColorFilter(
                                    ColorPalette.getHex(
                                        it, Annotation.Type.UNDERLINE
                                    )
                                )

//                                }
                                core?.setUnderlineColor()
                                addAnnotation(annotMode)
                            }
                            colorSheetFragment?.show(childFragmentManager, "Color Fragment")
                        }

                        Annotation.Type.STRIKEOUT -> {
                            colorSheetFragment = AnnotationColorSheet.getInstance(
                                Annotation.Type.STRIKEOUT, storage.strikeoutColor
                            ) {
                                colorSheetFragment?.dismiss()
                                storage.strikeoutColor = it
//                                if (it == 0) {
//                                    mViewDataBinding.imgAnnotColor.setColorFilter(
//                                        Color.argb(
//                                            1,
//                                            1, 0, 0
//                                        )
//                                    )
//                                } else {
                                mViewDataBinding.imgAnnotColor.setColorFilter(
                                    ColorPalette.getHex(
                                        it, Annotation.Type.STRIKEOUT
                                    )
                                )

//                                }
                                core?.setStrikeoutColor()
                                addAnnotation(annotMode)
                            }
                            colorSheetFragment?.show(childFragmentManager, "Color Fragment")
                        }

                        else -> {}
                    }
                }

                mViewDataBinding.imgInkColor.setOnClickListener {


                    inkSettingSheetFragment = InkSettingFragment.getInstance(storage.inkThickness,
                        storage.inkColor,
                        thick = {
                            storage.inkThickness = it
                            core?.setInkThickness()
                            updateInkSetting()
                            inkSettingSheetFragment?.dismiss()
                        }) {
                        inkSettingSheetFragment?.dismiss()
                        storage.inkColor = it
                        updateInkSetting()
                    }

                    inkSettingSheetFragment?.show(childFragmentManager, "Ink Color Fragment")

                }

                mViewDataBinding.btnSavePDF.setOnClickListener {
                    try {
                        if (core != null && core?.hasChanges() == true) {

                            val alert = mAlertBuilder?.create()
                            alert?.setTitle("Save Changing?")
                            alert?.cancel()
                            alert?.setMessage(getString(R.string.document_has_changes_save_them_))
                            alert?.setButton(
                                AlertDialog.BUTTON_POSITIVE,
                                getString(R.string.yes),

                                ) { _, _ ->
                                alert?.dismiss()
                                saveAsPDF()
                            }
                            alert?.setButton(
                                AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
                            ) { _, _ ->
                                isAlive {
                                    alert.dismiss()

                                }


                            }
                            alert?.show()
                        }
                    } catch (e: UnsatisfiedLinkError) {
                    }
                }


                if (FileUtils.isPDFEncrypted(mViewModel.documentModel?.absolutePath!!)) {
                    isPDFEncrypted = true
                    mViewDataBinding.fabEdit.hide()
                }

                activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner,
                    object : OnBackPressedCallback(true) {
                        override fun handleOnBackPressed() {
                            loadingDialog.dismiss()
                            when {
                                isReadingMode -> {
                                    isAlive {
                                        mViewDataBinding.imgCloseReadMode.hide()
                                        isReadingMode = false
                                        requireActivity().keepScreenOn(false)
                                        hideOrShow()

                                    }
                                }

                                mViewDataBinding.searchToolbar.isVisible -> {
                                    disableSearch()
                                }

                                mViewDataBinding.annotationLayout.isVisible -> {
                                    readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                                    val pageView = readerView?.displayedView as MuPDFView?
                                    pageView?.deselectText()
                                    pageView?.cancelDraw()
                                    mViewDataBinding.applyToolbar.hide()
                                    disableAnnotation()
                                }

                                mViewDataBinding.applyToolbar.isVisible -> {
                                    mViewDataBinding.mainToolbar.show()
                                    mViewDataBinding.applyToolbar.hide()
                                    readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                                    val pageView = readerView?.displayedView as MuPDFView?
                                    pageView?.deselectText()
                                    pageView?.cancelDraw()

//                                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                                    Animation.AnimationListener {
//                                    override fun onAnimationStart(animation: Animation) {
//                                        mViewDataBinding.mainToolbar.show()
//                                    }
//
//                                    override fun onAnimationRepeat(animation: Animation) {}
//                                    override fun onAnimationEnd(animation: Animation) {
//                                        mViewDataBinding.applyToolbar.hide()
//                                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                                        val pageView =
//                                            readerView?.displayedView as MuPDFView
//                                        pageView.deselectText()
//                                        pageView.cancelDraw()
//                                    }
//                                })


                                }

                                else -> {
                                    isAlive { activity ->
                                        activity.keepScreenOn(false)
                                        activity.setDeviceBrightness()
                                    }
                                    try {
                                        if (core != null && core?.hasChanges() == true) {

                                            val alert = mAlertBuilder?.create()
                                            alert?.setTitle("Save Changing?")
                                            alert?.cancel()
                                            alert?.setMessage(getString(R.string.document_has_changes_save_them_))
                                            alert?.setButton(
                                                AlertDialog.BUTTON_POSITIVE,
                                                getString(R.string.yes),

                                                ) { _, _ ->
                                                alert?.dismiss()
                                                savePDF()
                                            }
                                            alert?.setButton(
                                                AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
                                            ) { _, _ ->
//                                        FileUtils.deleteFile(
//                                            documentFile?.absolutePath!!,
//                                            requireActivity()
//                                        ) {
//
//                                        }
                                                isAlive {
                                                    alert?.dismiss()
                                                    showAd()
                                                }


                                            }
                                            alert?.show()
                                        } else {

                                            when {
                                                mViewModel.interstitialAd != null -> {
                                                    sharedViewModel.selectedDoc = null
                                                    showAd()
                                                }

                                                !storage.isUserGaveRating() && activity?.dateDifferenceInDays(
                                                    storage.getRatingDate()
                                                )!! >= 1L -> {
                                                    storage.setRatingDate()
                                                    RatingsDialog.getInstance(false) {
                                                        isAlive {
                                                            sharedViewModel.selectedDoc = null
                                                            findNavController().popBackStack()
                                                        }
                                                    }.show(childFragmentManager, "Rating Dialog")

                                                }

                                                else -> {
                                                    isAlive {
                                                        sharedViewModel.selectedDoc = null
                                                        showAd()
                                                    }
                                                }
                                            }
                                        }
                                    } catch (e: UnsatisfiedLinkError) {
                                    }
//                                else {
//                                    if (!shareSave) {
//                                        FileUtils.deleteFile(
//                                            documentFile?.absolutePath!!,
//                                            requireActivity()
//                                        ) {}
//                                        findNavController().popBackStack()
//                                    } else {
//                                        findNavController().popBackStack()
//                                    }
//
//                                }
                                }
                            }
                        }
                    })

                if (isEdit) {
                    if (!isPDFEncrypted) enableAnnotation()
                    else {
                        mViewDataBinding.fabEdit.hide()
                    }
                }

                activity?.keepScreenOn(storage.isScreenOnEnabled)


                // Set up the page slider

                try {
                    val smax = max(core?.countPages()!! - 1, 1)
                    mPageSliderRes = (10 + smax - 1) / smax * 2
                    mViewDataBinding.mySeekBar.max = (core?.countPages()!! - 1) * mPageSliderRes
                    mViewDataBinding.horizontalScroll.max =
                        (core?.countPages()!! - 1) * mPageSliderRes

                } catch (e: UnsatisfiedLinkError) {
                }


                mViewDataBinding.mySeekBar.setOnSeekBarChangeListener(object :
                    OnSeekBarChangeListener {
                    override fun onStopTrackingTouch(seekBar: SeekBar) {
                        readerView?.displayedViewIndex =
                            (seekBar.progress + mPageSliderRes / 2) / mPageSliderRes
                        mViewDataBinding.txtAnnotationNot.hide()
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {
                        mViewDataBinding.txtAnnotationNot.show()
                    }

                    override fun onProgressChanged(
                        seekBar: SeekBar, progress: Int, fromUser: Boolean
                    ) {
                        val index = (progress + mPageSliderRes / 2) / mPageSliderRes
                        updatePageNumView(index)
                        seekBar.thumb = getVerticalThumb(index + 1)

                    }
                })

                mViewDataBinding.horizontalScroll.setOnSeekBarChangeListener(object :
                    OnSeekBarChangeListener {
                    override fun onStopTrackingTouch(seekBar: SeekBar) {
                        readerView?.displayedViewIndex =
                            (seekBar.progress + mPageSliderRes / 2) / mPageSliderRes
                        mViewDataBinding.txtAnnotationNot.hide()
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {
                        mViewDataBinding.txtAnnotationNot.show()
                    }

                    override fun onProgressChanged(
                        seekBar: SeekBar, progress: Int, fromUser: Boolean
                    ) {
                        val index = (progress + mPageSliderRes / 2) / mPageSliderRes
                        updatePageNumView(index)
                        seekBar.thumb = getHorizontalThumb(index + 1)

                    }
                })

                // Update page number text and slider
                val index: Int = readerView?.displayedViewIndex!!
                mViewDataBinding.mySeekBar.progress = index * mPageSliderRes
                mViewDataBinding.horizontalScroll.progress = index * mPageSliderRes
                mViewDataBinding.horizontalScroll.thumb = getHorizontalThumb(index + 1)
                updatePageNumView(index)
                try {

                    if (core != null && core?.countPages() == 1) {
                        mViewDataBinding.scrollHandle.hide()
                        mViewDataBinding.horizontalScroll.hide()
                    }
                } catch (e: UnsatisfiedLinkError) {
                }
                loadingDialog.dismiss()

            }
        }
    }

    private fun loadFile() {

//        windowManager = requireActivity().getSystemService(Service.WINDOW_SERVICE) as WindowManager
//        deleteAnnotBinding = AnnotationDeleteLayoutBinding.inflate(
//            LayoutInflater.from(requireContext()),
//            null,
//            false
//        )
//        mViewModel.documentModel = args.pdfFile
//        isEdit = args.isEdit

        val buffer: ByteArray?
//        fileUri =
//            FileUtils.getUriFromPath(requireContext(), mViewModel.documentModel?.absolutePath!!)

        if (fileUri != null && fileUri.toString().startsWith("content://")) {
            var reason: String? = null
            try {
                val `is` = activity?.contentResolver?.openInputStream(fileUri!!)
                val len = `is`?.available()
                buffer = ByteArray(len!!)
                `is`.read(buffer, 0, len)
                `is`.close()
            } catch (e: OutOfMemoryError) {
                reason = e.toString()
            } catch (e: Exception) {
                try {
                    val cursor = activity?.contentResolver?.query(
                        fileUri!!, arrayOf("_data"), null, null, null
                    )
                    if (cursor!!.moveToFirst()) {
                        val str = cursor.getString(0)
                        if (str == null) {
                            reason = "Couldn't parse data in intent"
                        } else {
                            fileUri = Uri.parse(str)
                        }
                    }
                    cursor.close()
                } catch (e2: Exception) {
                    reason = e2.toString()
                }
            }
//            if (reason != null) {
//                showErrorDialog(
//                    String.format(
//                        getString(R.string.cannot_open_document_Reason),
//                        reason
//                    )
//                )
//                return
//            }

//            core = if (buffer != null) {
//                openBuffer(buffer)
//            } else {
//                openFile(Uri.decode(fileUri?.encodedPath))
//            }
//            if (mViewModel.core == null) {
//                mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
//                core = mViewModel.core
//            } else {

//            mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
            try {
                core = openFile(mViewModel.documentModel?.absolutePath!!)
            } catch (e: UnsatisfiedLinkError) {
            }

//            }


            SearchTaskResult.set(null)

            try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    val result = core?.authenticatePassword(mViewModel.documentModel?.password!!)
                    if (result == true) {
                        initViews()
                    }
//                requestPassword()
                } else {
                    initViews()
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }


            try {
                if (core == null) showErrorDialog(getString(R.string.cannot_open_document))
            } catch (e: UnsatisfiedLinkError) {
            }
        } else {
//            if (mViewModel.core == null) {
//                mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
//                core = mViewModel.core
//            } else {
//            lifecycleScope
//                .launch(Dispatchers.Default) {

//            mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
            try {
                core = openFile(mViewModel.documentModel?.absolutePath!!)
            } catch (e: UnsatisfiedLinkError) {
                loadingDialog.dismiss()
            }

            SearchTaskResult.set(null)

            /*try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    val result =
                        core?.authenticatePassword(mViewModel.documentModel?.password!!)
                    if (result == true) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            initViews()
                        }
                    }

//                requestPassword()
                } else {
                    lifecycleScope.launch(Dispatchers.Main) {
                        initViews()
                    }
                }
            } catch (e: UnsatisfiedLinkError) {
            }*/

            /*try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core == null)
                    lifecycleScope.launch(Dispatchers.Main) {
                        loadingDialog?.dismiss()
                        showErrorDialog(getString(R.string.cannot_open_document))
                    }
            } catch (e: UnsatisfiedLinkError) {
            }*/
//                }

//            }


            try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    loadingDialog.dismiss()
                    val result = core?.authenticatePassword(mViewModel.documentModel?.password!!)
                    if (result == true) {
                        initViews()
                    } else requestPassword()
                } else {

                    initViews()
                }
            } catch (e: UnsatisfiedLinkError) {
            }


            try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core == null) {
                    loadingDialog.dismiss()
                    showErrorDialog(getString(R.string.cannot_open_document))
                }
            } catch (e: UnsatisfiedLinkError) {
            }
        }


    }

    private fun compressPDF() {
        loadingDialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            mViewModel.compressFile(File(
                mViewModel.documentModel?.absolutePath!!
            ), type = FileType.COMPRESS, progress = object : Progress {
                override fun progress(progress: Int) {

                }

                override fun fail() {

                    activity?.runOnUiThread {
                        loadingDialog.dismiss()
                        activity?.showToast("Failed to compress")
                    }
                }

                override fun currentStatus(status: String) {

                }

                override fun downloadingSuccess(file: File) {

                    activity?.runOnUiThread {
                        loadingDialog.dismiss()
                        activity?.showToast("Compress successfully")
                        findNavController().popBackStack()
                    }
                }
            })

        }
    }

    private fun updateInkSetting() {
        val color = storage.inkColor
        val thickness = storage.inkThickness
        mViewDataBinding.imgInkColor.setColorFilter(
            ColorPalette.getHex(
                color, Annotation.Type.INK
            )
        )
        core?.setInkColor()
        val pageView = readerView?.displayedView as MuPDFView?
        pageView?.setInkSetting(color, thickness * 2)
    }
//    private fun openBuffer(buffer: ByteArray): MuPDFCore? {
//        try {
//            core = MuPDFCore(mContext, buffer, null)
//            OutlineActivityData.set(null)
//        } catch (e: java.lang.Exception) {
//            return null
//        }
//        return core
//    }

    private fun requestPassword() {
        if (mViewModel.documentModel?.password?.isNotEmpty() == true) {
            val result = core?.authenticatePassword(mViewModel.documentModel?.password!!)
            if (result == false) {
                mViewModel.documentModel?.password = ""
                requestPassword()
            } else initViews()
        } else {
            DialogPdfPassword.getInstance { action, password ->
                when (action) {
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                        mViewModel.documentModel?.password = password
                        val result =
                            core?.authenticatePassword(mViewModel.documentModel?.password!!)
                        if (result == false) {
                            activity?.showToast(getString(R.string.txt_pdf_wrong_password))
                            requestPassword()
                        } else initViews()
                    }

                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                        activity?.showToast(getString(R.string.txt_pdf_null_password_warning))
                    }

                    ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
                        isAlive {

                            findNavController().popBackStack()
                        }

                    }

                    else -> {}
                }
            }.show(childFragmentManager, "Password Dialog")
        }

    }

    private fun openFile(path: String): MuPDFCore? {

        try {

            core = MuPDFCore(mContext, path)
            OutlineActivityData.set(null)
        } catch (e: UnsatisfiedLinkError) {
            requireActivity().sendFirebaseLog(
                "unsatisfied_link_error", "This error because of library issue"
            )
            return null
        } catch (e: java.lang.Exception) {
            return null
        }
        return core
    }

    override fun onTapMainDocArea() {
//        hideOrShow()
    }

    override fun onDocMotion() {
    }

    override fun onHit(item: Hit?) {
        when (item) {
            Hit.Annotation -> {
                val pageView = readerView?.displayedView as MuPDFView?
                val selectedAnnot = pageView?.selectedAnnotation
                if (selectedAnnot != null) {
                    val rect = selectedAnnot.toRect()
                    val x =
                        ((windowManager?.defaultDisplay?.width!! / 2) - ((rect.right + rect.left) / 2))
                    val y =
                        -(((windowManager?.defaultDisplay?.height!! / 2) - (((rect.bottom + rect.top) - rect.height()) / 2)))

                    showDeleteAnnotWindow(x, y)
                }
            }

            Hit.Nothing -> {
                if (!isReadingMode) hideOrShow()
            }

            else -> {

            }
        }
    }

    override fun onMoveToChild(i: Int) {
//        mViewDataBinding.pdfView.jumpTo(i)
        updatePageNumView(i)
        mViewDataBinding.mySeekBar.progress = i * mPageSliderRes
        mViewDataBinding.horizontalScroll.progress = i * mPageSliderRes
    }

    private fun hideOrShow() {
        when {
            mViewDataBinding.topLayout.isVisible -> {
                mViewDataBinding.topLayout.hide()

                mViewDataBinding.fabEdit.hide()
                val scrollMode = storage.getScrollDirection()

                if (scrollMode) {
//                    mViewDataBinding.scrollHandle.hide()
                    mViewDataBinding.horizontalScroll.hide()
                } else {

                    mViewDataBinding.scrollHandle.hide()
//                    mViewDataBinding.horizontalScroll.hide()
                }
//                mViewDataBinding.mainToolbar.hideWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation?) {
//
//                    }
//
//                    override fun onAnimationEnd(animation: Animation?) {
//                        mViewDataBinding.mainToolbar.hide()
//                        mViewDataBinding.fabEdit.hide()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation?) {
//
//                    }
//                })
            }

            else -> {
                if (!mViewDataBinding.applyToolbar.isVisible && !mViewDataBinding.searchToolbar.isVisible && !mViewDataBinding.annotationLayout.isVisible) {
                    mViewDataBinding.topLayout.show()
                    mViewDataBinding.fabEdit.hide()
                    val scrollMode = storage.getScrollDirection()

                    if (scrollMode) mViewDataBinding.horizontalScroll.show()
                    else mViewDataBinding.scrollHandle.show()

                    if (!isPDFEncrypted) mViewDataBinding.fabEdit.show()
                }

//                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation?) {
//                        mViewDataBinding.mainToolbar.show()
//
//                    }
//
//                    override fun onAnimationEnd(animation: Animation?) {
//                        mViewDataBinding.fabEdit.show()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation?) {
//
//                    }
//                })
            }
        }
    }

    private fun search(direction: Int, text: String) {
        val displayPage = readerView?.displayedViewIndex
        val r = SearchTaskResult.get()
        val searchPage = r?.pageNumber ?: -1
        try {
            searchTask?.go(text, direction, displayPage!!, searchPage)
        } catch (e: UnsatisfiedLinkError) {
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun showErrorDialog(title: String, message: String = "") {
        isAlive {
            val builder = AlertDialog.Builder(it)
            builder.setTitle(title)
            builder.setMessage(message)
            builder.setPositiveButton("Dismiss") { _, _ ->

                dialog?.dismiss()
                isAlive {

                    findNavController().popBackStack()
                }
            }

            dialog = builder.create()
            dialog?.show()
        }


    }

    override fun onDestroy() {

        try {
            destroyCore()
        } catch (e: UnsatisfiedLinkError) {
        }
//        mViewModel.core?.onDestroy()
//        mViewModel.core = null


        super.onDestroy()
    }

    private fun addAnnotation(type: Annotation.Type) {
        if (type != Annotation.Type.UNKNOWN) {

            val pageView = readerView?.displayedView as MuPDFView?
            if (pageView?.selectedText != null) {
                pageView.markupSelection(type)
            }
        }

    }

    private fun showDeleteAnnotWindow(x: Int = 0, y: Int = 0) {

        popupWindow = PopupWindow(
            deleteAnnotBinding?.root,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow?.elevation = 20f
        deleteAnnotBinding?.root?.setOnClickListener {
            val pageView = readerView?.displayedView as MuPDFView?
            pageView?.deleteSelectedAnnotation()
            popupWindow?.dismiss()
        }

        popupWindow?.setOnDismissListener {
            val pageView = readerView?.displayedView as MuPDFView?

            pageView?.deselectAnnotation()
        }

        if (popupWindow?.isShowing == true) {
            popupWindow?.dismiss()
        }
        popupWindow?.showAtLocation(view, Gravity.CENTER, x, y)
    }

    private fun enableAnnotation() {
        mViewDataBinding.mainToolbar.hide()
        mViewDataBinding.fabEdit.hide()
        readerView?.setMode(MuPDFReaderView.Mode.Selecting)
        mViewDataBinding.annotationLayout.show()
        mViewDataBinding.annotToolbar.show()
        if (!storage.isFirstTime()) {
            mViewDataBinding.selectionAnim.show()
            mViewDataBinding.view.show()
            mViewDataBinding.selectionAnim.playAnimation()
            Handler(activity?.mainLooper!!).postDelayed({
                mViewDataBinding.selectionAnim.hide()
                mViewDataBinding.view.hide()
            }, 2500)
            storage.setFirstTime(true)
        }
        updateAnnotViews(annotMode)
        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            else -> {}
        }

//        mViewDataBinding.mainToolbar.hideWithAnimation(object :
//            Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {}
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.mainToolbar.hide()
//                mViewDataBinding.fabEdit.hide()
//                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
//                mViewDataBinding.annotationLayout.show()
//                mViewDataBinding.selectionAnim.show()
//                mViewDataBinding.view.show()
//                mViewDataBinding.selectionAnim.playAnimation()
//                Handler(requireActivity().mainLooper).postDelayed({
//                    mViewDataBinding.selectionAnim.hide()
//                    mViewDataBinding.view.hide()
//                }, 2500)
//            }
//        })
    }

    private fun disableAnnotation() {
        mViewDataBinding.mainToolbar.show()
        mViewDataBinding.fabEdit.show()
        val pageView = readerView?.displayedView as MuPDFView?
        pageView?.deselectText()
        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
        mViewDataBinding.annotationLayout.hide()
        mViewDataBinding.annotToolbar.hide()
        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            else -> {}
        }

//        mViewDataBinding.mainToolbar.showWithAnimation(object : Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {
//                mViewDataBinding.mainToolbar.show()
//                mViewDataBinding.fabEdit.show()
//            }
//
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                val pageView = readerView?.displayedView as MuPDFView
//                pageView.deselectText()
//                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                showToast("onAnimationEnd")
//                mViewDataBinding.annotationLayout.hide()
//            }
//        })
    }

    private fun enableSearch() {
        mViewDataBinding.mainToolbar.hide()
        mViewDataBinding.fabEdit.hide()
        mViewDataBinding.searchToolbar.show()
        mViewDataBinding.edtSearch.showKeyboard()
        mViewDataBinding.edtSearch.setText("")
        mViewDataBinding.edtSearch.requestFocus()
        mViewDataBinding.edtSearch.showKeyboard()
//        mViewDataBinding.mainToolbar.hideWithAnimation(object :
//            Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {}
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.mainToolbar.hide()
//                mViewDataBinding.searchToolbar.show()
//                mViewDataBinding.edtSearch.showKeyboard()
//                mViewDataBinding.edtSearch.setText("")
//                mViewDataBinding.edtSearch.requestFocus()
//
//            }
//        })
    }

    private fun disableSearch() {
        mViewDataBinding.mainToolbar.show()
        if (!isPDFEncrypted) mViewDataBinding.fabEdit.show()
        mViewDataBinding.searchToolbar.hide()
        mViewDataBinding.edtSearch.hideKeyboard()
        SearchTaskResult.set(null)
        readerView?.resetupChildren()

//        mViewDataBinding.mainToolbar.showWithAnimation(object : Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {
//                mViewDataBinding.mainToolbar.show()
//            }
//
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.searchToolbar.hide()
//                mViewDataBinding.edtSearch.hideKeyboard()
//                SearchTaskResult.set(null)
//                readerView?.resetupChildren()
//            }
//        })
    }

    override fun onStart() {
        try {
            if (core != null) {
                core?.startAlerts()
                createAlertWaiter()
            }
        } catch (e: UnsatisfiedLinkError) {

        }

        super.onStart()
    }

    override fun onStop() {
        try {
            if (core != null) {
                destroyAlertWaiter()
                core?.stopAlerts()
            }
        } catch (e: UnsatisfiedLinkError) {
        }
        super.onStop()
    }

    override fun onPause() {
        super.onPause()
        try {
            if (searchTask != null) searchTask?.stop()
        } catch (e: UnsatisfiedLinkError) {
        }
    }

    fun createAlertWaiter() {
        mAlertsActive = true
        if (mAlertTask != null) {
            mAlertTask?.cancel(true)
            mAlertTask = null
        }
        if (mAlertDialog != null) {
            mAlertDialog?.cancel()
            mAlertDialog = null
        }

        mAlertTask = object : AsyncTask<Void, Void, MuPDFAlert>() {
            override fun doInBackground(vararg params: Void?): MuPDFAlert? {
                return try {
                    if (!mAlertsActive) null else core?.waitForAlert()
                } catch (e: UnsatisfiedLinkError) {
                    null
                }

            }

            override fun onPostExecute(result: MuPDFAlert?) {
                super.onPostExecute(result)
                if (result == null) return
                val pressed = arrayOfNulls<ButtonPressed>(3)
                for (i in 0..2) pressed[i] = ButtonPressed.None
                val listener = DialogInterface.OnClickListener { _, which ->
                    mAlertDialog = null
                    if (mAlertsActive) {
                        var index = 0
                        when (which) {
                            AlertDialog.BUTTON1 -> index = 0
                            AlertDialog.BUTTON2 -> index = 1
                            AlertDialog.BUTTON3 -> index = 2
                        }
                        result.buttonPressed = pressed[index]
                        // Send the user's response to the core, so that it can
                        // continue processing.
                        try {
                            core?.replyToAlert(result)
                            createAlertWaiter()
                        } catch (e: UnsatisfiedLinkError) {
                        }

                        // Create another alert-waiter to pick up the next alert.

                    }
                }
                mAlertDialog = mAlertBuilder?.create()
                mAlertDialog?.setTitle(result.title)
                mAlertDialog?.setMessage(result.message)
                when (result.iconType) {
                    MuPDFAlert.IconType.Error -> {}
                    MuPDFAlert.IconType.Warning -> {}
                    MuPDFAlert.IconType.Question -> {}
                    MuPDFAlert.IconType.Status -> {}
                }
                when (result.buttonGroupType) {
                    ButtonGroupType.OkCancel -> {
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON2, getString(R.string.cancel), listener
                        )
                        pressed[1] = ButtonPressed.Cancel
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON1, getString(R.string.okay), listener
                        )
                        pressed[0] = ButtonPressed.Ok
                    }

                    ButtonGroupType.Ok -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.okay), listener
                        )
                        pressed[0] = ButtonPressed.Ok
                    }

                    ButtonGroupType.YesNoCancel -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON3, getString(R.string.cancel), listener
                        )
                        pressed[2] = ButtonPressed.Cancel
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.yes), listener
                        )
                        pressed[0] = ButtonPressed.Yes
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON2, getString(R.string.no), listener
                        )
                        pressed[1] = ButtonPressed.No
                    }

                    ButtonGroupType.YesNo -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.yes), listener
                        )
                        pressed[0] = ButtonPressed.Yes
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON2, getString(R.string.no), listener
                        )
                        pressed[1] = ButtonPressed.No
                    }
                }
                mAlertDialog?.setOnCancelListener {
                    mAlertDialog = null
                    if (mAlertsActive) {
                        result.buttonPressed = ButtonPressed.None
                        try {
                            core?.replyToAlert(result)
                            createAlertWaiter()
                        } catch (e: UnsatisfiedLinkError) {
                        }

                    }
                }
                mAlertDialog?.show()
            }

        }
        mAlertTask?.executeOnExecutor(MyThreadExecutor())
    }

    private fun destroyAlertWaiter() {
        mAlertsActive = false
        if (mAlertDialog != null) {
            mAlertDialog?.cancel()
            mAlertDialog = null
        }
        if (mAlertTask != null) {
            mAlertTask?.cancel(true)
            mAlertTask = null
        }
    }

    private fun savePDF() {
        val saveAsyncTask: AsyncTask<Void, Void, Void?> = object : AsyncTask<Void, Void, Void?>() {

            override fun onPreExecute() {
                super.onPreExecute()
                loadingDialog.show()
            }

            override fun doInBackground(vararg params: Void?): Void? {
                try {
                    core?.save()
                } catch (e: UnsatisfiedLinkError) {
                }


                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                loadingDialog.dismiss()
                if (mViewModel.documentModel?.absolutePath != null) {
                    activity?.sendBroadcast(
                        Intent(
                            Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                                File(mViewModel.documentModel?.absolutePath!!)
                            )
                        )
                    )

                    if (actionType != null) {
                        when (actionType) {
                            ActionType.SHARE -> {
                                FileUtils.shareFile(
                                    mViewModel.documentModel?.absolutePath!!, mContext!!
                                )
                            }

                            ActionType.MERGE -> {
                                isAlive {
                                    destroyCore()
                                    val direc =
                                        ViewerFragmentDirections.actionPdfViewFragmentToMergeFragment()
                                    findNavController().navigate(direc)
                                }

                            }

                            ActionType.SPLIT -> {
                                isAlive {
                                    destroyCore()
                                    val direc =
                                        ViewerFragmentDirections.actionPdfViewFragmentToSplitFragment()
                                    findNavController().navigate(direc)
                                }

                            }

                            ActionType.SORT -> {
                                isAlive {
                                    destroyCore()
                                    val direc =
                                        ViewerFragmentDirections.actionPdfViewFragmentToSortPDFFragment()
                                    findNavController().navigate(direc)
                                }

                            }

                            ActionType.PDF_EXPORT -> {
                                isAlive {

                                    PdfToImages()
                                }
                            }

                            ActionType.COMPRESSION -> {
                                compressPDF()
                            }

                            else -> {
                                when {
                                    mViewModel.interstitialAd != null -> {
                                        sharedViewModel.selectedDoc = null
                                        showAd()
                                    }

                                    !storage.isUserGaveRating() && activity?.dateDifferenceInDays(
                                        storage.getRatingDate()
                                    )!! >= 1L -> {
                                        storage.setRatingDate()
                                        RatingsDialog.getInstance(false) {
                                            isAlive {
                                                sharedViewModel.selectedDoc = null
                                                findNavController().popBackStack()
                                            }
                                        }.show(childFragmentManager, "Rating Dialog")

                                    }

                                    else -> {
                                        isAlive {
                                            sharedViewModel.selectedDoc = null
                                            sharedViewModel.loadFiles(requireActivity())
                                            showAd()
                                        }
                                    }
                                }

                            }
                        }
                    } else {
                        when {
                            mViewModel.interstitialAd != null -> {
                                sharedViewModel.selectedDoc = null
                                showAd()
                            }

                            !storage.isUserGaveRating() && activity?.dateDifferenceInDays(
                                storage.getRatingDate()
                            )!! >= 1L -> {
                                storage.setRatingDate()
                                RatingsDialog.getInstance(false) {
                                    isAlive {
                                        sharedViewModel.selectedDoc = null
                                        findNavController().popBackStack()
                                    }
                                }.show(childFragmentManager, "Rating Dialog")

                            }

                            else -> {
                                isAlive {
                                    sharedViewModel.selectedDoc = null
                                    sharedViewModel.loadFiles(requireActivity())
                                    showAd()
                                }
                            }
                        }
                    }
                } else {
                    isAlive {
                        sharedViewModel.selectedDoc = null
                        sharedViewModel.loadFiles(requireActivity())
                        showAd()
                    }
                }


            }
        }

        saveAsyncTask.execute()
    }

    private fun saveAsPDF() {
        val saveAsyncTask: AsyncTask<Void, Void, Void?> = object : AsyncTask<Void, Void, Void?>() {

            override fun onPreExecute() {
                super.onPreExecute()
                loadingDialog.show()
            }

            override fun doInBackground(vararg params: Void?): Void? {
                try {
                    core?.save()
                } catch (e: UnsatisfiedLinkError) {
                }


                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                activity?.sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            File(mViewModel.documentModel?.absolutePath!!)
                        )
                    )
                )
                loadFile()
                loadingDialog.dismiss()
            }
        }

        saveAsyncTask.execute()
    }

    override fun onAttach(activity: Activity) {
        super.onAttach(activity)
        this.mContext = activity
    }

    private fun updatePageNumView(index: Int) {
        try {
            if (core != null) {
                val pageNumber = "${index + 1}/${core?.countPages()}"
//                mViewDataBinding.txtPageNumber.text = pageNumber
                mViewDataBinding.txtAnnotationNot.text = pageNumber

            }

        } catch (e: UnsatisfiedLinkError) {
        }
    }

    private fun PdfToImages() {
        lifecycleScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                loadingDialog.show()
            }

            FileUtils.convertPDFToImages(
                mViewModel.documentModel?.absolutePath!!,
                mViewModel.documentModel?.fileName!!,
                mViewModel.documentModel?.password!!,
                requireActivity()
            ) { flag ->
                activity?.runOnUiThread {
                    loadingDialog.dismiss()
                    if (flag) {
                        activity?.showToast("Images are saved!")
                        isAlive {
                            val root = Environment.getExternalStorageDirectory().toString()
                            val myDir = root + "$PDF_IMAGES/${mViewModel.documentModel?.fileName}/"

                            it.showCreateSheet(myDir) { flag ->
                                if (flag) {
                                    isAlive { con ->
                                        con.openFile(myDir)
                                    }
                                }
                            }
                        }
                    } else activity?.showToast("Failed to convert")
//                                        findNavController().popBackStack()
                }
            }

        }
    }

    private fun showSaveDialog(onClick: (Boolean) -> Unit) {
        val alert = mAlertBuilder?.create()
        alert?.setTitle("Save Changing?")
        alert?.cancel()
        alert?.setMessage(getString(R.string.document_has_changes_save_them_))
        alert?.setButton(
            AlertDialog.BUTTON_POSITIVE,
            getString(R.string.yes),

            ) { _, _ ->
            onClick(true)
        }
        alert?.setButton(
            AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
        ) { _, _ ->
            onClick(false)
        }
        alert?.show()
    }

//    private fun loadPDFView() {
//
//        mViewDataBinding.pdfView.apply {
//            fromFile(File(mViewModel.documentModel?.absolutePath!!)).pageFitPolicy(FitPolicy.WIDTH)
//                .enableAnnotationRendering(true)
//                .swipeHorizontal(storage.getScrollDirection())
//                .spacing(15)
//
//                .nightMode(isDarkThemeSP() == PublicValue.DARK_THEME)
//                .password(mViewModel.documentModel?.password).onPageChange { page, pageCount ->
//                    mViewDataBinding.txtPageNumber.text = "${page + 1}/${pageCount}"
//
//                }.onLoad {
//                    if (!isScroll) {
//                        loadingDialog.show()
//
//                        lifecycleScope.launch(Dispatchers.IO) {
//                            delay(500)
//                            loadFile()
//                        }
//                    } else {
//                        isScroll = false
//                    }
//
//
//                }
//                .onError { exception ->
//                    getPassword()
//                }.load()
//
//        }
//    }

//    private fun getPassword() {
//
//        DialogPdfPassword { action, password ->
//            when (action) {
//                ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
//                    mViewModel.documentModel?.password = password
//                    loadPDFView()
//                }
//                ViewPdfActions.NULL_PDF_PASSWORD -> {
//                    activity?.showToast(getString(R.string.txt_pdf_null_password_warning))
//                }
//                ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
//                    isAlive {
//                        findNavController().popBackStack()
//                    }
//
//
//                }
//            }
//        }.show(childFragmentManager, "Password Dialog")
//    }

    private fun destroyCore() {
        try {
            readerView?.applyToChildren(object : ViewMapper() {
                override fun applyToView(view: View) {
                    (view as MuPDFView?)?.releaseBitmaps()
                }
            })

            if (core != null) core?.onDestroy()

            if (mAlertTask != null) {
                mAlertTask!!.cancel(true)
                mAlertTask = null
            }
            core = null
        } catch (e: UnsatisfiedLinkError) {
        }

    }

    fun getVerticalThumb(progress: Int): Drawable {
        verticalThumb?.tvProgress?.text = progress.toString()
        verticalThumb?.root?.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        val bitmap = Bitmap.createBitmap(
            verticalThumb?.root?.measuredWidth!!,
            verticalThumb?.root?.measuredHeight!!,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        verticalThumb?.root?.layout(
            0, 0, verticalThumb?.root?.measuredWidth!!, verticalThumb?.root?.measuredHeight!!
        )
        verticalThumb?.root?.draw(canvas)
        return BitmapDrawable(resources, bitmap)
    }

    private fun getHorizontalThumb(progress: Int): Drawable {
        horizontalThumb?.tvProgress?.text = progress.toString()
        horizontalThumb?.root?.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        val bitmap = Bitmap.createBitmap(
            horizontalThumb?.root?.measuredWidth!!,
            horizontalThumb?.root?.measuredHeight!!,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        horizontalThumb?.root?.layout(
            0, 0, horizontalThumb?.root?.measuredWidth!!, horizontalThumb?.root?.measuredHeight!!
        )
        horizontalThumb?.root?.draw(canvas)
        return BitmapDrawable(resources, bitmap)
    }

    private fun setBrightness(brightness: Int) {
        isAlive { activity ->
            val var2: Window = activity.window
            val var3 = var2.attributes
            var3.screenBrightness = brightness.toFloat() / 255.0f
            var2.attributes = var3
        }

    }

    private fun updateAnnotViews(type: Annotation.Type) {

        when (type) {
            Annotation.Type.HIGHLIGHT -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = "Highlight"
                val color = storage.highlightColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(.5f, 1f, 1f, 0f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.HIGHLIGHT
                    )
                )

                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.UNDERLINE -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = "Underline"

                val color = storage.underlineColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(1f, 0f, 0f, 1f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.UNDERLINE
                    )
                )

                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.INK -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.deselectText()
                readerView?.setMode(MuPDFReaderView.Mode.Drawing)
                updateInkSetting()
                mViewDataBinding.annotToolbar.hide()
                mViewDataBinding.applyToolbar.show()
                val color = storage.inkColor
                mViewDataBinding.imgInkColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.HIGHLIGHT
                    )
                )

                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.STRIKEOUT -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = "Strikeout"

                val color = storage.strikeoutColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(1f, 1f, 0f, 0f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.STRIKEOUT
                    )
                )

                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            else -> {}
        }

        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            else -> {
                annotMode = type
            }
        }
    }

    private fun showAd() {
        when {
            mViewModel.interstitialAd != null -> {
                val interstitialAd = mViewModel.interstitialAd
                interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                    override fun onAdDismissedFullScreenContent() {
                        super.onAdDismissedFullScreenContent()
                        isAlive {
                            loadingDialog.dismiss()
                            mViewModel.interstitialAd = null
                            findNavController().popBackStack()
                        }
                    }
                }
                interstitialAd?.show(requireActivity())
            }

            else -> {
                isAlive {
                    findNavController().popBackStack()
                }
            }
        }

    }
}