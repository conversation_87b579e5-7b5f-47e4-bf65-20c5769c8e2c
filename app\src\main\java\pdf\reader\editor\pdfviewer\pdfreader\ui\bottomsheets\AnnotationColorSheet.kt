package pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.artifex.mupdfdemo.ColorPalette
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AnnotationColorLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.ui.adapter.ColorAdapter

class AnnotationColorSheet : BottomSheetDialogFragment() {
    companion object {
        private var callback: ((Int) -> Unit)? = null

        private var type: com.artifex.mupdfdemo.Annotation.Type =
            com.artifex.mupdfdemo.Annotation.Type.HIGHLIGHT
        private var pos = 0
        fun getInstance(
            text: com.artifex.mupdfdemo.Annotation.Type,
            pos: Int,
            callback: ((Int) -> Unit)
        ): AnnotationColorSheet {
            this.callback = callback
            this.type = text
            this.pos = pos
            return AnnotationColorSheet()
        }
    }

    private var binding: AnnotationColorLayoutBinding? = null
    private var colorAdapter: ColorAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = AnnotationColorLayoutBinding.inflate(inflater, container, false)
        initViews()

        return binding?.root
    }

    private fun initViews() {

        val colorList = when (type) {
            com.artifex.mupdfdemo.Annotation.Type.HIGHLIGHT -> {
                ColorPalette.highlightPaletteRGB.size
            }
            com.artifex.mupdfdemo.Annotation.Type.UNDERLINE -> {
                ColorPalette.underlinePaletteRGB.size
            }
            else -> {
                ColorPalette.strikeoutPaletteRGB.size
            }
        }
        colorAdapter = ColorAdapter(type, colorList, pos) {

            callback?.invoke(it)
//            if (pos != it) {
//                if (it > 0) {
//                    callback?.invoke(it - 1)
//                } else {
//                    callback?.invoke(0)
//                }
//            }
        }
        binding?.apply {
            txtAnnotationTitle.text = type.name
            colorRecyclerView.adapter = colorAdapter
            imgSheetClose.setOnClickListener {
                dismiss()
            }

        }
    }
}