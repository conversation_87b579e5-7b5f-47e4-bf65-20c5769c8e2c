package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.file

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.artifex.mupdfdemo.ActionType
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentSheetFileBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isPortrait
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogDeletePdf
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogGoToPage
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogRenamePdf
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.FileInfoDialog


class SheetFileFragment(
    private val pageCount: Int,
    private val onScroll: (ActionType) -> Unit,
    private val onClose: (Int?) -> Unit
) : Fragment() {

    private var binding: FragmentSheetFileBinding? = null
    private var documentModel: DocumentsModel? = null
    private var storage: Storage? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        binding = FragmentSheetFileBinding.inflate(inflater, container, false)
        documentModel = arguments?.getParcelable("documentModel")
        storage = Storage(requireContext())
        initViews()
        return binding?.root
    }

    private fun initViews() {
        if (activity?.isPortrait() == false) {
            binding?.btnScrollSheetPDF?.setCompoundDrawablesWithIntrinsicBounds(
                0,
                R.drawable.ic_vertical_scroll,
                0,
                0
            )
            binding?.btnScrollSheetPDF?.text = getString(R.string.view_vertical)
        } else {
            binding?.btnScrollSheetPDF?.setCompoundDrawablesWithIntrinsicBounds(
                0,
                R.drawable.ic_horizontal_scroll,
                0,
                0
            )
            binding?.btnScrollSheetPDF?.text = getString(R.string.view_horizontal)
        }

        if (storage?.isViewContinues() == true) {
            binding?.btnViewTypeSheetPDF?.setCompoundDrawablesWithIntrinsicBounds(
                0,
                R.drawable.ic_vertical_scroll,
                0,
                0
            )
            binding?.btnViewTypeSheetPDF?.text = getString(R.string.page_by_page)
        } else {
            binding?.btnViewTypeSheetPDF?.setCompoundDrawablesWithIntrinsicBounds(
                0,
                R.drawable.ic_horizontal_scroll,
                0,
                0
            )
            binding?.btnViewTypeSheetPDF?.text = getString(R.string.continuous_page)
        }

//        binding?.btnDarkModeSheetPDF?.text = when (storage?.pdfNightMode!!) {
//            true -> {
//
//                "Light Mode"
//            }
//            else -> {
//                "Dark Mode"
//            }
//        }
        binding?.apply {

            btnRenameSheetPDF.setOnClickListener {
                DialogRenamePdf.getInstance(
                    documentModel?.fileName!!,
                    documentModel!!
                ) { action, fileName ->
                    when (action) {
                        ViewPdfActions.RENAME -> {
//                            FileUtils.renameFile(
//                                fileName, documentModel?.absolutePath!!, requireContext()
//                            ) {
//
//                            }
                        }

                        ViewPdfActions.DUPLICATE_FILE -> {
                            Toast.makeText(
                                requireContext(),
                                requireContext().getString(R.string.txt_pdf_duplicate),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        ViewPdfActions.EMPTY_RENAME -> {
                            Toast.makeText(
                                requireContext(),
                                requireContext().getString(R.string.txt_pdf_empty),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                            Toast.makeText(
                                requireContext(),
                                requireContext().getString(R.string.txt_pdf_special_charac),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        ViewPdfActions.INVALID_EXTENSION -> {
                            Toast.makeText(
                                requireContext(),
                                requireContext().getString(R.string.txt_pdf_invalid_extension),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        else -> {}
                    }
                }.show(childFragmentManager, "newFileDialog")

            }

            btnGoToSheetPDF.setOnClickListener {
                DialogGoToPage.getInstance(pageCount) { pageNo, action ->
                    when (action) {
                        ViewPdfActions.OK_GOTO_PAGE -> {
                            onClose(pageNo)
                        }

                        ViewPdfActions.PAGE_SIZE_EXCEDED -> {
                            requireContext().showToast(getString(R.string.txt_pdf_PAGE_404))
                        }

                        ViewPdfActions.VALID_PAGE_SIZE -> {
                            requireContext().showToast(getString(R.string.txt_pdf_PAGE_valid))
                        }

                        else -> {

                        }
                    }
                }.show(childFragmentManager, "dialogGoToPage")
            }

//            btnSplitSheetPDF.setOnClickListener { }

            /*btnPasswordSheetPDF.setOnClickListener {
                if (FileUtils.isPDFEncrypted(documentModel?.absolutePath)) {
                    requireContext().showToast("File is already protected!")
                } else {
                    DialogPdfProtect(
                        documentModel?.absolutePath!!,
                        documentModel?.parentFile!!,
                        documentModel?.fileName!!,
                    ) { action, _ ->
                        when (action) {
                            ViewPdfActions.PDF_PROTECT_PASSWORD -> {

                            }
                            ViewPdfActions.NULL_PDF_PASSWORD -> {
                                requireContext().showToast(requireContext().getString(R.string.txt_pdf_null_password_warning))
                            }
                        }
                    }.show(childFragmentManager, "Password Protect")
                }

            }*/

            btnDetailSheetPDF.setOnClickListener {

                FileInfoDialog.getInstance(documentModel!!).show(
                    childFragmentManager, "PDF Detail Dialog"
                )

            }

            btnShareSheetPDF.setOnClickListener {
                onScroll(ActionType.SHARE)
            }

            btnDeleteSheetPDF.setOnClickListener {
                DialogDeletePdf.getInstance(documentModel!!) {
                    onClose(null)
                }.show(childFragmentManager, "DeletePdfDialog")

            }

            btnScrollSheetPDF.setOnClickListener {

                onScroll(ActionType.SCROLL)
            }

            btnViewModeSheetPDF.setOnClickListener {
                onScroll(ActionType.READING_MODE)
            }

//            btnDarkModeSheetPDF.setOnClickListener {
//                onScroll(ActionType.DARK_MODE)
//            }
//
            btnReadSettingSheetPDF.setOnClickListener {
                onScroll(ActionType.READING_SETTING)
            }

            btnViewTypeSheetPDF.setOnClickListener {
                storage?.setViewContinues(!storage?.isViewContinues()!!)
                onScroll(ActionType.VIEW_MODE)
            }
        }
    }


}