package com.artifex.mupdfdemo;

import android.graphics.Color;

public class ColorPalette {

    public final static int[][] paletteRGB = {
            {0, 0, 0},
            {0, 34, 110},
            {4, 70, 110},
            {75, 28, 99},
            {52, 76, 5},
            {136, 131, 0},
            {121, 66, 3},
            {98, 4, 4},

            {49, 49, 49},
            {0, 62, 204},
            {0, 153, 204},
            {153, 51, 204},
            {102, 153, 0},
            {255, 246, 1},
            {255, 136, 0},
            {204, 0, 0},

            {91, 91, 91},
            {52, 102, 228},
            {51, 181, 229},
            {170, 102, 204},
            {153, 204, 0},
            {238, 255, 51},
            {255, 187, 51},
            {255, 68, 68},
            {255, 255, 255},
    };

    public final static int[][] highlightPaletteRGB = {
            {1, 1, 0},
            {0, 0, 0},
            {0, 34, 110},
            {4, 70, 110},
            {75, 28, 99},
            {52, 76, 5},
            {136, 131, 0},
            {121, 66, 3},
            {98, 4, 4},

            {49, 49, 49},
            {0, 62, 204},
            {0, 153, 204},
            {153, 51, 204},
            {102, 153, 0},
            {255, 246, 1},
            {255, 136, 0},
            {204, 0, 0},

            {91, 91, 91},
            {52, 102, 228},
            {51, 181, 229},
            {170, 102, 204},
            {153, 204, 0},
            {238, 255, 51},
            {255, 187, 51},
            {255, 68, 68},
            {255, 255, 255},
    };

    public final static int[][] underlinePaletteRGB = {
            {0, 0, 1},
            {0, 0, 0},
            {0, 34, 110},
            {4, 70, 110},
            {75, 28, 99},
            {52, 76, 5},
            {136, 131, 0},
            {121, 66, 3},
            {98, 4, 4},

            {49, 49, 49},
            {0, 62, 204},
            {0, 153, 204},
            {153, 51, 204},
            {102, 153, 0},
            {255, 246, 1},
            {255, 136, 0},
            {204, 0, 0},

            {91, 91, 91},
            {52, 102, 228},
            {51, 181, 229},
            {170, 102, 204},
            {153, 204, 0},
            {238, 255, 51},
            {255, 187, 51},
            {255, 68, 68},
            {255, 255, 255},
    };

    public final static int[][] strikeoutPaletteRGB = {
            {1, 0, 0},
            {0, 0, 0},
            {0, 34, 110},
            {4, 70, 110},
            {75, 28, 99},
            {52, 76, 5},
            {136, 131, 0},
            {121, 66, 3},
            {98, 4, 4},

            {49, 49, 49},
            {0, 62, 204},
            {0, 153, 204},
            {153, 51, 204},
            {102, 153, 0},
            {255, 246, 1},
            {255, 136, 0},
            {204, 0, 0},

            {91, 91, 91},
            {52, 102, 228},
            {51, 181, 229},
            {170, 102, 204},
            {153, 204, 0},
            {238, 255, 51},
            {255, 187, 51},
            {255, 68, 68},
            {255, 255, 255},
    };


    // private final static int[ ] paletteHEX = {
    //     0x33B5E5,
    //     0xAA66CC,
    //     0x99CC00,
    //     0xFFBB33,
    //     0xFF4444,
    //     0x0099CC,
    //     0x9933CC,
    //     0x669900,
    //     0xFF8800,
    //     0xCC0000
    // };

    public static float getR(int number, Annotation.Type type) {
        if (number < 0) number = 0;
        switch (type) {
            case HIGHLIGHT: {
                if (number >= highlightPaletteRGB.length) number = highlightPaletteRGB.length - 1;
                return highlightPaletteRGB[number][0] / 255f;
            }
            case UNDERLINE: {
                if (number >= underlinePaletteRGB.length) number = underlinePaletteRGB.length - 1;
                return underlinePaletteRGB[number][0] / 255f;
            }
            case STRIKEOUT: {

                if (number >= strikeoutPaletteRGB.length) number = strikeoutPaletteRGB.length - 1;
                return strikeoutPaletteRGB[number][0] / 255f;
            }
            default: {
                if (number >= paletteRGB.length) number = paletteRGB.length - 1;
                return paletteRGB[number][0] / 255f;
            }
        }

    }

    public static float getG(int number, Annotation.Type type) {
        if (number < 0) number = 0;
        switch (type) {
            case HIGHLIGHT: {
                if (number >= highlightPaletteRGB.length) number = highlightPaletteRGB.length - 1;
                return highlightPaletteRGB[number][1] / 255f;
            }
            case UNDERLINE: {
                if (number >= underlinePaletteRGB.length) number = underlinePaletteRGB.length - 1;
                return underlinePaletteRGB[number][1] / 255f;
            }
            case STRIKEOUT: {

                if (number >= strikeoutPaletteRGB.length) number = strikeoutPaletteRGB.length - 1;
                return strikeoutPaletteRGB[number][1] / 255f;
            }
            default: {
                if (number >= paletteRGB.length) number = paletteRGB.length - 1;
                return paletteRGB[number][1] / 255f;
            }
        }

    }

    public static float getB(int number, Annotation.Type type) {
        if (number < 0) number = 0;
        switch (type) {
            case HIGHLIGHT: {
                if (number >= highlightPaletteRGB.length) number = highlightPaletteRGB.length - 1;
                return highlightPaletteRGB[number][2] / 255f;
            }
            case UNDERLINE: {
                if (number >= underlinePaletteRGB.length) number = underlinePaletteRGB.length - 1;
                return underlinePaletteRGB[number][2] / 255f;
            }
            case STRIKEOUT: {

                if (number >= strikeoutPaletteRGB.length) number = strikeoutPaletteRGB.length - 1;
                return strikeoutPaletteRGB[number][2] / 255f;
            }
            default: {
                if (number >= paletteRGB.length) number = paletteRGB.length - 1;
                return paletteRGB[number][2] / 255f;
            }
        }

    }

    public static int getHex(int number, Annotation.Type type) {
        if (number < 0) number = 0;

        switch (type) {
            case HIGHLIGHT: {
                if (number >= highlightPaletteRGB.length) number = highlightPaletteRGB.length - 1;
                return Color.argb(255, highlightPaletteRGB[number][0], highlightPaletteRGB[number][1], highlightPaletteRGB[number][2]);
            }
            case UNDERLINE: {
                if (number >= underlinePaletteRGB.length) number = underlinePaletteRGB.length - 1;
                return Color.argb(255, underlinePaletteRGB[number][0], underlinePaletteRGB[number][1], underlinePaletteRGB[number][2]);
            }
            case STRIKEOUT: {

                if (number >= strikeoutPaletteRGB.length) number = strikeoutPaletteRGB.length - 1;
                return Color.argb(255, strikeoutPaletteRGB[number][0], strikeoutPaletteRGB[number][1], strikeoutPaletteRGB[number][2]);
            }
            default: {
                if (number >= paletteRGB.length) number = paletteRGB.length - 1;
                return Color.argb(255, paletteRGB[number][0], paletteRGB[number][1], paletteRGB[number][2]);
            }
        }

    }

    public static CharSequence[] getColorNames() {
        CharSequence[] colorNames = {
                "Black",
                "Dark blue",
                "Dark turquoise",
                "Dark violet",
                "Dark green",
                "Dark yellow",
                "Dark orange",
                "Dark red",
                "Gray",
                "Blue",
                "Turquoise",
                "Violet",
                "Green",
                "Yellow",
                "Orange",
                "Red",
                "Light Gray",
                "Light blue",
                "Light turquoise",
                "Light violet",
                "Light green",
                "Light yellow",
                "Light orange",
                "Light red",
                "White"
        };
        return colorNames;
    }

    public static CharSequence[] getColorNumbers() {
        CharSequence[] colorNumbers = new CharSequence[paletteRGB.length];
        for (int i = 0; i < paletteRGB.length; i++) colorNumbers[i] = Integer.toString(i);
        return colorNumbers;
    }
}
