package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter


class ViewPageAdapter(supportFragmentManager: FragmentManager) :
    FragmentPagerAdapter(supportFragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

    private val mFragmentList = ArrayList<Fragment>()
    private val mFragmentListTitle = ArrayList<String>()

    override fun getCount(): Int {
        return mFragmentList.size

    }

    override fun getItem(position: Int): Fragment {

        return mFragmentList[position]
    }

    override fun getPageTitle(position: Int): CharSequence = mFragmentListTitle[position]

    fun addFragment(fragment: Fragment, title: String) {

        mFragmentList.add(fragment)
        mFragmentListTitle.add(title)
    }
}

//class BottomSheetViewPagerAdapter(private val fa: FragmentActivity) : FragmentStateAdapter(fa) {
//    override fun getItemCount(): Int {
//
//    }
//
//    override fun createFragment(position: Int): Fragment {
//        return if (position==0){
//            SettingsFragment()
//        }else (if (position==1){
//            SettingsFragment()
//        }else{
//            SettingsFragment()
//        }) as Fragment
//    }
//}