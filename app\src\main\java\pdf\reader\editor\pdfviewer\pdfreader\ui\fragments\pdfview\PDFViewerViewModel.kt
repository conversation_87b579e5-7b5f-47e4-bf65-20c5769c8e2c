package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview

import android.app.Activity
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.interstitial.InterstitialAd
import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.local.database.entity.PdfStoreItems
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.getInterstitialVsStartAppAdObject
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.Compression
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ConverstionType
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.shared.Progress
import java.io.File
import javax.inject.Inject

@HiltViewModel
class PDFViewerViewModel @Inject constructor(
    private val repositoryLocal: RepositoryLocal
) : BaseViewModel() {
    var documentModel: DocumentsModel? = null
    var listData: MutableLiveData<Int> = MutableLiveData()
    var bannerAd: AdView? = null
    var interstitialAd: InterstitialAd? = null
    private var isAdReqSent = false
//    var core: MuPDFCore? = null

    fun insertRecentFiles(pdfStoreItems: PdfStoreItems) {
        repositoryLocal.insertRecentPdfRecord(pdfStoreItems)
    }

    fun compressFile(
        file: File,
        type: FileType,
        compressed: Compression = Compression.RECOMMENDED,
        fileConversion: ConverstionType = ConverstionType.COMPRESSION,
        progress: Progress
    ) {
        repositoryLocal.convertFile(file, type, compressed, fileConversion, progress)
    }

    fun loadAd(activity: Activity, isConsentDone: Boolean, onResult: (AdView) -> Unit) {
        if (!isAdReqSent) {
            isAdReqSent = true
            if (activity.checkInternetConnection() && repositoryLocal.isViewerIntEnable && isConsentDone) {
                activity.getInterstitialVsStartAppAdObject("activity.getString(R.string.viewer_back_int_ad_id)",
                    onResult = {
                        interstitialAd = it
                    },
                    onAdClosed = {},
                    onAdLoadFailed = {})
            }

            /*if (activity.checkInternetConnection() && repositoryLocal.isViewerBannerEnable) {
                bannerAd = AdView(activity)
                activity.loadAdaptiveBanner(bannerAd!!)
                bannerAd?.adListener = object : AdListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        onResult(bannerAd!!)
                    }
                }
            }*/


        } else if (bannerAd != null) {
            onResult(bannerAd!!)
        }

    }
}