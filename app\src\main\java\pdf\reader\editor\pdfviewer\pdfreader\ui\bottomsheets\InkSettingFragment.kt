package pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import com.artifex.mupdfdemo.ColorPalette
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentInkSettingBinding
import pdf.reader.editor.pdfviewer.pdfreader.ui.adapter.ColorAdapter


class InkSettingFragment : BottomSheetDialogFragment() {

    companion object {
        private var callback: ((Int) -> Unit)? = null
        private var thick: ((Float) -> Unit)? = null
        private var thickness = 5f
        private var pos = 0
        fun getInstance(
            thickness: Float,
            pos: Int,
            thick: ((Float) -> Unit),
            callback: ((Int) -> Unit)
        ): InkSettingFragment {
            this.callback = callback
            this.thick = thick
            this.pos = pos
            this.thickness = thickness
            return InkSettingFragment()
        }
    }

    private var binding: FragmentInkSettingBinding? = null
    private var colorAdapter: ColorAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentInkSettingBinding.inflate(inflater, container, false)
        initViews()

        return binding?.root
    }

    private fun initViews() {
        colorAdapter = ColorAdapter(
            com.artifex.mupdfdemo.Annotation.Type.INK,
            ColorPalette.paletteRGB.size,
            pos
        ) {
            callback?.invoke(it)
        }

        binding?.apply {
            colorRecyclerView.adapter = colorAdapter

            inkThicknessSeek.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {

                    when (seekBar?.progress) {

                        in 0..24 -> {
                            txtThicknessProgress.text = "2.5 pt"
                            thickness = 2.5f
                        }

                        in 25..49 -> {
                            txtThicknessProgress.text = "5 pt"
                            thickness = 5f
                        }

                        in 50..75 -> {
                            txtThicknessProgress.text = "5.5 pt"
                            thickness = 5.5f
                        }

                        else -> {
                            txtThicknessProgress.text = "10 pt"
                            thickness = 10f
                        }
                    }
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {

                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {

                }
            })
        }

        when (thickness) {

            in 0f..2.5f -> {
                binding?.inkThicknessSeek?.progress = 0
            }

            in 2.6f..5f -> {
                binding?.inkThicknessSeek?.progress = 25
            }

            in 5.1f..7.4f -> {
                binding?.inkThicknessSeek?.progress = 50
            }

            else -> {
                binding?.inkThicknessSeek?.progress = 100
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        thick?.invoke(thickness)

    }


}