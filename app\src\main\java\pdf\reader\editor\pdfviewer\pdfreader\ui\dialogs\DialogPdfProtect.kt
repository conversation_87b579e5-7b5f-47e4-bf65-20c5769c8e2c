package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.itextpdf.text.exceptions.BadPasswordException
import com.itextpdf.text.pdf.PdfReader.unethicalreading
import com.itextpdf.text.pdf.PdfStamper
import com.itextpdf.text.pdf.PdfWriter.ALLOW_COPY
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogProtectPdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class DialogPdfProtect : BaseDialog<DialogProtectPdfBinding>() {
//    override val resLayout: Int
//        get() = R.layout.dialog_pdf_password_layout

    companion object {
        //arguments
        private var callback: ((ViewPdfActions, String?) -> Unit)? = null
        private var isProtected: Boolean? = null
        private var path: String? = null
        private var fileName: String? = null
        fun getInstance(
            isProtected: Boolean,
            path: String,
            fileName: String,
            callback: ((ViewPdfActions, String?) -> Unit)
        ): DialogPdfProtect {
            this.callback = callback
            this.isProtected = isProtected
            this.path = path
            this.fileName = fileName
            return DialogPdfProtect()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Handler(Looper.getMainLooper()).postDelayed({
            view.showKeyboard()

            binding.edtReadPass.requestFocus()
        }, 500)
        this.isCancelable = false

        if (isProtected == true) {
            binding.apply {
                protectAnim.setAnimation(R.raw.unprotect_anim)
                txtProtectLbl.text = getString(R.string.unprotect_lbl)
                txtProtectSub.text = getString(R.string.unprotect_sub)
                edtReadPass.hint = getString(R.string.enter_password)
                inputPDFProtectE.hide()
                btDone.text = getString(R.string.remove)
            }


        }

        binding.btDone.setOnClickListener {

            when (isProtected) {
                false -> {
                    val pass: String = binding.edtReadPass.text?.trim().toString()
                    val editPass = binding.edtEditPass.text?.trim().toString()
                    when {
                        pass.isEmpty() -> {
                            callback?.invoke(ViewPdfActions.NULL_PDF_PASSWORD, null)
                        }
                        editPass.isEmpty() -> {
                            callback?.invoke(ViewPdfActions.NULL_PDF_PASSWORD, null)
                        }
                        else -> {
                            it.hideKeyboard()
                            protectFile(pass, editPass)
                        }
                    }
                }
                true -> {
                    val pass: String = binding.edtReadPass.text?.trim().toString()
                    when {
                        pass.isEmpty() -> {
                            callback?.invoke(ViewPdfActions.NULL_PDF_PASSWORD, null)
                        }
                        else -> {
                            it.hideKeyboard()
                            unProtectFile(pass)
                        }
                    }
                }

                else -> {}
            }

            /*        if (pass.isEmpty()) {
                        callback(ViewPdfActions.NULL_PDF_PASSWORD, null)
                    } else {
                        it.hideKeyboard()
                        try {
                            val pdfReader = PdfReader(path)
                            val writerProperties = WriterProperties()
                            writerProperties.setStandardEncryption(
                                pass.toByteArray(),
                                null,
                                EncryptionConstants.ALLOW_PRINTING or
                                        EncryptionConstants.ALLOW_MODIFY_CONTENTS or
                                        EncryptionConstants.ALLOW_MODIFY_ANNOTATIONS or
                                        EncryptionConstants.ALLOW_COPY or
                                        EncryptionConstants.DO_NOT_ENCRYPT_METADATA,
                                EncryptionConstants.ENCRYPTION_AES_128
                            )
                            context?.del(path)
                            val pdfWriter = PdfWriter(
                                FileOutputStream(parentPath + "/${fileName}.pdf"),
                                writerProperties
                            )
                            val pdfDocument = PdfDocument(pdfReader, pdfWriter)
                            pdfDocument.close()
                            context?.scanFile(path, fileName, parentPath)
                            context?.showToast(
                                context?.getString(R.string.txt_pdf_pass_protect_successfully)
                                    .toString()
                            )
                            dismiss()
                            callback(ViewPdfActions.PDF_PROTECT_PASSWORD, pass)
                        } catch (ex: Exception) {
                            Toast.makeText(
                                requireContext(),
                                getString(R.string.txt_pdf_pass_protect_warning),
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }*/


        }

        binding.btnCancel.setOnClickListener {
            callback?.invoke(ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED, "")
            it.hideKeyboard()
            dismiss()
        }
    }

    private fun protectFile(pass: String, editPass: String) {
        try {
            val pdfReader = com.itextpdf.text.pdf.PdfReader(path)
            unethicalreading = true
            val timeStamp = SimpleDateFormat("yyyyMMdd", Locale.US).format(Date())
            val newFile =
                FileUtils.saveFileToExternalStorage("${fileName}_${timeStamp}_locked")

            val stamper = PdfStamper(pdfReader, FileOutputStream(newFile?.absoluteFile))
            stamper.setEncryption(
                pass.toByteArray(),
                null,
                ALLOW_COPY
                        or com.itextpdf.text.pdf.PdfWriter.ALLOW_DEGRADED_PRINTING
                        or com.itextpdf.text.pdf.PdfWriter.ALLOW_PRINTING,
                com.itextpdf.text.pdf.PdfWriter.ENCRYPTION_AES_128
            )
            stamper.close()
            pdfReader.close()
            context?.sendBroadcast(
                Intent(
                    Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                    Uri.fromFile(newFile)
                )
            )
            context?.showToast(
                context?.getString(R.string.txt_pdf_pass_protect_successfully).toString()
            )
            dismiss()
            callback?.invoke(ViewPdfActions.PDF_PROTECT_PASSWORD, pass)
        } catch (ex: Exception) {
            Toast.makeText(
                requireContext(),
                getString(R.string.txt_pdf_pass_protect_warning),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun unProtectFile(inputPassword: String) {
        CoroutineScope(Dispatchers.IO).launch {
            kotlin.runCatching {


                val reader = com.itextpdf.text.pdf.PdfReader(path, inputPassword.toByteArray())
                unethicalreading = true
                val timeStamp = SimpleDateFormat("yyyyMMdd", Locale.US).format(Date())
                val newFile =
                    FileUtils.saveFileToExternalStorage("${fileName}_${timeStamp}_unlock")
                val stamper = PdfStamper(reader, FileOutputStream(newFile?.absoluteFile))

                stamper.close()
                reader.close()

                withContext(Dispatchers.Main) {

                    FileUtils.deleteFile(path!!, requireContext()) {}
                    context?.sendBroadcast(
                        Intent(
                            Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                            Uri.fromFile(newFile)
                        )
                    )

                    context?.showToast(
                        getString(R.string.txt_pdf_pass_unprotect_successfully)
                    )
                    dismiss()
                    callback?.invoke(ViewPdfActions.PDF_PROTECT_PASSWORD, null)
                }

            }.onSuccess {

            }.onFailure {
                when (it) {
                    is BadPasswordException -> {
                        withContext(Dispatchers.Main) {

                            context?.showToast(
                                getString(R.string.txt_pdf_pass_unprotect_failed)
                            )
                        }
                    }
                }

            }
        }
    }

    override fun onStart() {
        super.onStart()
        setTopAnimation()
    }

    override fun getViewBinding(
        inflater: LayoutInflater, container: ViewGroup?
    ): DialogProtectPdfBinding =
        DialogProtectPdfBinding.inflate(LayoutInflater.from(requireContext()), null, false)

}