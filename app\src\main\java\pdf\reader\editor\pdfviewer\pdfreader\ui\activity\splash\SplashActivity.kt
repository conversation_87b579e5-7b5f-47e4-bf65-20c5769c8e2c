package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.splash

import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.ads.interstitial.InterstitialAd
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.InAppUpdateManager.Companion.inAppUpdate
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.UpdateType
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivitySplashBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.extensions.copyPdfFromAssetsToCache
import pdf.reader.editor.pdfviewer.pdfreader.extensions.fromHtml
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showGDPRScreen
import pdf.reader.editor.pdfviewer.pdfreader.extensions.startAppActivity
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AdUtility.isSplashAdFailed
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.getInterstitialVsStartAppAdObject
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.displayBannerAd
import pdf.reader.editor.pdfviewer.pdfreader.newads.hasInternetConnected
import pdf.reader.editor.pdfviewer.pdfreader.newads.isNetworkAvailable
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.newads.manageFrameLayoutView
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.language.LanguageActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main.MainActivity
import javax.inject.Inject


@AndroidEntryPoint
@SuppressLint("CustomSplashScreen")
class SplashActivity : AppCompatActivity() {

    private val splashViewModel: SplashViewModel by viewModels()
    private val binding: ActivitySplashBinding by lazy {
        ActivitySplashBinding.inflate(layoutInflater)
    }
    private val storage: Storage by lazy {
        Storage(this)
    }
    private var timeToWait = 10000L
    private var delayJob: Job? = null
    private var interstitialAd: InterstitialAd? = null
    private var isPause = false

    private val adViewModel by viewModels<AdViewModel>()

    @Inject
    lateinit var adsIds: AdsIds

    private fun applySystemBarsPadding(targetView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(targetView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= 35) {
            enableEdgeToEdge()
        }
        splashViewModel.getRemoteValues()
        setContentView(binding.root)
        if (Build.VERSION.SDK_INT >= 35) {
            applySystemBarsPadding(binding.root)
        }
        binding.txtAppName.text = fromHtml(resources.getString(R.string.pdf_reader))

        if (isNetworkAvailable()) {
            CoroutineScope(Dispatchers.IO).launch {
                val isConnect = hasInternetConnected()
                withContext(Dispatchers.Main) {
                    if (isConnect) {

                        adViewModel.initRemoteConfig {
                            if (storage.isConsentDone) {
                                initViews()
                            } else {
                                showGDPRScreen { shown, canRequestAds ->
                                    if (shown)
                                        storage.isConsentDone = canRequestAds
                                    initViews()
                                }
                            }
                        }

                    } else {
                        initViews()
                    }
                }
            }
        } else {
            initViews()
        }
        initDownloadDummyFile()


    }

    private fun initDownloadDummyFile() {
        val pdfFile = copyPdfFromAssetsToCache(this)

        if (pdfFile != null) {
            // PDF file successfully copied to cache directory
            // You can now use pdfFile to open or share the file
            logs("PDF copied to cache: ${pdfFile.absolutePath}")
        } else {
            // Handle the error
            logs("Failed to copy PDF file")
        }
    }

    private fun loadBanner(callback: () -> Unit) {
        binding.bannerView.adViewContainer.show()
        adsIds.apply {
            splashBanner.run {
                canRequestAd = storage.isConsentDone
                remoteConfig = adViewModel.getRemoteConfigModel().adConfigModel.splashBanner.show
            }
            adViewModel.loadBanner(bannerAdInfo = splashBanner,
                context = this@SplashActivity,
                view = binding.bannerView.adViewContainer,
                onBannerNotFound = {
                    callback()
                    manageFrameLayoutView(binding.bannerView.adViewContainer)
                },
                onBannerLoaded = { i, hashMap ->
                    callback()
                    displayBannerAd(binding.bannerView.adViewContainer, hashMap[i]?.adView)
                },
                onBannerFailedToLoad = {
                    callback()
                    manageFrameLayoutView(binding.bannerView.adViewContainer)

                })
        }

    }

    private fun checkForUpdates() {
        if (adViewModel.getPrefValueForInAppUpdate() == 2L) {
            inAppUpdate {
                configure {
                    updateType = UpdateType.IMMEDIATE
                    callback(
                        moveNext = { adViewModel.setData(1) },
                        closeScreen = { adViewModel.setData(0) })
                }
            }
        } else {
            adViewModel.setData(1)
        }

    }

    private fun initViews() {
        checkForUpdates()
        lifecycleScope.launch {
            adViewModel.dataFlow.collect { value ->
                if (value == 1) {
                    loadNewAds()
                } else if (value == 0) {
                    finishAffinity()
                }
            }
        }


    }

    private fun loadNewAds() {
        if (checkInternetConnection() && storage.isConsentDone
        ) {
            CoroutineScope(Dispatchers.IO).launch {

                val isConnected = hasInternetConnected()
                withContext(Dispatchers.Main) {
                    if (isConnected) {
                        loadBanner {
                            waitForAWhile(timeToWait)
                            if (!storage.preferences.getBoolean(
                                    "isLanguageSet",
                                    false
                                ) && storage.isConsentDone
                            ) {
//                    getNativeAdObject(getString(R.string.language_native_ad_id), null, onResult = {
//                        it?.let { it1 -> splashViewModel.setNative(it1) }
//                    })
                                adsIds.apply {
                                    adViewModel.apply {
                                        languageNative.run {
                                            canRequestAd = storage.isConsentDone
                                            remoteConfig =
                                                adViewModel.getRemoteConfigModel().adConfigModel.languageNative.show
                                        }
                                        splashInterWhenFail.run {
                                            canRequestAd = storage.isConsentDone
                                            remoteConfig =
                                                adViewModel.getRemoteConfigModel().adConfigModel.splashInterWhenFail.show
                                        }
                                        loadInterstitialAd(adInfo = splashInterWhenFail)
                                        loadNativeAd(nativeAdInfo = adsIds.languageNative,
                                            onAdLoaded = { i, hashMap ->
                                                hashMap[i]?.nativeAd?.let {
                                                    splashViewModel.setNative(
                                                        it
                                                    )
                                                }
                                            })
                                    }

                                }

                            }
                            if (adViewModel.getRemoteConfigModel().adConfigModel.splashInterstitial.show) {
                                getInterstitialVsStartAppAdObject(
                                    adsIds.splashInterstitialAd.adUnitId,
                                    onResult = {
                                        if (!isPause) {
                                            interstitialAd = it
                                            delayJob?.cancel()
                                            navigateToNextScreen()
                                        }

                                    },
                                    onAdClosed = {
                                        interstitialAd = null
                                        navigateToNextScreen()

                                    },
                                    onAdLoadFailed = {
                                        isSplashAdFailed = true
                                        interstitialAd = null
                                    },
                                )
                            } else {
                                isSplashAdFailed = true
                                waitForAWhile(timeToWait)
                            }


                        }

                    } else waitForAWhile(1000)

                }
            }
        } else waitForAWhile(3000)
    }

    private fun waitForAWhile(timeToWait: Long) {
        delayJob = lifecycleScope.launch {
            delay(timeToWait)
            navigateToNextScreen()
        }
    }

    private fun navigateToNextScreen() {
        delayJob?.cancel()

        if (interstitialAd != null) {
            interstitialAd?.show(this)
        } else {
            if (!storage.preferences.getBoolean("isLanguageSet", false)
                && splashViewModel.getNative().value != null
            ) {
                startAppActivity<LanguageActivity>()
                finish()
            } else {
                startAppActivity<MainActivity>()
                finish()
            }
        }


    }

    override fun onPause() {
        super.onPause()
        adViewModel.pauseBannerAd(adsIds.splashBanner.adKey)
        delayJob?.cancel()

    }

    override fun onResume() {
        super.onResume()
        adViewModel.resumeBannerAd(adsIds.splashBanner.adKey)
        isPause = false
        if (interstitialAd != null) {
            navigateToNextScreen()
        } else if (delayJob != null && delayJob?.isCancelled!!) {
            waitForAWhile(timeToWait)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isPause = true
        adViewModel.destroyBannerAd(adsIds.splashBanner.adKey)
        adViewModel.destroyInterstitialAd(adsIds.splashInterstitialAd.adKey)
    }

}