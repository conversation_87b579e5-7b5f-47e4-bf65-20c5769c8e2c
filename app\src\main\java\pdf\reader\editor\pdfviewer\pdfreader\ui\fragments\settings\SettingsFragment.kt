package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.settings

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.BuildConfig
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentSettingsBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.manager.language.LanguageData
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.AppLanguageData
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ListActionEnums
import pdf.reader.editor.pdfviewer.pdfreader.shared.PublicValue
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogClearHistory
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.LanguageDialog
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.RatingsDialog

@AndroidEntryPoint
class SettingsFragment : BaseFragment<FragmentSettingsBinding, SettingsViewModel>() {

    override val viewModel: Class<SettingsViewModel>
        get() = SettingsViewModel::class.java
    private var position: Int? = null

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentSettingsBinding = if (mViewModel.view == null)
        FragmentSettingsBinding.inflate(inflater, container, false)
    else
        FragmentSettingsBinding.bind(mViewModel.view!!)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
    }

    private fun initViews() {

        position = storage.getLocalization()
        if (position == null || position == 0)
            position = AppLanguageData.getAppLanguageCodeList().indexOf("en")


        if (position != null && position == 100)
            position = 0

        isDarkThemeSP().let {
            if (it == PublicValue.DARK_THEME) {
                mViewDataBinding.switchDarkMode.isChecked = true
            } else if (it == PublicValue.DEFAULT_THEME) {
                mViewDataBinding.switchDarkMode.isChecked = false
            }
//            theme = it
        }
//
        mViewDataBinding.switchDarkMode.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
                lifecycleScope.launch(Dispatchers.IO) {
                    storage.setPreferredTheme(PublicValue.DARK_THEME)
                }
                requireActivity().recreate()
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                lifecycleScope.launch(Dispatchers.IO) {
                    storage.setPreferredTheme(PublicValue.DEFAULT_THEME)
                }

                requireActivity().recreate()
            }
        }
//
//        mViewDataBinding.imgAppLangPicker.setOnClickListener {
//            showToast("Coming soon")
////            showLanguagePicker()
//        }

        mViewDataBinding.clearHistoryLayout.setOnClickListener {
            DialogClearHistory.getInstance {
                when (it) {
                    ListActionEnums.CLEAR_HISTORY_TRIGGERED -> {
                        try {
                            mViewModel.clearStorage().let {
                                showToast(getString(R.string.clear_history_clear_success_msg))
                            }
                        } catch (ex: Exception) {

                        }
                    }

                    else -> {}
                }
            }.show(childFragmentManager, "cleaHistoryDialog")
        }

        mViewDataBinding.txtLanguage.setOnClickListener {
            var position = storage.preferences.getInt("localization", 0)
            if (position == 0) position = LanguageData.getAppLanguageCodeList().indexOf("en")


            if (position == 100) position = 0
            LanguageDialog.create(position) {
                if (it) {
                    requireActivity().recreate()
                }
            }.show(childFragmentManager, "")
        }

        mViewDataBinding.txtShare.setOnClickListener {

            try {
                val shareIntent = Intent(Intent.ACTION_SEND)
                shareIntent.type = "text/plain"
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, "PDF Reader")
                var shareMessage = "\nLet me recommend you this application\n\n"
                shareMessage =
                    """
                    ${shareMessage}https://play.google.com/store/apps/details?id=${BuildConfig.APPLICATION_ID}
                    
                    """.trimIndent()
                shareIntent.putExtra(Intent.EXTRA_TEXT, shareMessage)
                startActivity(Intent.createChooser(shareIntent, "choose one"))
            } catch (e: java.lang.Exception) {
                //e.toString();
            }
        }

        mViewDataBinding.txtRating.setOnClickListener {
            RatingsDialog.getInstance(isSetting = true) {
            }.show(childFragmentManager, "Rating Dialog")
        }

        mViewDataBinding.txtPolicy.setOnClickListener {
            try {
                val browserIntent = Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("https://pdf-editor.digitalzone.today/privacy-policy.html")
                )
                startActivity(browserIntent)
            } catch (e: java.lang.Exception) {

            }

        }

        mViewDataBinding.imgBack.setOnClickListener {
            isAlive {

                findNavController().popBackStack()
            }
        }
    }

    private fun showLanguagePicker() {
        isAlive {
            var selected = 0
            val builder = AlertDialog.Builder(it)

            if (position == 100)
                position = 0
            builder.setTitle(getString(R.string.select_language))
                .setCancelable(false)
                .setSingleChoiceItems(
                    AppLanguageData.getAppLanguageList().toTypedArray(),
                    position!!
                ) { _, id ->
                    selected = id

                }
                .setPositiveButton(getString(R.string.done)) { _, _ ->
                    if (selected == 0) {
                        selected = 100

                    }
                    storage.setLocalization(selected)
                    position = 100
                    it.recreate()
                }
                .setNegativeButton(getString(R.string.cancel)) { _, _ -> }
                .show()
        }

    }

}