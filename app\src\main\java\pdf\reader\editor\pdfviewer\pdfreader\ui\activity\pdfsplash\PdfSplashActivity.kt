package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.pdfsplash

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Log
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.edit
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.ads.interstitial.InterstitialAd
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivityPdfSplashBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkStoragePermissionGranted
import pdf.reader.editor.pdfviewer.pdfreader.extensions.fromHtml
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getFormattedDate
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getReadableSize
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isVersionGreaterThanEqualTo
import pdf.reader.editor.pdfviewer.pdfreader.extensions.openSettings
import pdf.reader.editor.pdfviewer.pdfreader.extensions.requestPermission
import pdf.reader.editor.pdfviewer.pdfreader.extensions.requestPermissionAndroidR
import pdf.reader.editor.pdfviewer.pdfreader.extensions.shouldShowRationale
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AdUtility.isFromIntentInterOpenFailed
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.getInterstitialVsStartAppAdObject
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.shared.PublicValue
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.PermissionDialog
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import kotlin.system.exitProcess

@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class PdfSplashActivity : AppCompatActivity() {

    private val binding: ActivityPdfSplashBinding by lazy {
        ActivityPdfSplashBinding.inflate(layoutInflater)
    }
    private var timeToWait = 8000L
    private var delayJob: Job? = null
    private var interstitialAd: InterstitialAd? = null
    private var isPause = false
    private var documentModel: DocumentsModel? = null
    private val storage: Storage by lazy {
        Storage(this)
    }
    private val adViewModel by viewModels<AdViewModel>()

    companion object {
        var fromIntent = false
    }

    @Inject
    lateinit var adsIds: AdsIds

    private fun applySystemBarsPadding(targetView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(targetView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= 35) {
            enableEdgeToEdge()
        }
        setContentView(binding.root)
        if (Build.VERSION.SDK_INT >= 35) {
            applySystemBarsPadding(binding.root)
        }
        binding.txtAppName.text = fromHtml(resources.getString(R.string.pdf_reader))
        storage.preferences.edit {
            putBoolean("pdf_splash", false)
        }
        fromIntent = false
        if (intent != null && intent.data != null) {
            logs("PdfSplashActivity::intent.data->oNCreateActivity")
            documentModel = getFileFromUri(intent.data!!)
            checkAndAskPermissions()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        if (intent != null && intent.data != null) {
            logs("PdfSplashActivity::intent.data->onNewIntent")
            documentModel = getFileFromUri(intent.data!!)
            checkAndAskPermissions()
        }
    }

    private fun loadAd() {
        if (checkInternetConnection() && storage.isConsentDone &&
            adViewModel.getRemoteConfigModel().adConfigModel.fileViewInterIntentAd.show
        ) {
            waitForAWhile(timeToWait,"loadAd-if")
            getInterstitialVsStartAppAdObject(
                adsIds.fileViewInterIntentAd.adUnitId,
                onResult = {
                    if (!isPause) {
                        interstitialAd = it
                        delayJob?.cancel()
                        navigateToNextScreen("onResult")
                    }

                },
                onAdClosed = {
                    interstitialAd = null
                    isFromIntentInterOpenFailed = false
                    navigateToNextScreen("onAdClosed")

                },
                onAdLoadFailed = {
                    isFromIntentInterOpenFailed = true
                    interstitialAd = null
                },
            )


        } else waitForAWhile(3000,"loadAd-else")
    }

    private fun checkAndAskPermissions() {
        if (!checkStoragePermissionGranted()) {
            PermissionDialog.getInstance {
                when (it) {
                    ViewPdfActions.RUNTIME_PERMISSION_ALLOW -> {
                        if (isVersionGreaterThanEqualTo(Build.VERSION_CODES.R)) requestPermissionAndroidR()
                        else when {
                            shouldShowRationale(
                                Manifest.permission.WRITE_EXTERNAL_STORAGE
                            ) -> {
                                openSettings()
                            }

                            else -> {
                                requestPermission(
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                    PublicValue.KEY_REQUEST_PERMISSIONS
                                )
                            }
                        }
                    }

                    ViewPdfActions.RUNTIME_PERMISSION_DENAY -> {
                        exitProcess(0)
                    }

                    else -> {}
                }
            }.show(supportFragmentManager, "pdfPassword")
        } else {
            adViewModel.initRemoteConfig { loadAd() }

        }
    }

    private fun waitForAWhile(timeToWait: Long,callString: String) {
        delayJob = lifecycleScope.launch {
            delay(timeToWait)
            navigateToNextScreen(callString)
        }
    }

    private fun navigateToNextScreen(fromString: String) {
        logs("navigateToNextScreen::fromString: $fromString")
        delayJob?.cancel()

        if (interstitialAd != null) {
            interstitialAd?.show(this)
            storage.preferences.edit {
                putBoolean("pdf_splash", true)
            }
        } else {
            logs("ViewerActivity>>navigateToNextScreen")
            delayJob?.cancel()
            fromIntent = true
            val viewerIntent = Intent(this, ViewerActivity::class.java)
            viewerIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            viewerIntent.putExtra("pdf_file", documentModel)
//            viewerIntent.putExtra("fromIntent", true)
            viewerIntent.data = intent.data
            startActivity(viewerIntent)
            finish()
        }
    }

    override fun onPause() {
        super.onPause()
        delayJob?.cancel()

    }

    override fun onResume() {
        super.onResume()
        isPause = false
        if (interstitialAd != null) {
            logs("viewerActivity>>onResume")
            navigateToNextScreen("onResume")
        } else if (delayJob != null && delayJob?.isCancelled!!) {
            waitForAWhile(timeToWait,"onResume")
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        isPause = true
    }

    private fun getFileFromUri(uri: Uri): DocumentsModel? {
        var documentModel: DocumentsModel? = null
        try {

            uri.scheme?.let { scheme ->


                if (scheme == "file") {
                    val path = uri.path!!
                    val file = File(path)
                    val name = file.nameWithoutExtension
                    val date = file.lastModified() / 1000 //convert to seconds
                    val size = file.length()
                    documentModel = DocumentsModel()
                    documentModel?.fileName = name
                    documentModel?.absolutePath = path
                    documentModel?.fileSize = size.getReadableSize()
                    documentModel?.dateInDigit = date
                    documentModel?.fileDate = date.getFormattedDate()
                    documentModel?.parentFile = file.parent!!
                } else {
                    val pdfExt = "_data LIKE '%.pdf'"
                    var cursor: Cursor? = null
                    val docsProjection = arrayOf(MediaStore.Files.FileColumns.DATA)
                    cursor = contentResolver.query(
                        uri, docsProjection, pdfExt, null, null
                    )

                    if (cursor != null && cursor?.columnCount!! > 0) {

                        while (cursor?.moveToNext() == true) {
                            documentModel = try {
                                val path =
                                    cursor?.getString(cursor?.getColumnIndex(MediaStore.Files.FileColumns.DATA)!!)
                                Log.d("TAG", "getFileFromUri: get file try $path")
                                if (path == null) {
                                    val path =
                                        copyFileToInternalStorage(uri, "filesWithoutExtension")
                                    getPDFFile(path)
                                } else getPDFFile(path)
                            } catch (ee: java.lang.Exception) {
                                val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                                Log.d("TAG", "getFileFromUri: get file catch")
                                getPDFFile(path)
                            }
                        }
                        cursor?.close()
                    } else {
                        val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                        documentModel = getPDFFile(path)
                    }
                }
            }
            return documentModel
        } catch (ee: java.lang.Exception) {
            return null
        }
    }

    private fun copyFileToInternalStorage(
        uri: Uri,
        newDirName: String,
    ): String {
        val returnCursor = contentResolver?.query(
            uri, arrayOf(OpenableColumns.DISPLAY_NAME, OpenableColumns.SIZE), null, null, null
        )/*
         * Get the column indexes of the data in the Cursor,
         *     * move to the first row in the Cursor, get the data,
         *     * and display it.
         * */
        if (returnCursor?.columnCount ?: 0 > 0) {
            val column_index = returnCursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME) ?: -1
            if (returnCursor?.moveToFirst() == true && column_index >= 0) {
                val name = returnCursor.getString(column_index)
                val output: File = if (newDirName != "") {
                    val dir = File("${filesDir}/$newDirName")
                    if (!dir.exists()) {
                        dir.mkdir()
                    }
                    File("${filesDir}/$newDirName/$name")
                } else {
                    File("${filesDir}/$name")
                }
                try {
                    val inputStream = contentResolver?.openInputStream(uri)
                    val outputStream = FileOutputStream(output)
                    var read: Int
                    val bufferSize = 1024
                    val buffers = ByteArray(bufferSize)
                    while (inputStream!!.read(buffers).also { read = it } != -1) {
                        outputStream.write(buffers, 0, read)
                    }
                    inputStream.close()
                    outputStream.close()
                } catch (e: Exception) {
                    Log.e("error", e.message!!)
                }
                return output.path
            }
        }
        return ""
    }

    private fun getPDFFile(path: String?): DocumentsModel? {
        return if (path != null) {
            val name: String?
            val date: Long?
            val size: Long?
            val file = File(path)
            name = file.nameWithoutExtension
            date = file.lastModified() / 1000 //convert to seconds
            size = file.length()
            DocumentsModel(
                name,
                size.getReadableSize(),
                fileDate = date.getFormattedDate(),
                parentFile = file.parent!!,
                absolutePath = path,
                dateInDigit = date,
                sizeInDigit = size
            )
        } else null
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<out String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PublicValue.KEY_REQUEST_PERMISSIONS) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                checkAndAskPermissions()
            } else {
                exitProcess(0)

            }

        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PublicValue.KEY_REQUEST_PERMISSIONS_11) {

            if (checkStoragePermissionGranted()) {
                showToast("Permission allowed, thank you.")
                checkAndAskPermissions()
            } else {
                exitProcess(0)
            }


        }
    }

}