package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.settings

import android.view.View
import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val repositoryLocal: RepositoryLocal
) : BaseViewModel() {
    var view: View? = null
    fun clearStorage() {
        repositoryLocal.clearStorage()
    }
}