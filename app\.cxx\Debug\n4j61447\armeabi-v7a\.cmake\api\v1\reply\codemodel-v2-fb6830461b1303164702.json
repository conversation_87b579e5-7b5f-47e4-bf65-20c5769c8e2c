{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "MuPDF_Project", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "adnan::@6890427a1f51a3e7e1df", "jsonFile": "target-adnan-Debug-e0f3016454bc92f73d24.json", "name": "<PERSON>nan", "projectIndex": 0}, {"directoryIndex": 0, "id": "mupdfcore::@6890427a1f51a3e7e1df", "jsonFile": "target-mupdfcore-Debug-68e4b1c9b7104b4a31e6.json", "name": "mupdfcore", "projectIndex": 0}, {"directoryIndex": 0, "id": "mupdfthirdparty::@6890427a1f51a3e7e1df", "jsonFile": "target-mupdfthirdparty-Debug-c5e1703b3fff24f70b55.json", "name": "mupdfthirdparty", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "source": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni"}, "version": {"major": 2, "minor": 3}}