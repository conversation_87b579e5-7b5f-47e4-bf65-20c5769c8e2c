package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.webtopdf

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.navigation.fragment.findNavController
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.WebToPdfFragmentBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.managepdf.SavePdfChanges
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName


class WebToPdfFragment : BaseFragment<WebToPdfFragmentBinding, WebToPdfViewModel>() {

    override val viewModel: Class<WebToPdfViewModel>
        get() = WebToPdfViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): WebToPdfFragmentBinding = if (mViewModel.view == null)
        WebToPdfFragmentBinding.inflate(inflater, container, false)
    else
        WebToPdfFragmentBinding.bind(mViewModel.view!!)

    private var pdfName: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        loadingDialog.show()
        mViewDataBinding.editor.loadUrl(sharedViewModel.getWebURL().toString()).let {

        }

        mViewDataBinding.editor.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView, urlNewString: String): Boolean {
                loadingDialog.show()
                mViewDataBinding.editor.loadUrl(urlNewString)
                mViewDataBinding.exportBtn.isClickable = false
                return true
            }

            override fun onPageFinished(view: WebView, url: String) {
                if (mViewDataBinding.editor.progress == 100) {
                    loadingDialog.dismiss()
                    mViewDataBinding.exportBtn.isClickable = true
                }
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                isAlive {
                    loadingDialog.dismiss()
                    showToast(getString(R.string.txt_invalid_link))
                    findNavController().popBackStack(R.id.webLinkFragment, false)
                    super.onReceivedError(view, request, error)
                }

            }


        }

        mViewDataBinding.exportBtn.setOnClickListener {
            try {
                DialogNewPdfName.getInstance(pdfName) { action, fileName ->
                    when (action) {
                        ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                            pdfName = fileName
                            exportPdf(fileName)
                        }
                        ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_special_charac),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.INVALID_EXTENSION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_invalid_extension),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.NULL_PDF_PASSWORD -> {
                            showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                        }

                        else -> {}
                    }
                }.show(childFragmentManager, "newFileDialog")


            } catch (ex: Exception) {
                activity?.runOnUiThread {
                    loadingDialog.dismiss()
                    showToast(ex.localizedMessage!!)
                }

            }
        }

        mViewDataBinding.imgBack.setOnClickListener {
            isAlive {

                findNavController().popBackStack()
            }
        }
    }

    private fun exportPdf(name: String) {
//        val url: URL = URL("https://www.${sharedViewModel.getWebURL()}")
        //val url : URL = URL(sharedViewModel.getWebURL())
        loadingDialog.show()
        val savePath = FileUtils.saveFileToExternalStorage(name)
//        val dir = File(savePath)
//        if (!dir.exists()) {
//            dir.mkdirs()
//        }
        Log.d("TAG", "exportPdf: ${savePath?.path} \n ${savePath?.name}")

        isAlive {
            SavePdfChanges.save(
                mViewDataBinding.editor,
                it,
                savePath!!
            ) { _, _ ->
                activity?.sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            savePath
                        )
                    )
                )
                isAlive { activity ->
                    sharedViewModel.loadFiles(requireActivity())
                    loadingDialog.dismiss()
                    findNavController().popBackStack(R.id.homeFragment, false)
                }

            }
        }


    }

//    @Throws(IOException::class)
//    fun createPdf(url: URL, dest: String?) {
//        val writer = PdfWriter(dest)
//        val pdf = PdfDocument(writer)
//        val pageSize = PageSize(850F, 1700F)
//        pdf.setDefaultPageSize(pageSize)
//        val properties = ConverterProperties()
//        val mediaDeviceDescription = MediaDeviceDescription(MediaType.SCREEN)
//        mediaDeviceDescription.width = pageSize.getWidth()
//        properties.mediaDeviceDescription = mediaDeviceDescription
//        HtmlConverter.convertToPdf(url.openStream(), pdf, properties)
//    }


}