package com.artifex.mupdfdemo

import android.graphics.ColorMatrix

enum class ColorType(val matrix: ColorMatrix? = null) {
    WHITE, GREY(
        ColorMatrix(
            floatArrayOf(
                0.74f, 0.0f, 0.0f, 0.0f, 0f,
                0.0f, 0.74f, 0.0f, 0.0f, 0f,
                0.0f, 0.0f, 0.74f, 0.0f, 0f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            )
        )
    ),
    YELLOW(
        ColorMatrix(
            floatArrayOf(
                1.0f, 0.0f, 0.0f, 0.0f, 0.0f,
                0.0f, 0.98f, 0.0f, 0.0f, 0.0f,
                0.0f, 0.0f, 0.77f, 0.0f, 0.0f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            )
        )
    ),
    BLACK(
        ColorMatrix(
            floatArrayOf(
                -1.0f, 0.0f, 0.0f, 0.0f, 255.0f,
                0.0f, -1.0f, 0.0f, 0.0f, 255.0f,
                0.0f, 0.0f, -1.0f, 0.0f, 255.0f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            )
        )
    );

}