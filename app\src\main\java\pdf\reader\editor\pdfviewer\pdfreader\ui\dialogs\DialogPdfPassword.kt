package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogPdfPasswordLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions

class DialogPdfPassword : BaseDialog<DialogPdfPasswordLayoutBinding>() {

    companion object {
        //arguments
        private var callback: ((ViewPdfActions, String) -> Unit)? = null

        fun getInstance(
            callback: ((ViewPdfActions, String) -> Unit)
        ): DialogPdfPassword {
            this.callback = callback
            return DialogPdfPassword()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Handler(Looper.getMainLooper()).postDelayed({
            view.showKeyboard()
            binding.edtPassword.requestFocus()
        }, 500)
        isCancelable = false

        binding.btnOpen.setOnClickListener {
            val pass: String = binding.edtPassword.text?.trim().toString()
            if (pass.isEmpty()) {
                callback?.invoke(ViewPdfActions.NULL_PDF_PASSWORD, "")
            } else {
                callback?.invoke(
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED,
                    binding.edtPassword.text.toString()
                )
                it.hideKeyboard()
                dismiss()
            }


        }
        binding.btnCancel.setOnClickListener {
            callback?.invoke(ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED, "")
            it.hideKeyboard()
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        setTopAnimation()
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogPdfPasswordLayoutBinding =
        DialogPdfPasswordLayoutBinding.inflate(LayoutInflater.from(requireContext()), null, false)


}