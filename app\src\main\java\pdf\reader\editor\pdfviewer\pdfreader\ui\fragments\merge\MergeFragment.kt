package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.merge

import android.content.Intent
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.itextpdf.kernel.crypto.BadPasswordException
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfReader
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.kernel.pdf.ReaderProperties
import com.itextpdf.kernel.utils.PdfMerger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentMergeBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getFormattedDate
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getReadableSize
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showCreateSheet
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.tool.ToolsActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogClass
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.merge.adapter.MergeAdapter

@AndroidEntryPoint
class MergeFragment :
    BaseFragment<FragmentMergeBinding, MergeViewModel>() {
    override val viewModel: Class<MergeViewModel> = MergeViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMergeBinding =
        FragmentMergeBinding.inflate(inflater, container, false)

    private var mergeAdapter: MergeAdapter? = null
    private var selectedFiles: ArrayList<DocumentsModel> = ArrayList()
    private var fileName: String? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        mViewDataBinding.imgBack.setOnClickListener {
            if (activity != null && activity is ToolsActivity)
                activity?.finish()
            else
                isAlive {
                    findNavController().popBackStack()
                }
        }

        mViewDataBinding.btnMergeFiles.setOnClickListener {
            if (selectedFiles.size > 1) {
                DialogNewPdfName.getInstance(fileName) { action, fileName ->
                    when (action) {
                        ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                            this.fileName = fileName
                            mergePDF()
                        }

                        ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_special_charac),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.INVALID_EXTENSION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_invalid_extension),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.NULL_PDF_PASSWORD -> {
                            showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                        }

                        else -> {}
                    }
                }.show(childFragmentManager, "Merge Files")
            } else
                showToast(getString(R.string.please_select_files_to_merge))
        }
    }

    private fun initViews() {
        mergeAdapter = MergeAdapter(0) { selectedFiles ->
            this.selectedFiles = selectedFiles
            if (selectedFiles.isNotEmpty())

                try {
                    if (FileUtils.isProtectedFile(selectedFiles.last().absolutePath)) {
                        getPasswordDialog()
                    }

                } catch (e: BadPasswordException) {
                    getPasswordDialog()
//                    showToast("Protected files can't be merged yet.")
//                    mergeAdapter?.removeDocument(selectedFiles.last(), requireContext())
                } catch (e: Exception) {
                    showToast(getString(R.string.file_is_corrupt_or_not_pdf))
                    isAlive {
                        mergeAdapter?.removeDocument(selectedFiles.last(), it)
                    }

                }
        }

        isAlive {
            mViewDataBinding.mergeRecyclerView.apply {
                layoutManager = LinearLayoutManager(it)
                adapter = mergeAdapter
            }

            if (sharedViewModel.getDocuments().value != null
            ) {
                mergeAdapter?.setData(sharedViewModel.getDocuments().value!!)

            } else {
                sharedViewModel.loadFiles(requireActivity())
                sharedViewModel.getDocuments().observe(viewLifecycleOwner) { files ->
                    if (files != null) {
                        mergeAdapter?.setData(files)

                    }
                }
            }
            loadingDialog = DialogClass.loadingDialog(it)
        }


//        mViewModel.documentsList.observe(viewLifecycleOwner) { files ->
//            if (files != null) {
//                mergeAdapter?.setData(files)
//
//            }
//        }


    }

    private fun getPasswordDialog() {
        DialogPdfPassword.getInstance { action, password ->
            when (action) {
                ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                    try {
                        PdfDocument(
                            PdfReader(
                                selectedFiles.last().absolutePath,
                                ReaderProperties().setPassword(password.toByteArray())
                            )
                        )
                        this.selectedFiles.last().password = password

                    } catch (e: BadPasswordException) {
                        getPasswordDialog()
                    }

                }
                ViewPdfActions.NULL_PDF_PASSWORD -> {
                    showToast(getString(R.string.txt_pdf_null_password_warning))
                }
                ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {

                    isAlive {
                        mergeAdapter?.removeDocument(selectedFiles.last(), it)
                    }


                }

                else -> {}
            }
        }.show(childFragmentManager, "Password Dialog")
    }

    private fun mergePDF() {
        isAlive { activity ->
            if (selectedFiles.isNotEmpty() && selectedFiles.size >= 2) {
                lifecycleScope.launch((Dispatchers.IO)) {
                    activity.runOnUiThread {
                        loadingDialog.show()
                    }
                    try {


                        val file = FileUtils.saveFileToCache(activity)
                        val reader = PdfReader(
                            selectedFiles[0].absolutePath,
                            ReaderProperties().setPassword(selectedFiles[0].password.toByteArray())
                        )
                        reader.setUnethicalReading(true)
                        val pdfDocument = PdfDocument(
                            reader,
                            PdfWriter(file)
                        )

                        selectedFiles.forEachIndexed { index, fileModel ->
                            if (index != 0) {
                                val red = PdfReader(
                                    fileModel.absolutePath,
                                    ReaderProperties().setPassword(fileModel.password.toByteArray())
                                )
                                red.setUnethicalReading(true)
                                val secondDoc = PdfDocument(
                                    red
                                )
                                val merger = PdfMerger(pdfDocument)
                                merger.merge(secondDoc, 1, secondDoc.numberOfPages)
                                secondDoc.close()
                            }
                        }
                        pdfDocument.close()
                        val splitFile = FileUtils.saveFileToExternalStorage(fileName!!)
                        file.copyTo(splitFile!!)
                        <EMAIL>?.sendBroadcast(
                            Intent(
                                Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                                Uri.fromFile(splitFile)
                            )
                        )

                        <EMAIL>?.runOnUiThread {
                            loadingDialog.dismiss()
                            MediaScannerConnection.scanFile(
                                activity,
                                arrayOf(splitFile.path),
                                null
                            ) { _, _ ->
                                try {

                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }

                            if (file.exists()) {
                                file.delete()
                                file.canonicalFile.delete()
                                if (file.exists()) activity.deleteFile(file.name)
                            }

                            showToast(getString(R.string.documents_are_merged))
                            sharedViewModel.loadFiles(requireActivity())

                            <EMAIL>?.showCreateSheet(splitFile.absolutePath) { flag ->
                                if (flag) {
                                    isAlive { con ->
                                        val doc = DocumentsModel()
                                        doc.absolutePath = splitFile.absolutePath
                                        doc.fileName = splitFile.name
                                        doc.parentFile = splitFile.parent
                                        doc.sizeInDigit = splitFile.length()
                                        doc.dateInDigit = splitFile.lastModified()
                                        doc.fileDate =
                                            con.getFormattedDate(splitFile.lastModified())
                                        doc.fileSize = con.getReadableSize(splitFile.length())

                                        sharedViewModel.selectedDoc = doc
                                        isAlive { activity ->
                                            val intent = Intent(activity, ViewerActivity::class.java)
                                            intent.flags =
                                                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                                            intent.putExtra("pdf_file", doc)
                                            activity.startActivity(intent)
                                            if (activity is ToolsActivity)
                                                activity.finish()
                                            else
                                                findNavController().popBackStack()

                                        }
                                      /*  if (isFragmentInBackStack(R.id.action_homeFragment_to_pdfViewFragment)) {
                                            findNavController().popBackStack()
                                        } else {
                                            val action = MergeFragmentDirections
                                                .actionMergeFragmentToPdfViewFragment()
                                            findNavController().navigate(action)
                                        }*/
                                    }
                                } else
                                    isAlive {
                                        findNavController().popBackStack()
                                    }


                            }

                        }


                    } catch (e: FileAlreadyExistsException) {
                        <EMAIL>?.runOnUiThread {
                            showToast(getString(R.string.file_already_exist_with_this_name))
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        <EMAIL>?.runOnUiThread {
                            loadingDialog.dismiss()
                            showToast(getString(R.string.failed_to_merge))
                            <EMAIL>?.sendFirebaseLog(
                                "merge_screen",
                                "Merge Exception"
                            )
                        }
                    }
                }


            }
        }

    }
}