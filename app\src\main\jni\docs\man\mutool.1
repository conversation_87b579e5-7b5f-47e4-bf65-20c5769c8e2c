.TH "MUTOOL" "1" "May 25, 2015"
.\" Please adjust this date whenever revising the manpage.
.\" no hyphenation
.nh
.\" adjust left
.ad l

.SH NAME
mutool \- all purpose tool for dealing with PDF files

.SH SYNOPSIS
mutool <sub-command> [options]

.SH DESCRIPTION
mutool is a tool based on MuPDF for dealing with PDF files in various manners.
There are several sub commands available, as described below.

.SH DRAW
mudraw [options] file [pages]

The draw command will render a document to image files,
convert to another vector format, or extract the text content.

The supported input document formats are: pdf, xps, cbz, and epub.

The supported output image formats are: pbm, pgm, ppm, pam, png, tga, pwg, and pcl.
The supported output vector formats are: svg, pdf, and debug trace (as xml).
The supported output text formats are: plain text, html, and structured text (as xml).

.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B \-o output
The output format is inferred from the output file name.
Embed %d in the name to indicate the page number (for example: "page%d.png").
Printf modifiers are supported, for example "%03d".
If no output is specified, the output will go to stdout.
.TP
.B \-F format
Enforce a specific output format. Use this when outputting to stdout.
.TP
.B \-R angle
Rotate clockwise by given number of degrees.
.TP
.B \-r resolution
Render the page at the specified resolution.
The default resolution is 72 dpi.
.TP
.B \-w width
Render the page at the specified width (or, if the -r flag is used,
render with a maximum width).
.TP
.B \-h height
Render the page at the specified height (or, if the -r flag is used,
render with a maximum height).
.TP
.B \-f
Fit exactly; ignore the aspect ratio when matching specified width/heights.
.TP
.B \-W width
Page width in points for EPUB layout.
.TP
.B \-H height
Page height in points for EPUB layout.
.TP
.B \-S size
Font size in points for EPUB layout.
.TP
.B \-U filename
User CSS stylesheet for EPUB layout.
.TP
.B \-c colorspace
Render in the specified colorspace.
Supported colorspaces are: mono, gray, grayalpha, rgb, rgbalpha, cmyk, cmykalpha.
Some abbreviations are allowed: m, g, ga, rgba, cmyka.
The default is chosen based on the output format.
.TP
.B -G gamma
Apply gamma correction.
Some typical values are 0.7 or 1.4 to thin or darken text rendering.
.TP
.B -I
Invert colors.
.TP
.B \-s [mft5]
Show various bits of information:
memory use,
features,
timings, and/or
md5 checksum.
.TP
.B \-A bits
Specify how many bits of anti-aliasing to use. The default is 8.
.TP
.B \-D
Disable use of display lists. May cause slowdowns, but should reduce
the amount of memory used.
.B \-i
Ignore errors.
.TP
.B pages
Comma separated list of page numbers and ranges (for example: 1,5,10-15).
If no pages are specified, then all pages will be rendered.

.SH CLEAN
mutool clean [options] input.pdf [output.pdf] [pages]
.PP
The clean command pretty prints and rewrites the syntax of a PDF file.
It can be used to repair broken files, expand compressed streams, filter
out a range of pages, etc.
.PP
If no output file is specified, it will write the cleaned PDF to "out.pdf"
in the current directory.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B \-g
Garbage collect objects that have no references from other objects.
Give the option twice to renumber all objects and compact the cross reference table.
Give it three times to merge and reuse duplicate objects.
.TP
.B \-s
Rewrite content streams.
.TP
.B \-d
Decompress streams. This will make the output file larger, but provides
easy access for reading and editing the contents with a text editor.
.TP
.B \-l
Linearize output. Create a "Web Optimized" output file.
.TP
.B \-i
Toggle decompression of image streams. Use in conjunction with -d to leave
images compressed.
.TP
.B \-f
Toggle decompression of font streams. Use in conjunction with -d to leave
fonts compressed.
.TP
.B \-a
ASCII Hex encode binary streams. Use in conjuction with -d and -i or -f to
ensure that although the images and/or fonts are compressed, the resulting
file can still be viewed and edited with a text editor.
.TP
.B \-z
Deflate uncompressed streams.
If combined with -d, any decompressed streams will be recompressed.
If combined with -a, the streams will also be hex encoded after compression.
.TP
.B pages
Comma separated list of page numbers and ranges to include.

.SH EXTRACT
mutool extract [options] file.pdf [object numbers]
.PP
The extract command can be used to extract images and font files from a PDF.
If no object numbers are given on the command line, all images and fonts
will be extracted.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B \-r
Convert images to RGB when extracting them.

.SH INFO
mutool info [options] file.pdf [pages]
.PP
The info command lists the resources used on each page in a PDF file.
The default is to list all resource types, but if one
or more flags are given, only the flagged types will be shown.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B -F
List fonts.
.TP
.B -I
List images.
.TP
.B -M
List page dimensions.
.TP
.B -S
List shadings.
.TP
.B -P
List patterns.
.TP
.B -X
List form and postscript XObjects.
.TP
.B pages
Comma separated list of page numbers and ranges to include.

.SH PAGES
mutool pages [options] input.pdf [pages ...]
.PP
The pages command dumps information about the size and orientation
of pages within the document.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B pages
Comma separated list of page numbers and ranges to include.

.SH POSTER
mutool poster [options] input.pdf [output.pdf]
.PP
The poster command splits each page into tiles, and puts each tile on
a page of its own. It's useful for printing a large page onto smaller
pieces of paper that can then be glued together to create a large poster.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B \-x factor
Split the page into this many horizontal pieces.
.TP
.B \-y factor
Split the page into this many vertical pieces.
.PP
The output will have x times y number of pages for each input page.
.SH SHOW
mutool show [options] file.pdf [object numbers ...]
.PP
The show command will print the specified objects and streams to stdout.
Streams are decoded and non-printable characters are represented
with a period by default.
.TP
.B \-p password
Use the specified password if the file is encrypted.
.TP
.B \-o file
Write output to file instead of stdout.
.TP
.B \-b
Print streams as binary data and omit the object header.
.TP
.B \-e
Print streams in their original encoded (or compressed) form.
.PP
Specify objects by number, or use one of the following special names:
.TP
.B 'xref' or 'x'
Print the cross reference table.
.TP
.B 'trailer' or 't'
Print the trailer dictionary.
.TP
.B 'pagetree' or 'p'
List the object numbers for every page.
.TP
.B 'grep' or 'g'
Print all the objects in the file in a compact one-line format suitable for piping to grep.
.TP
.B 'outline' or 'o'
Print the outline (table of contents).

.SH SEE ALSO
.BR mupdf (1),

.SH AUTHOR
MuPDF is Copyright 2006-2015 Artifex Software, Inc.
