package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.sort.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil.load
import coil.transform.RoundedCornersTransformation
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.SplitItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide

class SortAdapter :
    RecyclerView.Adapter<SortAdapter.ViewHolder>() {

    private var allPagesList: ArrayList<PhotoModel> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding: SplitItemLayoutBinding =
            SplitItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val page = allPagesList[position]
        holder.binding.apply {
            imgThumbnail.load(page.bitmap) {
                crossfade(true)
                crossfade(700)
                transformations(RoundedCornersTransformation(radius = 10F))
            }
            txtPageNumber.text = (page.position + 1).toString()

            imgSelected.hide()
        }
    }

    fun setData(model: PhotoModel, position: Int) {
        this.allPagesList.add(model)
        notifyItemInserted(position)
    }

    fun swap(from: Int, to: Int) {
        val fromItem = allPagesList[from]
        allPagesList.remove(fromItem)
        allPagesList.add(to, fromItem)
    }

    override fun getItemCount(): Int = allPagesList.size

    class ViewHolder(val binding: SplitItemLayoutBinding) : RecyclerView.ViewHolder(binding.root)
}