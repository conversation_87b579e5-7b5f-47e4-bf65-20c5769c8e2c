package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentRecentPdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.startAppActivity
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.PDFFileActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity.Companion.isFromAssetViewer
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf.adapter.RecentPdfAdapter

@AndroidEntryPoint
class RecentPdfFragment : BaseFragment<FragmentRecentPdfBinding, RecentPdfViewModel>() {
    companion object {
        var isFromRecentAsset: Boolean = false
    }

    override val viewModel: Class<RecentPdfViewModel>
        get() = RecentPdfViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentRecentPdfBinding {
        return if (mViewModel.view == null) {
            mViewModel.view = inflater.inflate(R.layout.fragment_recent_pdf, container, false)
            FragmentRecentPdfBinding.bind(mViewModel.view!!)
        } else {
            FragmentRecentPdfBinding.bind(mViewModel.view!!)
        }
    }

    private var fileAdapter: RecentPdfAdapter? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (mViewModel.isFirstTime)
            initViews()

    }

    private fun initViews() {
        mViewModel.isFirstTime = false
        isAlive {
            fileAdapter = RecentPdfAdapter(
                requireActivity(),
                isFromRecentAsset = isFromRecentAsset
            ) { action, file ->
                when (action) {
                    PDFFileActions.COMPLETE -> {

                    }

                    PDFFileActions.MERGE -> {
                        isAlive {
                            sharedViewModel.selectedDoc = file
                            val mergeAction =
                                RecentPdfFragmentDirections.actionRecentPdfFragmentToMergeFragment()
                            findNavController().navigate(mergeAction)
                        }
                    }

                    PDFFileActions.SPLIT -> {
                        isAlive {
                            sharedViewModel.selectedDoc = file
                            val splitAction =
                                RecentPdfFragmentDirections.actionRecentPdfFragmentToSplitFragment()
                            findNavController().navigate(splitAction)
                        }

                    }

                    PDFFileActions.ANNOTATION -> {
                        isAlive {
                            sharedViewModel.selectedDoc = file
                            val annotAction =
                                RecentPdfFragmentDirections.actionRecentPdfFragmentToPdfViewFragment()
                            annotAction.isEdit = true
                            findNavController().navigate(annotAction)
                        }
                    }

                    PDFFileActions.SELECT -> {
                        isAlive {
                            sharedViewModel.selectedDoc = file
//                            val annotAction =
//                                RecentPdfFragmentDirections.actionRecentPdfFragmentToPdfViewFragment()
//                            findNavController().navigate(annotAction)
                            isFromAssetViewer = isFromRecentAsset
                            activity?.startAppActivity<ViewerActivity>(Pair("pdf_file", file))
                        }
                    }
                }
//                isAlive {
//                    sharedViewModel.selectedDoc = doc
//                    val action =
//                        RecentPdfFragmentDirections.actionRecentPdfFragmentToPdfViewFragment()
//                    findNavController().navigate(action)
//                }

            }

            mViewDataBinding.apply {
                btnBack.setOnClickListener {
                    isAlive {

                        findNavController().popBackStack()
                    }
                }
                recyclerViewRecentPdfFiles.apply {
                    adapter = fileAdapter
                    layoutManager = LinearLayoutManager(it)
                }

            }
        }


//        mViewModel.getRecentFilesList {
//            fileAdapter?.setData(it)
//        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getAllRecentFiles().observe(viewLifecycleOwner) { files ->
            Log.d("TAG", "initViews: recent")
            if (files != null)
                fileAdapter?.setData(files)
            else
                fileAdapter?.setData(emptyList())

        }
    }


}