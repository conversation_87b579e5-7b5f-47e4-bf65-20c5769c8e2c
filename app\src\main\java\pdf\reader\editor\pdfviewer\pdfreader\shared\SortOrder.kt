package pdf.reader.editor.pdfviewer.pdfreader.shared

class SortOrder {

    interface FileSortOrder {

        companion object {

            /* File sort order A-Z */
            const val FILE_A_Z = "Ascending"

            /* File sort order Z-A */
            const val FILE_Z_A = "Descending"

            /* File sort order date */
            const val FILE_DATE_A = "DateAsc"

            /* File sort order date */
            const val FILE_DATE_D = "DateDesc"

            /* File sort order size  ascending*/
            const val FILE_SIZE_A = "SizeAsc"

            /* File sort order size descending*/
            const val FILE_SIZE_Z = "SizeDesc"

        }
    }
}