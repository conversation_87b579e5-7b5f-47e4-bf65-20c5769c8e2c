package pdf.reader.editor.pdfviewer.pdfreader

import android.app.Activity
import androidx.annotation.Keep
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import pdf.reader.editor.pdfviewer.pdfreader.newads.isNetworkAvailable
import pdf.reader.editor.pdfviewer.pdfreader.newads.remote.RemoteConstants

import timber.log.Timber

@Keep
enum class UpdateType(type: Int) {
    FLEXIBLE(0),
    IMMEDIATE(1)


}

class InAppUpdateManager(private val activity: Activity) {
    private val appUpdateManager: AppUpdateManager = AppUpdateManagerFactory.create(activity)
    var updateType = UpdateType.FLEXIBLE
    private val listener: InstallStateUpdatedListener =
        InstallStateUpdatedListener { installState ->
            if (installState.installStatus() == InstallStatus.DOWNLOADED) {
                //Steps 5
                showDialogForCompleteUpdate()
            }
            if (installState.installStatus() == InstallStatus.FAILED) {
                if (checkIfImmediateRunning()) {
                    closeScreen?.invoke()
                }
            }
            if (installState.installStatus() == InstallStatus.CANCELED) {
                if (checkIfImmediateRunning()) {
                    closeScreen?.invoke()
                }
            }

        }

    fun configure(block: InAppUpdateManager.() -> Unit) {
        block()
    }

    private var moveNext: (() -> Unit)? = null
    private var closeScreen: (() -> Unit)? = null

    fun callback(moveNext: () -> Unit, closeScreen: () -> Unit) {
        this.moveNext = moveNext
        this.closeScreen = closeScreen
    }

    private fun getPrefValue() =
        TinyDB.getInstance(activity)?.getLong(RemoteConstants.IN_APP_UPDATE, 1) ?: 1


    companion object {
        const val TAG_INAPP = "AdsTag"
        const val REQUEST_CODE = 333

        inline fun AppCompatActivity.inAppUpdate(init: InAppUpdateManager.() -> Unit) {
            val inAppUpdateManager = InAppUpdateManager(this)
            inAppUpdateManager.init()
            inAppUpdateManager.checkUpdate()
            lifecycle.addObserver(object : LifecycleObserver {
                @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
                fun onDestroy() {
                    Timber.tag(TAG_INAPP).d("onDestroy: inAppUpdate")
                    inAppUpdateManager.unregisterListener()
                }
            })
        }
    }

    private fun showDialogForCompleteUpdate() {
        try {

            if (!activity.isFinishing) {
                val builder1: AlertDialog.Builder =
                    AlertDialog.Builder(activity)
                builder1.setMessage("An update has just been downloaded.")
                builder1.setCancelable(false)

                builder1.setPositiveButton(
                    "RESTART"
                ) { dialog, _ ->
                    //        Steps 6
                    appUpdateManager.completeUpdate()
                    if (checkIfImmediateRunning()) {
                        moveNext?.invoke()
                    }
                    dialog.dismiss()
                }

                builder1.setNegativeButton(
                    "No"
                ) { dialog, _ ->
                    if (checkIfImmediateRunning()) {
                        closeScreen?.invoke()
                    }
                    dialog.dismiss()
                }

                val alert11: AlertDialog = builder1.create()
                alert11.show()
            }
        } catch (ex: Exception) {
            appUpdateManager.completeUpdate()
        }
    }

    private fun checkIfImmediateRunning() = getPrefValue() == 2L

    fun checkUpdate() {

        if (activity.isNetworkAvailable().not()) {
            moveNext?.invoke()
            return
        }
        if (getPrefValue() == 0L) {
            moveNext?.invoke()
            return
        }
        Timber.tag(TAG_INAPP).d("updateType: %s", updateType)
        // Returns an intent object that you use to check for an update.
        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
        // Checks that the platform will allow the specified type of update.
        Timber.tag(TAG_INAPP).d("Checking for updates")
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                && appUpdateInfo.isUpdateTypeAllowed(updateType.ordinal)
            ) {
                requestAppUpdate(appUpdateInfo)
                // Request the update.
                Timber.tag(TAG_INAPP).d("Update available")
            } else {
                if ((appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_NOT_AVAILABLE) or
                    (appUpdateInfo.updateAvailability() == UpdateAvailability.UNKNOWN)
                ) {
                    Timber.tag(TAG_INAPP).d("No Update available")
                    moveNext?.invoke()
                }

            }
        }
        appUpdateInfoTask.addOnFailureListener {
            Timber.tag(TAG_INAPP).d("Exception: " + it.message)
//            closeScreen?.invoke()
            moveNext?.invoke()

        }

    }

    private fun requestAppUpdate(appUpdateInfo: AppUpdateInfo) {
        try {
            appUpdateManager.registerListener(listener)
            appUpdateManager.startUpdateFlowForResult(
                appUpdateInfo,
                updateType.ordinal,
                activity,
                REQUEST_CODE
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun unregisterListener() {
        Timber.tag(TAG_INAPP).d("unregisterListener: appUpdateManager")
        appUpdateManager.unregisterListener(listener)
    }

}