{"buildFiles": ["C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"adnan::@6890427a1f51a3e7e1df": {"artifactName": "<PERSON>nan", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\build\\intermediates\\cxx\\Debug\\6m3e1q4f\\obj\\arm64-v8a\\libadnan.so", "runtimeFiles": []}, "mupdfcore::@6890427a1f51a3e7e1df": {"artifactName": "mupdfcore", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\arm64-v8a\\libmupdfcore.a", "runtimeFiles": []}, "mupdfthirdparty::@6890427a1f51a3e7e1df": {"artifactName": "mupdfthirdparty", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\arm64-v8a\\libmupdfthirdparty.a", "runtimeFiles": []}}}