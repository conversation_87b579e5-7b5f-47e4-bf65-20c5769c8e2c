package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogRenamePdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.local.database.entity.PdfStoreItems
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.inject.Inject

@AndroidEntryPoint
class DialogRenamePdf : BaseDialog<DialogRenamePdfBinding>() {

    companion object {
        //arguments
        private var pdfTitle: String? = null
        private var documentsModel: DocumentsModel? = null
        private var callback: ((ViewPdfActions, String) -> Unit)? = null
        fun getInstance(
            pdfTitle: String,
            documentsModel: DocumentsModel,
            callback: ((ViewPdfActions, String) -> Unit)
        ): DialogRenamePdf {
            this.pdfTitle = pdfTitle
            this.callback = callback
            this.documentsModel = documentsModel
            return DialogRenamePdf()
        }
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogRenamePdfBinding = DialogRenamePdfBinding.inflate(inflater, container, false)

    @Inject
    lateinit var repositoryLocal: RepositoryLocal

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        if (pdfTitle?.isNotEmpty() == true) {
            binding.etPdfTitle.setText(pdfTitle)
            binding.etPdfTitle.hint = pdfTitle
        }
        isCancelable = false

        binding.etPdfTitle.requestFocus()

        Handler(Looper.getMainLooper()).postDelayed({
            view.showKeyboard()

        }, 500)


        binding.btnCancel.setOnClickListener {
            it.hideKeyboard()
            dismiss()
        }

        binding.saveChanges.setOnClickListener {
            try {
                val text = binding.etPdfTitle.text?.trim().toString()
                if (documentsModel?.fileName == text) {
                    callback?.invoke(ViewPdfActions.DUPLICATE_FILE, "")
                } else if (text == "") {
                    callback?.invoke(ViewPdfActions.EMPTY_RENAME, "")
                } else if (checkSpecialCharacters(text)) {
                    callback?.invoke(ViewPdfActions.SPECIAL_CHARACTER_VOILATION, "")
                } else if (checkExtension(text)) {
                    callback?.invoke(ViewPdfActions.INVALID_EXTENSION, "")
                } else {
                    isAlive { activity ->
                        FileUtils.renameFile(
                            text, documentsModel?.absolutePath!!, activity
                        ) { newFile ->

                            repositoryLocal.updateRecent(
                                PdfStoreItems(
                                    newFile?.absolutePath!!,
                                    text,
                                    documentsModel?.fileSize!!,
                                    documentsModel?.fileDate!!,
                                    getLocalDate(),
                                    "20/20/20",
                                    getLocalTimeStamp()
                                ),
                                documentsModel?.absolutePath!!
                            )
                        }
                    }

                    it.hideKeyboard()
                    dismiss()
                    callback?.invoke(ViewPdfActions.RENAME, text)


                }
            } catch (ex: Exception) {
                println(ex.localizedMessage)
            }

        }
    }

    override fun onStart() {
        super.onStart()
        setTopAnimation()
    }

    private fun checkSpecialCharacters(str: String): Boolean {
        val special: Pattern = Pattern.compile("[!@#$%&*+=|<>?{}\\[\\]~]")
        val hasSpecial: Matcher = special.matcher(str)
        return hasSpecial.find()
    }


    private fun checkExtension(str: String): Boolean {
        val pdfExt: Pattern = Pattern.compile("[.]pdf")
        val hasSpecial: Matcher = pdfExt.matcher(str)
        return hasSpecial.find()
    }


}