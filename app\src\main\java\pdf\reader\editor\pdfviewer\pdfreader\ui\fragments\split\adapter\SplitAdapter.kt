package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.split.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import coil.load
import coil.transform.RoundedCornersTransformation
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.SplitItemLayoutBinding
import java.util.*
import kotlin.collections.ArrayList

class SplitAdapter(
    var limit: Int,
    private var imageSelectListener: (ArrayList<PhotoModel>) -> Unit
) :
    RecyclerView.Adapter<SplitAdapter.ImageViewHolder>(), Filterable {
    private val selectedImages = arrayListOf<PhotoModel>()
    private val images: ArrayList<PhotoModel> = ArrayList()
    private var filterArrayList: ArrayList<PhotoModel> = ArrayList()


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val binding =
            SplitItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ImageViewHolder(
            binding
        )
    }

    override fun onBindViewHolder(
        viewHolder: ImageViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(viewHolder, position)
        } else {
            when {
                payloads.any { it is ImageSelectedOrUpdated } -> {

                    viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_selected)


                    setupItemForeground(viewHolder.binding.imgThumbnail, true)
                }
                payloads.any { it is ImageUnselected } -> {
                    viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
                    setupItemForeground(viewHolder.binding.imgThumbnail, false)
                }
                else -> {
                    onBindViewHolder(viewHolder, position)
                }
            }
        }
    }

    override fun onBindViewHolder(viewHolder: ImageViewHolder, position: Int) {
        val image = filterArrayList[position]
        val selectedIndex = findImageIndex(image, selectedImages)
        val isSelected = selectedIndex != -1
//        viewHolder.binding.imgThumbnail.load(image.bitmap)
        viewHolder.binding.imgThumbnail.load(image.bitmap) {
            crossfade(true)
            crossfade(700)
            transformations(RoundedCornersTransformation(radius = 10F))
        }
        setupItemForeground(viewHolder.binding.imgThumbnail, isSelected)
        viewHolder.binding.txtPageNumber.text = (image.position + 1).toString()
        if (isSelected) {
            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_selected)
        } else {
            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
        }
        viewHolder.itemView.setOnClickListener {
            selectOrRemoveImage(viewHolder.itemView.context, image, position)
        }
    }

    override fun getItemCount(): Int {
        return filterArrayList.size
    }

    private fun setupItemForeground(view: View, isSelected: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            view.foreground = if (isSelected) ColorDrawable(
                ContextCompat.getColor(
                    view.context,
                    R.color.transparent
                )
            ) else null
        }
    }

    private fun selectOrRemoveImage(
        context: Context,
        image: PhotoModel,
        position: Int
    ) {

        val selectedIndex = findImageIndex(image, selectedImages)
        if (selectedIndex != -1) {
            selectedImages.removeAt(selectedIndex)
            notifyItemChanged(
                position,
                ImageUnselected()
            )
            val indexes = findImageIndexes(selectedImages, filterArrayList)
            for (index in indexes) {
                notifyItemChanged(
                    index,
                    ImageSelectedOrUpdated()
                )
            }
        } else {
            if ((limit == 1 && selectedImages.size > 0) || selectedImages.size >= 100) {
                // val message = if (config.limitMessage != null) config.limitMessage!! else String.format(context.resources.getString(R.string.imagepicker_msg_limit_images), config.maxSize)
                // ToastHelper.show(context, message)
                Toast.makeText(context, "Limit exceeded!", Toast.LENGTH_SHORT).show()
                return
            } else {
                selectedImages.add(image)
                notifyItemChanged(
                    position,
                    ImageSelectedOrUpdated()
                )
            }
        }
        imageSelectListener(selectedImages)
    }

    fun setData(images: List<PhotoModel>) {
        this.images.clear()
        this.images.addAll(images)
        this.filterArrayList.clear()
        this.filterArrayList.addAll(images)
        notifyDataSetChanged()
    }

    fun setData(model: PhotoModel, position: Int) {

        this.images.add(model)
        this.filterArrayList.add(model)
        notifyItemInserted(position)
    }

    fun setSelectedImages(selectedImages: ArrayList<PhotoModel>) {
        this.selectedImages.clear()
        this.selectedImages.addAll(selectedImages)
        notifyDataSetChanged()

    }

    fun getSelectedImages(): ArrayList<PhotoModel> {
        return selectedImages
    }


    class ImageViewHolder(val binding: SplitItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)

    private fun findImageIndex(image: PhotoModel, images: ArrayList<PhotoModel>): Int {
        for (i in images.indices) {
            if (images[i] == image) {
                return i
            }
        }
        return -1
    }

    private fun findImageIndexes(
        subImages: ArrayList<PhotoModel>,
        images: ArrayList<PhotoModel>
    ): ArrayList<Int> {
        val indexes = arrayListOf<Int>()
        for (image in subImages) {
            for (i in images.indices) {
                if (images[i] == image) {
                    indexes.add(i)
                    break
                }
            }
        }
        return indexes
    }

    class ImageSelectedOrUpdated

    class ImageUnselected

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val searchContent = constraint.toString().lowercase()
                filterArrayList = if (searchContent.isEmpty())
                    images
                else {
                    val filterFiles = java.util.ArrayList<PhotoModel>()

                    for (docModel in images) {
                        if (docModel.position + 1 == searchContent.toInt())
                            filterFiles.add(docModel)
                    }
                    filterFiles
                }

                val filterResults = FilterResults()
                filterResults.values = filterArrayList
                return filterResults
            }

            @SuppressLint("NotifyDataSetChanged")
            override fun publishResults(p0: CharSequence?, results: FilterResults?) {
                filterArrayList = results?.values as java.util.ArrayList<PhotoModel>
                notifyDataSetChanged()
            }
        }
    }
}