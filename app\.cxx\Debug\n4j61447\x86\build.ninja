# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: MuPDF_Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/x86/
# =============================================================================
# Object build statements for STATIC_LIBRARY target mupdfthirdparty


#############################################
# Order-only phony target for mupdfthirdparty

build cmake_object_order_depends_target_mupdfthirdparty: phony || CMakeFiles/mupdfthirdparty.dir

build CMakeFiles/mupdfthirdparty.dir/thirdparty/mujs/one.c.o: C_COMPILER__mupdfthirdparty_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/mujs/one.c || cmake_object_order_depends_target_mupdfthirdparty
  DEFINES = -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\"
  DEP_FILE = CMakeFiles\mupdfthirdparty.dir\thirdparty\mujs\one.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfthirdparty.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfthirdparty.dir\thirdparty\mujs
  TARGET_COMPILE_PDB = CMakeFiles\mupdfthirdparty.dir\mupdfthirdparty.pdb
  TARGET_PDB = libmupdfthirdparty.pdb

build CMakeFiles/mupdfthirdparty.dir/thirdparty/jbig2dec/jbig2.c.o: C_COMPILER__mupdfthirdparty_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec/jbig2.c || cmake_object_order_depends_target_mupdfthirdparty
  DEFINES = -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\"
  DEP_FILE = CMakeFiles\mupdfthirdparty.dir\thirdparty\jbig2dec\jbig2.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfthirdparty.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfthirdparty.dir\thirdparty\jbig2dec
  TARGET_COMPILE_PDB = CMakeFiles\mupdfthirdparty.dir\mupdfthirdparty.pdb
  TARGET_PDB = libmupdfthirdparty.pdb

build CMakeFiles/mupdfthirdparty.dir/thirdparty/freetype/src/type1/type1.c.o: C_COMPILER__mupdfthirdparty_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/freetype/src/type1/type1.c || cmake_object_order_depends_target_mupdfthirdparty
  DEFINES = -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\"
  DEP_FILE = CMakeFiles\mupdfthirdparty.dir\thirdparty\freetype\src\type1\type1.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfthirdparty.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfthirdparty.dir\thirdparty\freetype\src\type1
  TARGET_COMPILE_PDB = CMakeFiles\mupdfthirdparty.dir\mupdfthirdparty.pdb
  TARGET_PDB = libmupdfthirdparty.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target mupdfthirdparty


#############################################
# Link the static library libmupdfthirdparty.a

build libmupdfthirdparty.a: C_STATIC_LIBRARY_LINKER__mupdfthirdparty_Debug CMakeFiles/mupdfthirdparty.dir/thirdparty/mujs/one.c.o CMakeFiles/mupdfthirdparty.dir/thirdparty/jbig2dec/jbig2.c.o CMakeFiles/mupdfthirdparty.dir/thirdparty/freetype/src/type1/type1.c.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info
  OBJECT_DIR = CMakeFiles\mupdfthirdparty.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\mupdfthirdparty.dir\mupdfthirdparty.pdb
  TARGET_FILE = libmupdfthirdparty.a
  TARGET_PDB = libmupdfthirdparty.pdb

# =============================================================================
# Object build statements for STATIC_LIBRARY target mupdfcore


#############################################
# Order-only phony target for mupdfcore

build cmake_object_order_depends_target_mupdfcore: phony || cmake_object_order_depends_target_mupdfthirdparty

build CMakeFiles/mupdfcore.dir/source/fitz/bbox-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/bbox-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\bbox-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/bitmap.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/bitmap.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\bitmap.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/buffer.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/buffer.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\buffer.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/colorspace.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/colorspace.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\colorspace.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/compressed-buffer.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/compressed-buffer.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\compressed-buffer.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/context.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/context.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\context.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/crypt-aes.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/crypt-aes.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\crypt-aes.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/crypt-arc4.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/crypt-arc4.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\crypt-arc4.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/crypt-md5.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/crypt-md5.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\crypt-md5.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/crypt-sha2.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/crypt-sha2.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\crypt-sha2.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/document-all.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/document-all.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\document-all.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/document.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/document.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\document.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-affine.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-affine.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-affine.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-blend.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-blend.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-blend.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-edge.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-edge.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-edge.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-glyph.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-glyph.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-glyph.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-mesh.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-mesh.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-mesh.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-paint.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-paint.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-paint.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-path.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-path.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-path.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-scale-simple.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-scale-simple.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-scale-simple.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/draw-unpack.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/draw-unpack.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\draw-unpack.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/error.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/error.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\error.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-basic.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-basic.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-basic.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-dct.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-dct.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-dct.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-fax.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-fax.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-fax.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-flate.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-flate.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-flate.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-jbig2.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-jbig2.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-jbig2.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-leech.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-leech.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-leech.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-lzw.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-lzw.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-lzw.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/filter-predict.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/filter-predict.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\filter-predict.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/font.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/font.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\font.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/ftoa.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/ftoa.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\ftoa.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/function.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/function.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\function.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/geometry.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/geometry.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\geometry.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/getopt.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/getopt.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\getopt.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/glyph.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/glyph.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\glyph.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/halftone.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/halftone.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\halftone.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/hash.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/hash.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\hash.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/image.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/image.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\image.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/jmemcust.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/jmemcust.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\jmemcust.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/link.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/link.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\link.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/list-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/list-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\list-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-gif.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-gif.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-gif.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-jpeg.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-jpeg.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-jpeg.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-jpx.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-jpx.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-jpx.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-jxr.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-jxr.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-jxr.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-png.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-png.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-png.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/load-tiff.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/load-tiff.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\load-tiff.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/memento.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/memento.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\memento.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/memory.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/memory.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\memory.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/outline.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/outline.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\outline.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/output-pcl.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/output-pcl.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\output-pcl.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/output-pwg.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/output-pwg.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\output-pwg.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/output.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/output.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\output.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/path.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/path.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\path.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/pixmap.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/pixmap.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\pixmap.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/printf.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/printf.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\printf.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/separation.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/separation.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\separation.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/shade.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/shade.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\shade.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stext-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stext-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stext-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stext-output.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stext-output.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stext-output.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stext-paragraph.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stext-paragraph.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stext-paragraph.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stext-search.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stext-search.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stext-search.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/store.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/store.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\store.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stream-open.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stream-open.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stream-open.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stream-prog.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stream-prog.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stream-prog.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/stream-read.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/stream-read.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\stream-read.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/string.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/string.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\string.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/strtod.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/strtod.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\strtod.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/svg-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/svg-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\svg-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/tempfile.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/tempfile.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\tempfile.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/test-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/test-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\test-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/text.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/text.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\text.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/time.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/time.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\time.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/trace-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/trace-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\trace-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/transition.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/transition.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\transition.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/tree.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/tree.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\tree.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/ucdn.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/ucdn.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\ucdn.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/unzip.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/unzip.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\unzip.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/util.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/util.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\util.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/fitz/xml.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz/xml.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\fitz\xml.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\fitz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-annot-edit.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-annot-edit.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-annot-edit.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-annot.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-annot.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-annot.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-appearance.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-appearance.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-appearance.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-clean-file.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-clean-file.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-clean-file.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-clean.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-clean.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-clean.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-load.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-cmap-load.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-cmap-load.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-parse.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-cmap-parse.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-cmap-parse.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-table.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-cmap-table.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-cmap-table.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-cmap.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-cmap.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-colorspace.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-colorspace.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-colorspace.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-crypt.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-crypt.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-crypt.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-device.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-device.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-device.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-encoding.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-encoding.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-encoding.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-event.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-event.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-event.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-field.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-field.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-field.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-font.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-font.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-font.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-fontfile.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-fontfile.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-fontfile.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-form.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-form.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-form.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-function.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-function.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-function.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-image.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-image.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-image.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-interpret.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-interpret.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-interpret.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-lex.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-lex.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-lex.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-metrics.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-metrics.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-metrics.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-nametree.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-nametree.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-nametree.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-object.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-object.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-object.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-buffer.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-op-buffer.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-op-buffer.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-filter.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-op-filter.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-op-filter.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-run.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-op-run.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-op-run.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-outline.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-outline.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-outline.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-page.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-page.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-page.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-parse.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-parse.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-parse.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-pattern.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-pattern.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-pattern.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-pkcs7.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-pkcs7.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-pkcs7.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-repair.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-repair.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-repair.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-run.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-run.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-run.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-shade.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-shade.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-shade.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-store.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-store.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-store.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-stream.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-stream.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-stream.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-type3.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-type3.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-type3.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-unicode.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-unicode.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-unicode.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-write.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-write.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-write.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-xobject.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-xobject.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-xobject.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/pdf-xref.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/pdf-xref.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\pdf-xref.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-common.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-common.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-common.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-doc.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-doc.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-doc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-glyphs.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-glyphs.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-glyphs.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-gradient.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-gradient.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-gradient.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-image.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-image.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-image.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-link.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-link.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-link.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-outline.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-outline.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-outline.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-path.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-path.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-path.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-resource.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-resource.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-resource.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-tile.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-tile.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-tile.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-util.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-util.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-util.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/xps/xps-zip.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps/xps-zip.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\xps\xps-zip.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\xps
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/cbz/mucbz.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz/mucbz.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\cbz\mucbz.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\cbz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/cbz/muimg.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz/muimg.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\cbz\muimg.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\cbz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/cbz/mutiff.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz/mutiff.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\cbz\mutiff.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\cbz
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/gprf/gprf-doc.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/gprf/gprf-doc.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\gprf\gprf-doc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\gprf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/gprf/gprf-skeleton.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/gprf/gprf-skeleton.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\gprf\gprf-skeleton.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\gprf
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/css-apply.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/css-apply.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\css-apply.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/css-parse.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/css-parse.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\css-parse.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/epub-doc.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/epub-doc.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\epub-doc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/html-doc.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/html-doc.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\html-doc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/html-font.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/html-font.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\html-font.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/html/html-layout.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/html/html-layout.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\html\html-layout.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\html
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/js/pdf-js.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/js/pdf-js.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\js\pdf-js.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf\js
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb

build CMakeFiles/mupdfcore.dir/source/pdf/js/pdf-jsimp-mu.c.o: C_COMPILER__mupdfcore_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf/js/pdf-jsimp-mu.c || cmake_object_order_depends_target_mupdfcore
  DEFINES = -DAA_BITS=8 -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -DSUPPORT_GPROOF
  DEP_FILE = CMakeFiles\mupdfcore.dir\source\pdf\js\pdf-jsimp-mu.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  OBJECT_FILE_DIR = CMakeFiles\mupdfcore.dir\source\pdf\js
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_PDB = libmupdfcore.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target mupdfcore


#############################################
# Link the static library libmupdfcore.a

build libmupdfcore.a: C_STATIC_LIBRARY_LINKER__mupdfcore_Debug CMakeFiles/mupdfcore.dir/source/fitz/bbox-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/bitmap.c.o CMakeFiles/mupdfcore.dir/source/fitz/buffer.c.o CMakeFiles/mupdfcore.dir/source/fitz/colorspace.c.o CMakeFiles/mupdfcore.dir/source/fitz/compressed-buffer.c.o CMakeFiles/mupdfcore.dir/source/fitz/context.c.o CMakeFiles/mupdfcore.dir/source/fitz/crypt-aes.c.o CMakeFiles/mupdfcore.dir/source/fitz/crypt-arc4.c.o CMakeFiles/mupdfcore.dir/source/fitz/crypt-md5.c.o CMakeFiles/mupdfcore.dir/source/fitz/crypt-sha2.c.o CMakeFiles/mupdfcore.dir/source/fitz/device.c.o CMakeFiles/mupdfcore.dir/source/fitz/document-all.c.o CMakeFiles/mupdfcore.dir/source/fitz/document.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-affine.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-blend.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-edge.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-glyph.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-mesh.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-paint.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-path.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-scale-simple.c.o CMakeFiles/mupdfcore.dir/source/fitz/draw-unpack.c.o CMakeFiles/mupdfcore.dir/source/fitz/error.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-basic.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-dct.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-fax.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-flate.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-jbig2.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-leech.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-lzw.c.o CMakeFiles/mupdfcore.dir/source/fitz/filter-predict.c.o CMakeFiles/mupdfcore.dir/source/fitz/font.c.o CMakeFiles/mupdfcore.dir/source/fitz/ftoa.c.o CMakeFiles/mupdfcore.dir/source/fitz/function.c.o CMakeFiles/mupdfcore.dir/source/fitz/geometry.c.o CMakeFiles/mupdfcore.dir/source/fitz/getopt.c.o CMakeFiles/mupdfcore.dir/source/fitz/glyph.c.o CMakeFiles/mupdfcore.dir/source/fitz/halftone.c.o CMakeFiles/mupdfcore.dir/source/fitz/hash.c.o CMakeFiles/mupdfcore.dir/source/fitz/image.c.o CMakeFiles/mupdfcore.dir/source/fitz/jmemcust.c.o CMakeFiles/mupdfcore.dir/source/fitz/link.c.o CMakeFiles/mupdfcore.dir/source/fitz/list-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-gif.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-jpeg.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-jpx.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-jxr.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-png.c.o CMakeFiles/mupdfcore.dir/source/fitz/load-tiff.c.o CMakeFiles/mupdfcore.dir/source/fitz/memento.c.o CMakeFiles/mupdfcore.dir/source/fitz/memory.c.o CMakeFiles/mupdfcore.dir/source/fitz/outline.c.o CMakeFiles/mupdfcore.dir/source/fitz/output-pcl.c.o CMakeFiles/mupdfcore.dir/source/fitz/output-pwg.c.o CMakeFiles/mupdfcore.dir/source/fitz/output.c.o CMakeFiles/mupdfcore.dir/source/fitz/path.c.o CMakeFiles/mupdfcore.dir/source/fitz/pixmap.c.o CMakeFiles/mupdfcore.dir/source/fitz/printf.c.o CMakeFiles/mupdfcore.dir/source/fitz/separation.c.o CMakeFiles/mupdfcore.dir/source/fitz/shade.c.o CMakeFiles/mupdfcore.dir/source/fitz/stext-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/stext-output.c.o CMakeFiles/mupdfcore.dir/source/fitz/stext-paragraph.c.o CMakeFiles/mupdfcore.dir/source/fitz/stext-search.c.o CMakeFiles/mupdfcore.dir/source/fitz/store.c.o CMakeFiles/mupdfcore.dir/source/fitz/stream-open.c.o CMakeFiles/mupdfcore.dir/source/fitz/stream-prog.c.o CMakeFiles/mupdfcore.dir/source/fitz/stream-read.c.o CMakeFiles/mupdfcore.dir/source/fitz/string.c.o CMakeFiles/mupdfcore.dir/source/fitz/strtod.c.o CMakeFiles/mupdfcore.dir/source/fitz/svg-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/tempfile.c.o CMakeFiles/mupdfcore.dir/source/fitz/test-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/text.c.o CMakeFiles/mupdfcore.dir/source/fitz/time.c.o CMakeFiles/mupdfcore.dir/source/fitz/trace-device.c.o CMakeFiles/mupdfcore.dir/source/fitz/transition.c.o CMakeFiles/mupdfcore.dir/source/fitz/tree.c.o CMakeFiles/mupdfcore.dir/source/fitz/ucdn.c.o CMakeFiles/mupdfcore.dir/source/fitz/unzip.c.o CMakeFiles/mupdfcore.dir/source/fitz/util.c.o CMakeFiles/mupdfcore.dir/source/fitz/xml.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-annot-edit.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-annot.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-appearance.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-clean-file.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-clean.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-load.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-parse.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap-table.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-cmap.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-colorspace.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-crypt.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-device.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-encoding.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-event.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-field.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-font.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-fontfile.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-form.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-function.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-image.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-interpret.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-lex.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-metrics.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-nametree.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-object.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-buffer.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-filter.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-op-run.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-outline.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-page.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-parse.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-pattern.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-pkcs7.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-repair.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-run.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-shade.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-store.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-stream.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-type3.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-unicode.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-write.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-xobject.c.o CMakeFiles/mupdfcore.dir/source/pdf/pdf-xref.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-common.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-doc.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-glyphs.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-gradient.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-image.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-link.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-outline.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-path.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-resource.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-tile.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-util.c.o CMakeFiles/mupdfcore.dir/source/xps/xps-zip.c.o CMakeFiles/mupdfcore.dir/source/cbz/mucbz.c.o CMakeFiles/mupdfcore.dir/source/cbz/muimg.c.o CMakeFiles/mupdfcore.dir/source/cbz/mutiff.c.o CMakeFiles/mupdfcore.dir/source/gprf/gprf-doc.c.o CMakeFiles/mupdfcore.dir/source/gprf/gprf-skeleton.c.o CMakeFiles/mupdfcore.dir/source/html/css-apply.c.o CMakeFiles/mupdfcore.dir/source/html/css-parse.c.o CMakeFiles/mupdfcore.dir/source/html/epub-doc.c.o CMakeFiles/mupdfcore.dir/source/html/html-doc.c.o CMakeFiles/mupdfcore.dir/source/html/html-font.c.o CMakeFiles/mupdfcore.dir/source/html/html-layout.c.o CMakeFiles/mupdfcore.dir/source/pdf/js/pdf-js.c.o CMakeFiles/mupdfcore.dir/source/pdf/js/pdf-jsimp-mu.c.o || libmupdfthirdparty.a C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/libgs.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info
  OBJECT_DIR = CMakeFiles\mupdfcore.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\mupdfcore.dir\mupdfcore.pdb
  TARGET_FILE = libmupdfcore.a
  TARGET_PDB = libmupdfcore.pdb
  RSP_FILE = CMakeFiles\mupdfcore.rsp

# =============================================================================
# Object build statements for SHARED_LIBRARY target adnan


#############################################
# Order-only phony target for adnan

build cmake_object_order_depends_target_adnan: phony || cmake_object_order_depends_target_mupdfcore cmake_object_order_depends_target_mupdfthirdparty

build CMakeFiles/adnan.dir/mupdf.c.o: C_COMPILER__adnan_Debug C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/mupdf.c || cmake_object_order_depends_target_adnan
  DEFINES = -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\"slimftoptions.h\" -Dadnan_EXPORTS
  DEP_FILE = CMakeFiles\adnan.dir\mupdf.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg
  OBJECT_DIR = CMakeFiles\adnan.dir
  OBJECT_FILE_DIR = CMakeFiles\adnan.dir
  TARGET_COMPILE_PDB = CMakeFiles\adnan.dir\
  TARGET_PDB = C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\x86\libadnan.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target adnan


#############################################
# Link the shared library C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\x86\libadnan.so

build C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/build/intermediates/cxx/Debug/n4j61447/obj/x86/libadnan.so: C_SHARED_LIBRARY_LINKER__adnan_Debug CMakeFiles/adnan.dir/mupdf.c.o | libmupdfcore.a C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/24/liblog.so C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/24/libjnigraphics.so libmupdfthirdparty.a C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/libgs.so || libmupdfcore.a libmupdfthirdparty.a
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = libmupdfcore.a  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/24/liblog.so  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/24/libjnigraphics.so  -lm  libmupdfthirdparty.a  C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/libgs.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\adnan.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libadnan.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\adnan.dir\
  TARGET_FILE = C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\x86\libadnan.so
  TARGET_PDB = C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\x86\libadnan.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\StudioProjects\pdf-editor9D\app\.cxx\Debug\n4j61447\x86 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\StudioProjects\pdf-editor9D\app\.cxx\Debug\n4j61447\x86 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\StudioProjects\pdf-editor9D\app\src\main\jni -BC:\Users\<USER>\StudioProjects\pdf-editor9D\app\.cxx\Debug\n4j61447\x86"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build adnan: phony C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/build/intermediates/cxx/Debug/n4j61447/obj/x86/libadnan.so

build libadnan.so: phony C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/build/intermediates/cxx/Debug/n4j61447/obj/x86/libadnan.so

build mupdfcore: phony libmupdfcore.a

build mupdfthirdparty: phony libmupdfthirdparty.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/x86

build all: phony libmupdfthirdparty.a libmupdfcore.a C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/build/intermediates/cxx/Debug/n4j61447/obj/x86/libadnan.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake C$:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
