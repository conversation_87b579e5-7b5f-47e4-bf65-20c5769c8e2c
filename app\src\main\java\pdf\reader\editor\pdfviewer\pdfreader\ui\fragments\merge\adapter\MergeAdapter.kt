package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.merge.adapter

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AllFileItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show

class MergeAdapter(
    var limit: Int,
    private var imageSelectListener: (ArrayList<DocumentsModel>) -> Unit
) : RecyclerView.Adapter<MergeAdapter.ImageViewHolder>() {


    private val selectedImages = arrayListOf<DocumentsModel>()
    private val images: ArrayList<DocumentsModel> = ArrayList()


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val binding =
            AllFileItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ImageViewHolder(
            binding
        )
    }

    override fun onBindViewHolder(
        viewHolder: ImageViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        viewHolder.binding.imgSelected.show()
        viewHolder.binding.imgOptions.hide()
        if (payloads.isEmpty()) {
            onBindViewHolder(viewHolder, position)
        } else {
            when {
                payloads.any { it is ImageSelectedOrUpdated } -> {
                    val image = images[position]
                    val selectedIndex = findImageIndex(image, selectedImages)
                    Log.d("TAG", "onBindViewHolder: $selectedIndex")
                    viewHolder.binding.imgSelected.setImageResource(if (selectedIndex != -1) R.drawable.ic_selected else R.drawable.ic_unselected)
//                    viewHolder.binding.txtSelectNumber.background = ContextCompat.getDrawable(
//                        viewHolder.binding.root.context,
//                        R.drawable.selected_image_background
//                    )
                    setupItemForeground(viewHolder.binding.root, true)
                }
                payloads.any { it is ImageUnselected } -> {
                    viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
//                    viewHolder.binding.txtSelectNumber.background = ContextCompat.getDrawable(
//                        viewHolder.binding.root.context,
//                        R.drawable.unselected_image_background
//                    )
                    setupItemForeground(viewHolder.binding.root, false)
                }
                else -> {
                    onBindViewHolder(viewHolder, position)
                }
            }
        }
    }

    override fun onBindViewHolder(viewHolder: ImageViewHolder, position: Int) {
        viewHolder.binding.imgOptions.hide()
        viewHolder.binding.imgSelected.show()
        val image = images[position]
        val selectedIndex = findImageIndex(image, selectedImages)
        val isSelected = selectedIndex != -1
//        viewHolder.binding.imgPDFThumbnail.loadPDFThumbnail(image)
        viewHolder.binding.txtFileName.text = image.fileName
        viewHolder.binding.txtFileSize.text = image.fileSize
        viewHolder.binding.txtFileDate.text = image.fileDate

        setupItemForeground(viewHolder.binding.root, isSelected)

        if (isSelected) {
            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_selected)
//            viewHolder.binding.txtSelectNumber.background = ContextCompat.getDrawable(
//                viewHolder.binding.root.context,
//                R.drawable.selected_image_background
//            )
        } else {
            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
//            viewHolder.binding.txtSelectNumber.background = ContextCompat.getDrawable(
//                viewHolder.binding.root.context,
//                R.drawable.unselected_image_background
//            )
        }
        viewHolder.itemView.setOnClickListener {
            selectOrRemoveImage(viewHolder.itemView.context, image, position)
        }
    }

    override fun getItemCount(): Int {
        return images.size
    }

    private fun setupItemForeground(view: View, isSelected: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            view.foreground = if (isSelected) ColorDrawable(
                ContextCompat.getColor(
                    view.context,
                    R.color.transparent
                )
            ) else null
        }
    }

    private fun selectOrRemoveImage(
        context: Context,
        image: DocumentsModel,
        position: Int
    ) {

        val selectedIndex = findImageIndex(image, selectedImages)
        if (selectedIndex != -1) {
            selectedImages.removeAt(selectedIndex)
            notifyItemChanged(
                position,
                ImageUnselected()
            )
            Log.d("TAG", "selectOrRemoveImage: ${selectedIndex}  $position")
            val indexes = findImageIndexes(selectedImages, images)
            for (index in indexes) {
                notifyItemChanged(
                    index,
                    ImageSelectedOrUpdated()
                )
            }
        } else {
            if ((limit == 1 && selectedImages.size > 0) || selectedImages.size >= 100) {
                // val message = if (config.limitMessage != null) config.limitMessage!! else String.format(context.resources.getString(R.string.imagepicker_msg_limit_images), config.maxSize)
                // ToastHelper.show(context, message)
                Toast.makeText(context, "Limit exceeded!", Toast.LENGTH_SHORT).show()
                return
            } else {
                selectedImages.add(image)
                notifyItemChanged(
                    position,
                    ImageSelectedOrUpdated()
                )
            }
        }
        imageSelectListener(selectedImages)
    }

    fun setData(images: List<DocumentsModel>) {
        this.images.clear()
        this.images.addAll(images)
        notifyDataSetChanged()
    }

    fun setSelectedImages(selectedImages: ArrayList<DocumentsModel>) {
        this.selectedImages.clear()
        this.selectedImages.addAll(selectedImages)
        notifyDataSetChanged()

    }

    fun removeDocument(documentsModel: DocumentsModel, context: Context) {
        val index = findImageIndex(documentsModel, images)
        selectOrRemoveImage(context, documentsModel, index)
    }

    fun getSelectedImages(): ArrayList<DocumentsModel> {
        return selectedImages
    }

    class ImageViewHolder(val binding: AllFileItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)

    private fun findImageIndex(image: DocumentsModel, images: ArrayList<DocumentsModel>): Int {
        for (i in images.indices) {
            if (images[i] == image) {
                Log.d("TAG", "findImageIndex: $i")
                return i
            }
        }
        return -1
    }

    private fun findImageIndexes(
        subImages: ArrayList<DocumentsModel>,
        images: ArrayList<DocumentsModel>
    ): ArrayList<Int> {
        val indexes = arrayListOf<Int>()
        for (image in subImages) {
            for (i in images.indices) {
                if (images[i] == image) {
                    indexes.add(i)
                    break
                }
            }
        }
        return indexes
    }

    class ImageSelectedOrUpdated

    class ImageUnselected
}