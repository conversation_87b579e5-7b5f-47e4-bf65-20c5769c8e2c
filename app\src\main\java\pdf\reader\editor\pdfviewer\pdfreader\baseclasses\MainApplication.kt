package pdf.reader.editor.pdfviewer.pdfreader.baseclasses

import android.app.Application
import androidx.appcompat.app.AppCompatDelegate
import dagger.hilt.android.HiltAndroidApp
import pdf.reader.editor.pdfviewer.pdfreader.BuildConfig
import pdf.reader.editor.pdfviewer.pdfreader.TimberTree
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.shared.PublicValue
import timber.log.Timber

@HiltAndroidApp
class MainApplication : Application() {


    val storage: Storage by lazy {
        Storage(this)
    }



    override fun onCreate() {
        super.onCreate()
        instance = this
        storage.getPreferredTheme().let {
            if (it == PublicValue.DARK_THEME) {

                AppCompatDelegate.setDefaultNightMode(
                    AppCompatDelegate.MODE_NIGHT_YES
                )
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }
//            theme = it
            Timber.plant(if (BuildConfig.DEBUG) Timber.DebugTree() else TimberTree())
        }
    }

//    override fun attachBaseContext(base: Context) {
//        val preferences = base.getSharedPreferences("language_pref", Context.MODE_PRIVATE)
//        val l = preferences?.getString("Locale.Helper.Selected.Language", "en")
//        Log.i("initiate", " language------------------------------"+l.toString())
//        super.attachBaseContext(LocalHelper.onAttach(base))
//    }

//    override fun attachBaseContext(base: Context) {
//        val preferences = base.getSharedPreferences("language_pref", Context.MODE_PRIVATE)
//        val l = preferences?.getString("Locale.Helper.Selected.Language", "en")
//        Log.i("initiate", " language------------------------------"+l.toString())
//        super.attachBaseContext(LocaleUtils.getLocalizedContext(base, Storage(base).getPreferredLocale()))
//    }

    companion object {
        private var instance: MainApplication? = null

        fun getContext(): MainApplication {
            return instance!!
        }

    }


}