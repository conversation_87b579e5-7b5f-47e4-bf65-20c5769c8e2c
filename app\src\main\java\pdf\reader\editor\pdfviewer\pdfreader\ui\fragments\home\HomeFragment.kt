package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.home

import android.app.Dialog
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.webkit.MimeTypeMap
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.facebook.shimmer.ShimmerFrameLayout
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ExitBottomSheetBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentHomeBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.HomeNativeAdLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkStoragePermissionGranted
import pdf.reader.editor.pdfviewer.pdfreader.extensions.dateDifferenceInDays
import pdf.reader.editor.pdfviewer.pdfreader.extensions.fromHtml
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getFileFromCache
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideWithAnimation
import pdf.reader.editor.pdfviewer.pdfreader.extensions.invisible
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showWithAnimation
import pdf.reader.editor.pdfviewer.pdfreader.extensions.startAppActivity
import pdf.reader.editor.pdfviewer.pdfreader.manager.MyBottomSheet
import pdf.reader.editor.pdfviewer.pdfreader.manager.SortMenuHelper
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AdUtility
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AppOpenManager
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.data.InterstitialAdInfo
import pdf.reader.editor.pdfviewer.pdfreader.newads.displayBannerAd
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.newads.manageFrameLayoutView
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.PDFFileActions
import pdf.reader.editor.pdfviewer.pdfreader.shared.PublicValue
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity.Companion.isFromAssetViewer
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.RatingsDialog
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.home.adapter.FilesAdapter
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf.RecentPdfFragment.Companion.isFromRecentAsset
import java.io.File
import java.io.FileOutputStream
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import javax.inject.Inject
import kotlin.math.pow
import kotlin.system.exitProcess

@AndroidEntryPoint
class HomeFragment :
    BaseFragment<FragmentHomeBinding, HomeViewModel>() {
    override val viewModel: Class<HomeViewModel>
        get() = HomeViewModel::class.java

    private var filesAdapter: FilesAdapter? = null
    private var nativeAd: NativeAd? = null
    private var sheet: Dialog? = null
    private var isFirsTime = false
    private val adViewModel by viewModels<AdViewModel>()

    @Inject
    lateinit var adsIds: AdsIds
    private var isFromAsset = false
    private var fileDocList: MutableList<DocumentsModel> = mutableListOf()


    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentHomeBinding = FragmentHomeBinding.inflate(inflater, container, false)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkAndAskPermissions()

        if (!isFirsTime) {
            isFirsTime = true
            adsIds.apply {
                adViewModel.apply {
//                    if (storage.isConsentDone) {
//
//                        activity
//                            ?.getNativeAdObject("getString(R.string.exit_native_ad_id)", null, onResult = {
//                                nativeAd = it
//                            })
//                    }
                    exitNative.run {
                        canRequestAd = storage.isConsentDone
                        remoteConfig =
                            adViewModel.getRemoteConfigModel().adConfigModel.exitNative.show
                    }
                    loadNativeAd(nativeAdInfo = exitNative,
                        onAdLoaded = { i, hashMap ->
                            hashMap[i]?.nativeAd?.let { nativeAd = it }

                        })
                }
            }

        }
    }

    override fun initAllViews() {
        super.initAllViews()
//        lifecycleScope.launch(Dispatchers.IO) {
//            delay(500)
//            initViews()
//        }

        initViews()
    }

//    override fun onResume() {
//        super.onResume()
//
//        if (requireActivity().checkStoragePermissionGranted()) {
//
//            sharedViewModel.loadFiles(requireActivity())
//        }
//    }

    private fun initViews() {

        isAlive {
            adsIds.homeNativeScroll.run {
                canRequestAd = storage.isConsentDone
                remoteConfig =
                    adViewModel.getRemoteConfigModel().adConfigModel.homeNativeScroll.show
            }
            filesAdapter = activity?.let { it1 ->
                FilesAdapter(
                    it1,
                    remoteValue = adsIds.homeNativeScroll.remoteConfig,
                    googleConsent = adsIds.homeNativeScroll.canRequestAd,
                    nativeAdUnitId = adsIds.homeNativeScroll.adUnitId,
                    adPosition = adViewModel.getRemoteConfigModel().homeScrollPositionAd.toInt()
                ) { action, file ->
                    when (action) {
                        PDFFileActions.COMPLETE -> {
                            logs("Complete action")
                            isAlive {
                                sharedViewModel.loadFiles(requireActivity())
                            }
                        }

                        PDFFileActions.MERGE -> {
                            isAlive {
                                sharedViewModel.selectedDoc = file
                                val mergeAction =
                                    HomeFragmentDirections.actionHomeFragmentToMergeFragment()
                                findNavController().navigate(mergeAction)
                            }
                        }

                        PDFFileActions.SPLIT -> {
                            isAlive {
                                sharedViewModel.selectedDoc = file
                                val splitAction =
                                    HomeFragmentDirections.actionHomeFragmentToSplitFragment()
                                findNavController().navigate(splitAction)
                            }

                        }

                        PDFFileActions.ANNOTATION -> {
                            isAlive {
                                sharedViewModel.selectedDoc = file
                                val annotAction =
                                    HomeFragmentDirections.actionHomeFragmentToPdfViewFragment()
                                annotAction.isEdit = true
                                findNavController().navigate(annotAction)
                            }
                        }

                        PDFFileActions.SELECT -> {
                            showInterAdWhenOpeningFile(file)
                        }
                    }
                    //                { action, file ->
                    //
                    ////                FileUtils.copyFile(it) { doc ->
                    ////                    requireActivity().sendBroadcast(
                    ////                        Intent(
                    ////                            Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                    ////                                File(doc.absolutePath)
                    ////                            )
                    ////                        )
                    ////                    )
                    //                    isAlive { activity ->
                    //                        sharedViewModel.selectedDoc = it
                    //                        val action = HomeFragmentDirections.actionHomeFragmentToPdfViewFragment()
                    //                        findNavController().navigate(action)
                    //                    }
                    //
                    ////                    mViewDataBinding.searchView.closeSearch()
                    ////                }
                    //
                    //                }
                }
            }

//            mViewModel.loadAd(it, storage.isConsentDone) { native ->
//
//                populateNative(native)
//            }

        }

        mViewDataBinding.allFilesRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        mViewDataBinding.allFilesRecyclerView.adapter = filesAdapter

        sharedViewModel.getDocuments().observe(viewLifecycleOwner) { filesList ->

            if (filesList.isNotEmpty()) {
                fileDocList.clear()
                fileDocList.addAll(filesList)
                isFromAsset = false
                filesAdapter?.setData(filesList, isFromAsset)
                loadAds()
//                mViewDataBinding.allFilesRecyclerView.scrollToPosition(0)
            } else {
                val cachedFile = context?.let { getFileFromCache(it) }
//                val documentsModel = loadPdfFromAssets("sample.pdf")

                if (cachedFile != null) {
                    val documentsModel = DocumentsModel(
                        fileName = cachedFile.name.toString(),
                        fileSize = cachedFile.length().getReadableSize(),
                        fileMimeType = getMimeType(cachedFile.absolutePath).toString(),
                        parentFile = cachedFile.parentFile?.toString() ?: "",
                        absolutePath = cachedFile.absolutePath,

                        )

                    isFromAsset = true
                    val list = mutableListOf<DocumentsModel>().apply {
                        add(documentsModel)
                    }
                    fileDocList.clear()
                    fileDocList.addAll(list)
                    filesAdapter?.setData(list, isFromAsset)
                    loadAds()
                }
            }
        }

        mViewDataBinding.txtAppTitle.text =
            activity?.fromHtml(resources.getString(R.string.pdf_reader))

//        mViewDataBinding.fabCreate.setOnClickListener {
//            navigate(HomeFragmentDirections.actionHomeFragmentToNewFileMenuFragment())
////            mViewDataBinding.searchView.closeSearch()
//        }

        isAlive {
            mViewDataBinding.imgSort.setOnClickListener(object :
                SortMenuHelper.OnClickSortMenu(requireActivity()) {

                override fun onComplete(order: String) {
                    sharedViewModel.setPDFSortingType(order)
                }

                override val sortOrder: String
                    get() = sharedViewModel.pdfSortingType
            })
        }


//        mViewDataBinding.allFilesRecyclerView.apply {
//            layoutManager = LinearLayoutManager(requireContext())
//            adapter = filesAdapter
//        }


        mViewDataBinding.recentHeader.setOnClickListener {
            isAlive {
                isFromRecentAsset = isFromAsset
                findNavController().navigate(HomeFragmentDirections.actionHomeFragmentToRecentPdfFragment())
//            mViewDataBinding.searchView.getEditText().setText("")
                mViewDataBinding.searchView.hideKeyboard()
//            mViewDataBinding.searchView.closeSearch()
            }

        }

        mViewDataBinding.imgSetting.setOnClickListener {
            isAlive {
                val direction = HomeFragmentDirections.actionHomeFragmentToSettingsFragment()
                findNavController().navigate(direction)
            }

        }

        mViewDataBinding.searchView.setOnClickListener {
            enableSearch()
        }

        mViewDataBinding.searchBack.setOnClickListener {
            disableSearch()
        }

        mViewDataBinding.imgClearText.setOnClickListener {
            mViewDataBinding.edtSearch.setText("")
        }

        mViewDataBinding
            .edtSearch.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {

                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                    if (!s.isNullOrEmpty()) {
                        mViewDataBinding.imgClearText.show()
                        filesAdapter?.filter?.filter(s.trim().toString())
                    } else {
                        filesAdapter?.filter?.filter("")
                        mViewDataBinding.imgClearText.invisible()
                    }

                }

                override fun afterTextChanged(s: Editable?) {

                }
            })
        filesAdapter?.callBack = { isNoResult ->
            if (isNoResult) {
                if (mViewDataBinding.searchView.isVisible) {
                    mViewDataBinding.nativeAdLayoutSearch.nativeAdView.hide()
                    mViewDataBinding.shimmerSearch.shimmer.hide()
                }
            } else {
                if (mViewDataBinding.searchView.isVisible) {
                    if (adViewModel.getNativeAdMap().containsKey(adsIds.homeSearchNative.adKey)) {
                        adViewModel.getNativeAdMap()[adsIds.homeSearchNative.adKey]?.nativeAd?.let {
                            logs("homeSearchNative:: $it")
                            if (mViewDataBinding.searchToolbar.isVisible) {
                                mViewDataBinding.nativeAdLayoutSearch.nativeAdView.show()
                            }

                        }

                    }
                }
            }
        }

        if (mViewModel.isFirstTime) {
            isAlive {
                if (it.checkStoragePermissionGranted())
                    sharedViewModel.loadFiles(requireActivity())
            }

        }

        mViewModel.isFirstTime = false
        isAlive {
            activity?.onBackPressedDispatcher
                ?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
                    override fun handleOnBackPressed() {
                        when {
                            mViewDataBinding.searchToolbar.isVisible -> {
                                disableSearch()
                            }

                            nativeAd != null -> {
                                val nativeBinding = ExitBottomSheetBinding.inflate(
                                    LayoutInflater.from(requireContext()), null, false
                                )

                                nativeBinding.apply {


                                    btnsubmit.setOnClickListener {
                                        MyBottomSheet.getInstance(requireActivity())
                                            ?.dismissDialog()
                                        activity?.finishAffinity()
                                    }

                                    adFrameExit.apply {
                                        cardBg.mediaView = adMedia
                                        cardBg.mediaView?.setOnHierarchyChangeListener(object :
                                            ViewGroup.OnHierarchyChangeListener {
                                            override fun onChildViewAdded(
                                                parent: View?,
                                                child: View?
                                            ) {
                                                if (child is ImageView) {
                                                    child.scaleType =
                                                        ImageView.ScaleType.CENTER_INSIDE
                                                }
                                            }

                                            override fun onChildViewRemoved(
                                                parent: View?,
                                                child: View?
                                            ) {
                                            }
                                        })
                                        cardBg.headlineView = adHeadline
                                        cardBg.callToActionView = adCallToAction

                                        adCallToAction.text = nativeAd?.callToAction
                                        adBody.text = nativeAd?.body
                                        (cardBg.headlineView as TextView).text = nativeAd?.headline
                                        adAppIcon.setImageDrawable(nativeAd?.icon?.drawable)

                                        cardBg.setNativeAd(nativeAd!!)
                                        cardBg.show()
                                    }
                                }


                                sheet = MyBottomSheet.getInstance(requireActivity())
                                    ?.setContentView(nativeBinding.root, true)?.showDialog()
                                sheet?.setOnDismissListener {
                                    sheet?.dismiss()

                                }
                            }

                            !storage.isUserGaveRating() && it.dateDifferenceInDays(
                                storage.getRatingDate()
                            ) >= 1L -> {
                                storage.setRatingDate()
                                RatingsDialog.getInstance { flag ->
                                    if (flag) {
                                        it.finish()
                                    }

                                }.show(childFragmentManager, "Rating Dialog")
                            }

                            else -> it.finish()
                        }


                    }
                }
                )

            it.intent?.let { intent ->
                logs("HomeFragment>>it.intent")
//                if (!mViewModel.isFromIntent) {
                if (intent.data != null) {

//                checkForIntentFilter()
                    val doc = getFileFromUri(intent.data!!)
                    if (doc != null) {
                        sharedViewModel.selectedDoc = doc
                        isAlive {
                            activity?.startAppActivity<ViewerActivity>(Pair("pdf_file", doc))
                            /*     intent.data = null
                                 findNavController().navigate(HomeFragmentDirections.actionHomeFragmentToPdfViewFragment())*/
                        }

                    }
                }
//                }

            }
        }

    }

    fun Long.getReadableSize(): String {
        try {
            val symbolsEN_US: DecimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.US)

            if (this <= 0) return "0"
            val units = arrayOf("B", "kB", "MB", "GB", "TB")
            val digitGroups = (Math.log10(this.toDouble()) / Math.log10(1024.0)).toInt()
            return DecimalFormat(
                "#,##0.#",
                symbolsEN_US
            ).format(this / 1024.0.pow(digitGroups.toDouble()))
                .toString() + " " + units[digitGroups]
            //  return android.text.format.Formatter.formatShortFileSize(context, size)

        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return ""

    }


    private fun getMimeType(url: String?): String? {
        var type: String? = null
        val extension = MimeTypeMap.getFileExtensionFromUrl(url)
        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
        }
        return type
    }


    private var counter = 0L
    private fun showInterAdWhenOpeningFile(file: DocumentsModel?) {
        isAlive {
            sharedViewModel.selectedDoc = file
            /*  val annotAction =
                  HomeFragmentDirections.actionHomeFragmentToPdfViewFragment()
              findNavController().navigate(annotAction)*/
            isFromAssetViewer = isFromAsset
            activity?.startAppActivity<ViewerActivity>(
                Pair("pdf_file", file),
            )
            if (AppOpenManager.isShowingAd) {
                logs("AppOpenManager is showing can't load interstitial ad")
                return@isAlive
            }
            counter++
            if (counter == adViewModel.getRemoteConfigModel().fileClickInterAdCount) {
                counter = 0
                adsIds.apply {
                    homeFileOpenInterAd.run {
                        canRequestAd = storage.isConsentDone
                        remoteConfig =
                            adViewModel.getRemoteConfigModel().adConfigModel.homeFileOpenInterAd.show
                    }
                    activity?.let { it1 ->
                        adViewModel.showInterstitialAd(
                            activity = it1,
                            adInfo = homeFileOpenInterAd,
                            onAdNotFound = {
                                loadOpenInterFileClickAd()

                                AdUtility.isFromInterOpenFailed = true
                            },
                            onAdDismiss = {
                                loadOpenInterFileClickAd()
                                AdUtility.isFromInterOpenFailed = false
                            },
                            onAdFailedToLoad = {
                                loadOpenInterFileClickAd()
                                AdUtility.isFromInterOpenFailed = true
                            },

                            )
                    }
                }
            } else {
                AdUtility.isFromInterOpenFailed = true
            }
        }
    }

    private fun loadAds() {
        adViewModel.getNetworkStatus().observe(viewLifecycleOwner) {
            if (it) {
                lifecycleScope.launch {
                    kotlinx.coroutines.delay(2000L)
                    loadOpenInterFileClickAd()
                    if (!mViewDataBinding.searchToolbar.isVisible) {
                        logs("loadNativeCall-->home")
                        loadNativeCall()
                    } else {
                        mViewDataBinding.apply {
                            adViewModel.apply {
                                if (getNativeAdMap().containsKey(adsIds.homeSearchNative.adKey)) {
                                    logs("searchNativeFound-> for key")
                                    getNativeAdMap()[adsIds.homeSearchNative.adKey]?.nativeAd?.let {
                                        logs("search native ad exist")
                                        shimmerSearch.shimmer.hide()
                                        nativeAdLayoutSearch.nativeAdView.show()
                                    }
                                } else {
                                    nativeAdLayoutSearch.nativeAdView.hide()
                                    shimmerSearch.shimmer.hide()
                                }
                            }
                        }

                    }
                }


            }
        }

    }

    private fun loadOpenInterFileClickAd() {
        adsIds.apply {
            homeFileOpenInterAd.run {
                canRequestAd = storage.isConsentDone
                remoteConfig =
                    adViewModel.getRemoteConfigModel().adConfigModel.homeFileOpenInterAd.show
            }
            adViewModel.loadInterstitialAd(adInfo = homeFileOpenInterAd,
                onAdAlreadyLoaded = { AdUtility.isFromInterOpenFailed = false },
                onAdNotFound = { AdUtility.isFromInterOpenFailed = false })
        }
    }

    private fun loadNativeCall() {
        mViewDataBinding.apply {
            shimmerSearch.shimmer.hide()
            nativeAdLayoutSearch.nativeAdView.hide()
            adViewModel.apply {
                if (getRemoteConfigModel().homeAdPlacementAdPosition == 0L) {
                    if (getRemoteConfigModel().homeAdPlacementAd == 1L) {
                        shimmer1.shimmer.hide()
                        nativeAdLayout1.nativeAdView.hide()
                        bannerView.adViewContainer.hide()
                        bannerView1.adViewContainer.hide()
                        loadHomeNative(shimmer.shimmer, nativeAdLayout, nativeAdLayout.nativeAdView)
                    } else {
                        shimmer.shimmer.hide()
                        shimmer1.shimmer.hide()
                        nativeAdLayout.nativeAdView.hide()
                        nativeAdLayout1.nativeAdView.hide()
                        bannerView.adViewContainer.show()
                        bannerView1.adViewContainer.hide()

                        loadBannerHome(bannerView.adViewContainer)
                    }
                } else {
                    if (getRemoteConfigModel().homeAdPlacementAd == 1L) {
                        shimmer.shimmer.hide()
                        nativeAdLayout.nativeAdView.hide()
                        bannerView.adViewContainer.hide()
                        bannerView1.adViewContainer.hide()
                        loadHomeNative(
                            shimmer1.shimmer,
                            nativeAdLayout1,
                            nativeAdLayout1.nativeAdView
                        )
                    } else {
                        shimmer.shimmer.hide()
                        shimmer1.shimmer.hide()
                        nativeAdLayout.nativeAdView.hide()
                        nativeAdLayout1.nativeAdView.hide()
                        bannerView.adViewContainer.hide()
                        bannerView1.adViewContainer.show()
                        loadBannerHome(bannerView1.adViewContainer)
                    }
                }
            }
        }
    }

    private fun loadBannerHome(adViewContainer: FrameLayout) {
        adsIds.apply {
            adViewModel.apply {
                homeBanner.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = adViewModel.getRemoteConfigModel().adConfigModel.homeBanner.show
                }
                context?.let {
                    loadBanner(bannerAdInfo = homeBanner,
                        context = it,
                        view = adViewContainer,
                        onBannerNotFound = { manageFrameLayoutView(adViewContainer) },
                        onBannerLoaded = { i, hashMap ->
                            displayBannerAd(adViewContainer, hashMap[i]?.adView)
                        },
                        onBannerFailedToLoad = { manageFrameLayoutView(adViewContainer) })
                }
            }
        }

    }

    private fun loadHomeNative(
        shimmer: ShimmerFrameLayout,
        nativeAdLayout: HomeNativeAdLayoutBinding,
        nativeAdView: NativeAdView
    ) {
        mViewDataBinding.apply {
            adsIds.apply {
                shimmer.show()
//                nativeAdView.show()
                homeNative.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = adViewModel.getRemoteConfigModel().adConfigModel.homeNative.show
                }
                adViewModel.loadNativeAd(nativeAdInfo = homeNative,
                    onAdNotFound = {
                        shimmer.hide()
                        nativeAdView.hide()

                    },
                    onAdLoaded = { i, hashMap ->
                        shimmer.hide()
                        nativeAdView.show()
                        hashMap[i]?.nativeAd?.let { populateNative(it, nativeAdLayout) }
                    },
                    onAdFailedToLoad = {
                        shimmer.hide()
                        nativeAdView.hide()
                    })
            }
        }

    }

    private fun populateNative(nativeAd: NativeAd, nativeAdLayout: HomeNativeAdLayoutBinding) {
        isAlive {
            val binding: HomeNativeAdLayoutBinding = nativeAdLayout
            binding.nativeAdView.headlineView = binding.txtTitle
            binding.nativeAdView.callToActionView = binding.btnCallToAction

            binding.btnCallToAction.text = nativeAd.callToAction
            binding.txtAdDescription.text = nativeAd.body
            (binding.nativeAdView.headlineView as AppCompatTextView).text = nativeAd.headline
            binding.imgIcon.setImageDrawable(nativeAd.icon?.drawable)

            binding.nativeAdView.setNativeAd(nativeAd)
            binding.parent.show()

        }

    }

    private fun enableSearch() {
        mViewDataBinding.mainToolbar.hideWithAnimation(
            object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {


                }

                override fun onAnimationEnd(animation: Animation?) {
                    mViewDataBinding.mainToolbar.hide()
                    mViewDataBinding.searchToolbar.show()
                    mViewDataBinding.edtSearch.showKeyboard()
                    mViewDataBinding.edtSearch.setText("")
                    mViewDataBinding.edtSearch.requestFocus()
                    mViewDataBinding.edtSearch.showKeyboard()
                    if (isFromAsset) {
                        hideOtherAdsView()
                        filesAdapter?.setData(emptyList(), isFromAsset)
                    } else {
                        filesAdapter?.setSearchView(true)
                        showSearchNative()
                    }

                }

                override fun onAnimationRepeat(animation: Animation?) {

                }
            }
        )
    }

    private fun showSearchNative() {
        adViewModel.apply {
            adsIds.apply {
                mViewDataBinding.apply {
                    if (filesAdapter?.itemCount!! > 0) {
                        hideOtherAdsView()
                        homeSearchNative.run {
                            canRequestAd = storage.isConsentDone
                            remoteConfig =
                                adViewModel.getRemoteConfigModel().adConfigModel.homeSearchNative.show
                        }
                        shimmerSearch.shimmer.show()
                        loadNativeAd(nativeAdInfo = homeSearchNative,
                            onAdNotFound = {
                                shimmerSearch.shimmer.hide()
                                nativeAdLayoutSearch.nativeAdView.hide()
                            },
                            onAdLoaded = { i, hashMap ->
                                shimmerSearch.shimmer.hide()
                                nativeAdLayoutSearch.nativeAdView.show()
                                hashMap[i]?.nativeAd?.let {
                                    populateNative(
                                        it,
                                        nativeAdLayoutSearch
                                    )
                                }
                            },
                            onAdFailedToLoad = {
                                shimmerSearch.shimmer.hide()
                                nativeAdLayoutSearch.nativeAdView.hide()
                            })
                    }
                }
            }
        }
    }

    private fun hideOtherAdsView() {
        mViewDataBinding.apply {
            shimmer.shimmer.hide()
            shimmer1.shimmer.hide()
            bannerView.adViewContainer.hide()
            bannerView1.adViewContainer.hide()
            nativeAdLayout.nativeAdView.hide()
            nativeAdLayout1.nativeAdView.hide()
        }
    }

    private fun disableSearch() {
        mViewDataBinding.mainToolbar.showWithAnimation(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                mViewDataBinding.mainToolbar.show()
            }

            override fun onAnimationRepeat(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                mViewDataBinding.searchToolbar.hide()
                mViewDataBinding.edtSearch.hideKeyboard()
                filesAdapter?.setData(fileDocList, isFromAsset)
                filesAdapter?.filter?.filter("")
                filesAdapter?.setSearchView(false)
                if (filesAdapter?.itemCount!! > 0) {
                    showOtherAdsIfAvailable()
                }

            }
        })
    }

    private fun showOtherAdsIfAvailable() {
        adViewModel.apply {
            adsIds.apply {
                mViewDataBinding.apply {
                    hideOtherAdsView()
                    nativeAdLayoutSearch.nativeAdView.hide()
                    shimmerSearch.shimmer.hide()
                    if (getRemoteConfigModel().homeAdPlacementAdPosition == 0L) {
                        if (getRemoteConfigModel().homeAdPlacementAd == 1L) {
                            if (getNativeAdMap().containsKey(homeNative.adKey)) {
                                nativeAdLayout.nativeAdView.show()
                            }
                        } else {
                            if (getBannerAdMap().containsKey(homeBanner.adKey)) {
                                bannerView.adViewContainer.show()
                            }
                        }
                    } else {
                        if (getRemoteConfigModel().homeAdPlacementAd == 1L) {
                            if (getNativeAdMap().containsKey(homeNative.adKey)) {
                                nativeAdLayout1.nativeAdView.show()
                            }
                        } else {
                            if (getBannerAdMap().containsKey(homeBanner.adKey)) {
                                bannerView1.adViewContainer.show()
                            }
                        }
                    }
                }
            }
        }
    }

    private fun getFileFromUri(uri: Uri): DocumentsModel? {
        var documentModel: DocumentsModel? = null
        try {

            uri.scheme?.let { scheme ->


                if (scheme == "file") {
                    val path = uri.path!!
                    val file = File(path)
                    val name = file.nameWithoutExtension
                    val date = file.lastModified() / 1000 //convert to seconds
                    val size = file.length()
                    documentModel = DocumentsModel()
                    documentModel?.fileName = name
                    documentModel?.absolutePath = path
                    documentModel?.fileSize = mViewModel.getReadableSize(size)
                    documentModel?.dateInDigit = date
                    documentModel?.fileDate = mViewModel.getFormattedDate(date)
                    documentModel?.parentFile = file.parent!!
                } else {
                    val pdfExt = "_data LIKE '%.pdf'"
                    var cursor: Cursor? = null
                    val docsProjection = arrayOf(MediaStore.Files.FileColumns.DATA)
                    isAlive {
                        cursor = it.contentResolver.query(
                            uri,
                            docsProjection,
                            pdfExt,
                            null,
                            null
                        )
                    }

                    if (cursor != null && cursor?.columnCount!! > 0) {

                        while (cursor?.moveToNext() == true) {
                            documentModel = try {
                                val path =
                                    cursor?.getString(cursor?.getColumnIndex(MediaStore.Files.FileColumns.DATA)!!)
                                Log.d("TAG", "getFileFromUri: get file try $path")
                                if (path == null) {
                                    val path =
                                        copyFileToInternalStorage(uri, "filesWithoutExtension")
                                    getPDFFile(path)
                                } else
                                    getPDFFile(path)
                            } catch (ee: java.lang.Exception) {
                                val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                                Log.d("TAG", "getFileFromUri: get file catch")
                                getPDFFile(path)
                            }
                        }
                        cursor?.close()
                    } else {
                        val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                        documentModel = getPDFFile(path)
                    }
                }
            }
            return documentModel
        } catch (ee: java.lang.Exception) {
            return null
        }
    }

    private fun copyFileToInternalStorage(
        uri: Uri,
        newDirName: String,
    ): String {
        isAlive {

        }
        val returnCursor = activity?.contentResolver?.query(
            uri,
            arrayOf(OpenableColumns.DISPLAY_NAME, OpenableColumns.SIZE),
            null,
            null,
            null
        )
        /*
         * Get the column indexes of the data in the Cursor,
         *     * move to the first row in the Cursor, get the data,
         *     * and display it.
         * */
        if (returnCursor?.columnCount ?: 0 > 0) {
            val column_index = returnCursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME) ?: -1
            if (returnCursor?.moveToFirst() == true && column_index >= 0) {
                val name = returnCursor.getString(column_index)
                val output: File = if (newDirName != "") {
                    val dir = File("${activity?.filesDir}/$newDirName")
                    if (!dir.exists()) {
                        dir.mkdir()
                    }
                    File("${activity?.filesDir}/$newDirName/$name")
                } else {
                    File("${activity?.filesDir}/$name")
                }
                try {
                    val inputStream = activity?.contentResolver?.openInputStream(uri)
                    val outputStream = FileOutputStream(output)
                    var read: Int
                    val bufferSize = 1024
                    val buffers = ByteArray(bufferSize)
                    while (inputStream!!.read(buffers).also { read = it } != -1) {
                        outputStream.write(buffers, 0, read)
                    }
                    inputStream.close()
                    outputStream.close()
                } catch (e: Exception) {
                    Log.e("error", e.message!!)
                }
                return output.path
            }
        }
        return ""
    }

    private fun getPDFFile(path: String?): DocumentsModel? {
        return if (path != null) {
            val name: String?
            val date: Long?
            val size: Long?
            val file = File(path)
            name = file.nameWithoutExtension
            date = file.lastModified() / 1000 //convert to seconds
            size = file.length()
            DocumentsModel(
                name,
                mViewModel
                    .getReadableSize(size),
                fileDate = mViewModel.getReadableSize(date),
                parentFile = file.parent!!,
                absolutePath = path,
                dateInDigit = date,
                sizeInDigit = size
            )
        } else null
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PublicValue.KEY_REQUEST_PERMISSIONS) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                isAlive {
                    sharedViewModel.loadFiles(requireActivity())
                }

            } else {
                exitProcess(0)

            }

        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PublicValue.KEY_REQUEST_PERMISSIONS_11) {
            isAlive {
                if (it.checkStoragePermissionGranted()) {
                    showToast("Permission allowed, thank you.")
                    sharedViewModel.loadFiles(requireActivity())
                } else {
                    exitProcess(0)
                }
            }


        }
    }


}