package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.edit

import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.Compression
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ConverstionType
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.shared.Progress
import java.io.File
import javax.inject.Inject

@HiltViewModel
class EditViewModel @Inject constructor(
    private val repositoryLocal: RepositoryLocal
) : BaseViewModel()