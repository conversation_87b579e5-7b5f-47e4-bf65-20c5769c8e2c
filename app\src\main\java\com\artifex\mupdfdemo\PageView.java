package com.artifex.mupdfdemo;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import java.util.ArrayList;
import java.util.Iterator;

class OpaqueImageView extends androidx.appcompat.widget.AppCompatImageView {

    public OpaqueImageView(Context context) {
        super(context);
    }

    @Override
    public boolean isOpaque() {
        return true;
    }
}

interface TextProcessor {
    void onStartLine();

    void onWord(TextWord word);

    void onEndLine();
}

class TextSelector {
    final private TextWord[][] mText;
    final private RectF mSelectBox;

    public TextSelector(TextWord[][] text, RectF selectBox) {
        mText = text;
        mSelectBox = selectBox;
    }

    public void select(TextProcessor tp) {
        if (mText == null || mSelectBox == null)
            return;

        ArrayList<TextWord[]> lines = new ArrayList<TextWord[]>();
        for (TextWord[] line : mText)
            if (line[0].bottom > mSelectBox.top && line[0].top < mSelectBox.bottom)
                lines.add(line);

        Iterator<TextWord[]> it = lines.iterator();
        while (it.hasNext()) {
            TextWord[] line = it.next();
            boolean firstLine = line[0].top < mSelectBox.top;
            boolean lastLine = line[0].bottom > mSelectBox.bottom;
            float start = Float.NEGATIVE_INFINITY;
            float end = Float.POSITIVE_INFINITY;

            if (firstLine && lastLine) {
                start = Math.min(mSelectBox.left, mSelectBox.right);
                end = Math.max(mSelectBox.left, mSelectBox.right);
            } else if (firstLine) {
                start = mSelectBox.left;
            } else if (lastLine) {
                end = mSelectBox.right;
            }

            tp.onStartLine();

            for (TextWord word : line)
                if (word.right > start && word.left < end)
                    tp.onWord(word);

            tp.onEndLine();
        }
    }
}

public abstract class PageView extends ViewGroup {
    private static final int HIGHLIGHT_COLOR = 0x802572AC;
    private static final int LINK_COLOR = 0x80AC7225;
    private static final int BOX_COLOR = 0xFF4444FF;
    protected int INK_COLOR = 0;
    protected float INK_THICKNESS = 10.0f;
    private int BACKGROUND_COLOR = 0xFFFFFFFF;
    private static final int PROGRESS_DIALOG_DELAY = 200;
    private static final String TAG = "PageView";

    private static final int SIGN_HEIGHT = 50;
    private static final int SIGN_WIDTH = 100;

    protected final Context mContext;
    protected int mPageNumber;
    private Point mParentSize; // Size of the view containing the pdf viewer. It could be the same as the screen if this view is full screen.
    protected Point mSize;   // Size of page at minimum zoom
    protected float mSourceScale;

    private ImageView mEntire; // Image rendered at minimum zoom
    private Bitmap mEntireBm; // Bitmap used to draw the entire page at minimum zoom.
    private Matrix mEntireMat;
    private AsyncTask<Void, Void, TextWord[][]> mGetText;
    private AsyncTask<Void, Void, LinkInfo[]> mGetLinkInfo;
    private CancellableAsyncTask<Void, Void> mDrawEntire;

    private Point mPatchViewSize; // View size on the basis of which the patch was created. After zoom.
    private Rect mPatchArea; // Area of the screen zoomed.
    private ImageView mPatch; // Image rendered at zoom resolution.
    private Bitmap mPatchBm; // Bitmap used to draw the zoomed image.
    private CancellableAsyncTask<Void, Void> mDrawPatch;
    private RectF mSearchBoxes[];
    protected LinkInfo mLinks[];
    private RectF mSelectBox;
    private TextWord mText[][];
    private RectF mItemSelectBox;
    protected ArrayList<ArrayList<PointF>> mDrawing;
    private View mSearchView;
    private boolean mIsBlank;
    private boolean mHighlightLinks;
    MuPDFCore mCore;
    private float current_scale;

    //    private ProgressBar mBusyIndicator;
    private final Handler mHandler = new Handler();


    private MuPDFPageAdapter mAdapter;

    private boolean flagHQ = false;
    //    public boolean mInvertColor;
    private ColorType mColorType;

    public PageView(Context ctx, Point parentSize, MuPDFPageAdapter adapter, ColorType colorType) {
        super(ctx);
        mContext = ctx;
        mColorType = colorType;


        mParentSize = parentSize;
        setBackgroundColor(BACKGROUND_COLOR);
        mEntireMat = new Matrix();
        mAdapter = adapter;
//        switch (mColorType) {
//            case GREY: {
//
//                BACKGROUND_COLOR = 0xFFBDBDBD;
//                break;
//            }
//
//            case YELLOW: {
//
//                BACKGROUND_COLOR = 0xFFFFF9C4;
//                break;
//            }
//
//            case BLACK: {
//                BACKGROUND_COLOR = 0xFF000000;
//                break;
//            }
//
//            default:
//                BACKGROUND_COLOR = 0xFFFFFFFF;
//                break;
//        }
    }

    protected abstract CancellableTaskDefinition<Void, Void> getDrawPageTask(Bitmap bm, int sizeX, int sizeY, int patchX, int patchY, int patchWidth, int patchHeight);

    protected abstract CancellableTaskDefinition<Void, Void> getUpdatePageTask(Bitmap bm, int sizeX, int sizeY, int patchX, int patchY, int patchWidth, int patchHeight);

    protected abstract LinkInfo[] getLinkInfo();

    protected abstract TextWord[][] getText();

    protected abstract void addMarkup(PointF[] quadPoints, Annotation.Type type);

    private void reinit() {
        // Cancel pending render task
        if (mDrawEntire != null) {
            mDrawEntire.cancelAndWait();
            mDrawEntire = null;
        }

        if (mDrawPatch != null) {
            mDrawPatch.cancelAndWait();
            mDrawPatch = null;
        }

        if (mGetLinkInfo != null) {
            mGetLinkInfo.cancel(true);
            mGetLinkInfo = null;
        }

        if (mGetText != null) {
            mGetText.cancel(true);
            mGetText = null;
        }

        mIsBlank = true;
        mPageNumber = 0;

        if (mSize == null)
            mSize = mParentSize;

        if (mEntire != null) {
            mEntire.setImageBitmap(null);
            mEntire.invalidate();
        }

        if (mPatch != null) {
            mPatch.setImageBitmap(null);
            mPatch.invalidate();
        }

        mPatchViewSize = null;
        mPatchArea = null;

        mSearchBoxes = null;
        mLinks = null;
        mSelectBox = null;
        mText = null;
        mItemSelectBox = null;
    }

    public void releaseResources() {
        releaseBitmaps();

        reinit();

//        if (mBusyIndicator != null) {
//            removeView(mBusyIndicator);
//            mBusyIndicator = null;
//        }
    }

    public void releaseBitmaps() {
        if (mEntire != null) {
            mEntire.setImageBitmap(null);
            mEntire.invalidate();
        }

        if (mPatch != null) {
            mPatch.setImageBitmap(null);
            mPatch.invalidate();
        }

        Log.i(TAG, "Recycle mEntire on releaseBitmaps: " + mEntireBm);
        recycleBitmap(mEntireBm);
        mEntireBm = null;
        Log.i(TAG, "Recycle mPathBm on releaseBitmaps: " + mPatchBm);
        recycleBitmap(mPatchBm);
        mPatchBm = null;
    }

    public void blank(int page) {
        reinit();
        mPageNumber = page;

//        if (mBusyIndicator == null) {
//            mBusyIndicator = new ProgressBar(mContext);
//            mBusyIndicator.setIndeterminate(true);
//            mBusyIndicator.setBackgroundResource(R.drawable.busy);
//            addView(mBusyIndicator);
//        }

        setBackgroundColor(BACKGROUND_COLOR);
    }

    public void setPage(int page, PointF size) {
//        pdfSize = correctBugMuPdf(size);

        if (mEntireBm == null) {
            try {
                mEntireBm = Bitmap.createBitmap(mParentSize.x, mParentSize.y, Config.ARGB_8888);
            } catch (OutOfMemoryError e) {
                e.printStackTrace();
            }
        }

        // Cancel pending render task
        if (mDrawEntire != null) {
            mDrawEntire.cancelAndWait();
            mDrawEntire = null;
        }

        mIsBlank = false;
        // Highlights may be missing because mIsBlank was true on last draw
        if (mSearchView != null)
            mSearchView.invalidate();

        mPageNumber = page;
        if (mEntire == null) {
            mEntire = new OpaqueImageView(mContext);
            mEntire.setScaleType(ImageView.ScaleType.MATRIX);
//            mEntire.setColorFilter(getColorFilter());
            addView(mEntire, 0);
        }

        // Calculate scaled size that fits within the screen limits
        // This is the size at minimum zoom
        mSourceScale = Math.min(mParentSize.x / size.x, mParentSize.y / size.y);
        Point newSize = new Point((int) (size.x * mSourceScale), (int) (size.y * mSourceScale));
        mSize = newSize;

        mEntire.setImageBitmap(null);
        mEntire.invalidate();

        // Get the link info in the background
        mGetLinkInfo = new AsyncTask<Void, Void, LinkInfo[]>() {
            protected LinkInfo[] doInBackground(Void... v) {
                return getLinkInfo();
            }

            protected void onPostExecute(LinkInfo[] v) {
                mLinks = v;
                if (mSearchView != null)
                    mSearchView.invalidate();
            }
        };

        mGetLinkInfo.execute();

        try {
            updateEntireCanvas(false);
        } catch (OutOfMemoryError e) {
           e.printStackTrace();
        }catch (Exception e) {
           e.printStackTrace();
        }

        if (mSearchView == null) {
            mSearchView = new View(mContext) {
                @Override
                protected void onDraw(final Canvas canvas) {
                    super.onDraw(canvas);
                    // Work out current total scale factor
                    // from source to view
                    final float scale = mSourceScale * (float) getWidth() / (float) mSize.x;
                    final Paint paint = new Paint();

                    if (!mIsBlank && mSearchBoxes != null) {
                        paint.setColor(HIGHLIGHT_COLOR);
                        for (RectF rect : mSearchBoxes)
                            canvas.drawRect(rect.left * scale, rect.top * scale,
                                    rect.right * scale, rect.bottom * scale,
                                    paint);
                    }

                    if (!mIsBlank && mLinks != null && mHighlightLinks) {
                        paint.setColor(LINK_COLOR);
                        for (LinkInfo link : mLinks)
                            canvas.drawRect(link.rect.left * scale, link.rect.top * scale,
                                    link.rect.right * scale, link.rect.bottom * scale,
                                    paint);
                    }

                    if (mSelectBox != null && mText != null) {
                        paint.setColor(HIGHLIGHT_COLOR);
                        processSelectedText(new TextProcessor() {
                            RectF rect;

                            public void onStartLine() {
                                rect = new RectF();
                            }

                            public void onWord(TextWord word) {
                                rect.union(word);
                            }

                            public void onEndLine() {
                                if (!rect.isEmpty())
                                    canvas.drawRect(rect.left * scale, rect.top * scale, rect.right * scale, rect.bottom * scale, paint);
                            }
                        });
                    }

                    if (mItemSelectBox != null) {
                        paint.setStyle(Paint.Style.STROKE);
                        paint.setColor(BOX_COLOR);
                        canvas.drawRect(mItemSelectBox.left * scale, mItemSelectBox.top * scale, mItemSelectBox.right * scale, mItemSelectBox.bottom * scale, paint);
                    }

                    if (mDrawing != null) {
                        Path path = new Path();
                        PointF p;

                        paint.setAntiAlias(true);
                        paint.setDither(true);
                        paint.setStrokeJoin(Paint.Join.ROUND);
                        paint.setStrokeCap(Paint.Cap.ROUND);

                        paint.setStyle(Paint.Style.FILL);
                        paint.setStrokeWidth(INK_THICKNESS * scale);
                        paint.setColor(ColorPalette.getHex(INK_COLOR, Annotation.Type.INK));

                        Iterator<ArrayList<PointF>> it = mDrawing.iterator();
                        while (it.hasNext()) {
                            ArrayList<PointF> arc = it.next();
                            if (arc.size() >= 2) {
                                Iterator<PointF> iit = arc.iterator();
                                p = iit.next();
                                float mX = p.x * scale;
                                float mY = p.y * scale;
                                path.moveTo(mX, mY);
                                while (iit.hasNext()) {
                                    p = iit.next();
                                    float x = p.x * scale;
                                    float y = p.y * scale;
                                    path.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);
                                    mX = x;
                                    mY = y;
                                }
                                path.lineTo(mX, mY);
                            } else {
                                p = arc.get(0);
                                canvas.drawCircle(p.x * scale, p.y * scale, INK_THICKNESS * scale / 2, paint);
                            }
                        }

                        paint.setStyle(Paint.Style.STROKE);
                        canvas.drawPath(path, paint);
                    }
                }
            };

            addView(mSearchView);
        }
        requestLayout();
    }

    public ColorFilter getColorFilter() {

        return new ColorMatrixColorFilter(new ColorMatrix(new float[]{-1.0f, 0.0f, 0.0f, 0.0f, 255.0f, 0.0f, -1.0f, 0.0f, 0.0f, 255.0f, 0.0f, 0.0f, -1.0f, 0.0f, 255.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f}));
    }

    public void updateEntireCanvas(final boolean updateZoomed) {
        // Render the page in the background
        mDrawEntire = new CancellableAsyncTask<Void, Void>(getDrawPageTask(mEntireBm, mSize.x, mSize.y, 0, 0, mSize.x, mSize.y)) {

            @Override
            public void cancelAndWait() {
                super.cancelAndWait();
                flagHQ = false;
            }

            @Override
            public void onPreExecute() {
                setBackgroundColor(BACKGROUND_COLOR);
                mEntire.setImageBitmap(null);
                mEntire.invalidate();

//                if (mBusyIndicator == null) {
//                    mBusyIndicator = new ProgressBar(mContext);
//                    mBusyIndicator.setIndeterminate(true);
//                    mBusyIndicator.setBackgroundResource(R.drawable.busy);
//                    addView(mBusyIndicator);
//                    mBusyIndicator.setVisibility(INVISIBLE);
//                    mHandler.postDelayed(new Runnable() {
//                        public void run() {
//                            if (mBusyIndicator != null)
//                                mBusyIndicator.setVisibility(VISIBLE);
//                        }
//                    }, PROGRESS_DIALOG_DELAY);
//                }
            }

            @Override
            public void onPostExecute(Void result) {
//                removeView(mBusyIndicator);
//                mBusyIndicator = null;
//                if (mInvertColor)
                mEntire.setImageBitmap(invert(mEntireBm));
//                else
//                    mEntire.setImageBitmap(mEntireBm);

                // Draws the signatures on EntireCanvas after changing pages (post loading).
//                if (mEntireBm != null && !mEntireBm.isRecycled()) {
//                    Canvas entireCanvas = new Canvas(mEntireBm);
//                    drawBitmaps(entireCanvas, null, null);
//                }

//                if (updateZoomed && (mPatchBm != null) && !mPatchBm.isRecycled()) {
//                    Canvas zoomedCanvas = new Canvas(mPatchBm);
//                    drawBitmaps(zoomedCanvas, mPatchViewSize, mPatchArea);
//                }
                flagHQ = false;
                mEntire.invalidate();
                setBackgroundColor(Color.TRANSPARENT);

            }
        };

        mDrawEntire.execute();

    }

    public void setSearchBoxes(RectF searchBoxes[]) {
        mSearchBoxes = searchBoxes;
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    public void setLinkHighlighting(boolean f) {
        mHighlightLinks = f;
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    public void deselectText() {
        mSelectBox = null;
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    public void selectText(float x0, float y0, float x1, float y1) {
        float scale = mSourceScale * (float) getWidth() / (float) mSize.x;
        float docRelX0 = (x0 - getLeft()) / scale;
        float docRelY0 = (y0 - getTop()) / scale;
        float docRelX1 = (x1 - getLeft()) / scale;
        float docRelY1 = (y1 - getTop()) / scale;
        // Order on Y but maintain the point grouping
        if (docRelY0 <= docRelY1)
            mSelectBox = new RectF(docRelX0, docRelY0, docRelX1, docRelY1);
        else
            mSelectBox = new RectF(docRelX1, docRelY1, docRelX0, docRelY0);

        if (mSearchView != null)
            mSearchView.invalidate();

        if (mGetText == null) {
            mGetText = new AsyncTask<Void, Void, TextWord[][]>() {
                @Override
                protected TextWord[][] doInBackground(Void... params) {
                    return getText();
                }

                @Override
                protected void onPostExecute(TextWord[][] result) {
                    mText = result;
                    if (mSearchView != null)
                        mSearchView.invalidate();
                }
            };

            mGetText.execute();
        }
    }

    public void startDraw(float x, float y) {
        float scale = mSourceScale * (float) getWidth() / (float) mSize.x;
        float docRelX = (x - getLeft()) / scale;
        float docRelY = (y - getTop()) / scale;
        if (mDrawing == null)
            mDrawing = new ArrayList<ArrayList<PointF>>();

        ArrayList<PointF> arc = new ArrayList<PointF>();
        arc.add(new PointF(docRelX, docRelY));
        mDrawing.add(arc);
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    public void continueDraw(float x, float y) {
        float scale = mSourceScale * (float) getWidth() / (float) mSize.x;
        float docRelX = (x - getLeft()) / scale;
        float docRelY = (y - getTop()) / scale;

        if (mDrawing != null && mDrawing.size() > 0) {
            ArrayList<PointF> arc = mDrawing.get(mDrawing.size() - 1);
            arc.add(new PointF(docRelX, docRelY));
            if (mSearchView != null)
                mSearchView.invalidate();
        }
    }

    public void cancelDraw() {
        mDrawing = null;
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    protected PointF[][] getDraw() {
        if (mDrawing == null)
            return null;

        PointF[][] path = new PointF[mDrawing.size()][];

        for (int i = 0; i < mDrawing.size(); i++) {
            ArrayList<PointF> arc = mDrawing.get(i);
            path[i] = arc.toArray(new PointF[arc.size()]);
        }

        return path;
    }

    protected void processSelectedText(TextProcessor tp) {
        (new TextSelector(mText, mSelectBox)).select(tp);
    }

    protected String getSelectedString() {
        final StringBuilder text = new StringBuilder();

        processSelectedText(new TextProcessor() {
            StringBuilder line;

            public void onStartLine() {
                line = new StringBuilder();
            }

            public void onWord(TextWord word) {
                if (line.length() > 0)
                    line.append(' ');
                line.append(word.w);
            }

            public void onEndLine() {
                if (text.length() > 0)
                    text.append('\n');
                text.append(line);
            }
        });
        if (text.toString().trim().isEmpty())
            return null;
        else return text.toString().trim();
    }

    public void setItemSelectBox(RectF rect) {
        mItemSelectBox = rect;
        if (mSearchView != null)
            mSearchView.invalidate();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int x, y;
        switch (MeasureSpec.getMode(widthMeasureSpec)) {
            case MeasureSpec.UNSPECIFIED:
                x = mSize.x;
                break;
            default:
                x = MeasureSpec.getSize(widthMeasureSpec);
        }
        switch (MeasureSpec.getMode(heightMeasureSpec)) {
            case MeasureSpec.UNSPECIFIED:
                y = mSize.y;
                break;
            default:
                y = MeasureSpec.getSize(heightMeasureSpec);
        }

        setMeasuredDimension(x, y);

//        if (mBusyIndicator != null) {
//            int limit = Math.min(mParentSize.x, mParentSize.y) / 2;
//            mBusyIndicator.measure(MeasureSpec.AT_MOST | limit, MeasureSpec.AT_MOST | limit);
//        }
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int w = right - left;
        int h = bottom - top;

        if (mEntire != null) {
            if (mEntire.getWidth() != w || mEntire.getHeight() != h) {
                mEntireMat.setScale(w / (float) mSize.x, h / (float) mSize.y);
                mEntire.setImageMatrix(mEntireMat);
                mEntire.invalidate();
            }
            mEntire.layout(0, 0, w, h);
        }

        if (mSearchView != null) {
            mSearchView.layout(0, 0, w, h);
        }

        if (mPatchViewSize != null) {
            if (mPatchViewSize.x != w || mPatchViewSize.y != h) {
                // Zoomed since patch was created
                mPatchViewSize = null;
                mPatchArea = null;
                if (mPatch != null) {
                    mPatch.setImageBitmap(null);
                    mPatch.invalidate();
                }
            } else {
                mPatch.layout(mPatchArea.left, mPatchArea.top, mPatchArea.right, mPatchArea.bottom);
            }
        }

//        if (mBusyIndicator != null) {
//            int bw = mBusyIndicator.getMeasuredWidth();
//            int bh = mBusyIndicator.getMeasuredHeight();
//
//            mBusyIndicator.layout((w - bw) / 2, (h - bh) / 2, (w + bw) / 2, (h + bh) / 2);
//        }
    }

    public void updateHq(boolean update) {
        if (!flagHQ) {
            flagHQ = true;
            Rect viewArea = new Rect(getLeft(), getTop(), getRight(), getBottom());

            if (viewArea.width() == mSize.x || viewArea.height() == mSize.y) {
                // If the viewArea's size matches the unzoomed size, there is no need for an hq patch
                if (mPatch != null) {
                    mPatch.setImageBitmap(null);
                    mPatch.invalidate();
                }
                flagHQ = false;
            } else {
                final Point patchViewSize = new Point(viewArea.width(), viewArea.height());
                final Rect patchArea = new Rect(0, 0, mParentSize.x, mParentSize.y);

                // Intersect and test that there is an intersection
                if (!patchArea.intersect(viewArea)) {
                    flagHQ = false;
                    return;
                }

                // Offset patch area to be relative to the view top left
                patchArea.offset(-viewArea.left, -viewArea.top);

                boolean area_unchanged = patchArea.equals(mPatchArea) && patchViewSize.equals(mPatchViewSize);

                // If being asked for the same area as last time and not because of an update then nothing to do
//            if (area_unchanged && !update)
//                return;
//
//            boolean completeRedraw = !(area_unchanged && update);
                boolean completeRedraw = !area_unchanged || update;

                // Stop the drawing of previous patch if still going
                if (mDrawPatch != null) {
                    mDrawPatch.cancelAndWait();
                    mDrawPatch = null;
                }

                // Create and add the image view if not already done
                if (mPatch == null) {
                    mPatch = new OpaqueImageView(mContext);
                    mPatch.setScaleType(ImageView.ScaleType.MATRIX);
                    addView(mPatch);
                    if (mSearchView != null) {
                        mSearchView.bringToFront();
                    }
                }

                CancellableTaskDefinition<Void, Void> task;

                final Bitmap oldPatchBm = mPatchBm;
                try {
                    int mPatchAreaHeight = patchArea.bottom - patchArea.top;
                    int mPatchAreaWidth = patchArea.right - patchArea.left;
                    mPatchBm = Bitmap.createBitmap(mPatchAreaWidth, mPatchAreaHeight, Bitmap.Config.ARGB_8888);
                    Log.i(TAG, "Recycle oldPatchBm on updateHQ: " + oldPatchBm);
                    cancelDraw();
                } catch (OutOfMemoryError e) {
                    Log.e(TAG, e.getMessage(), e);
                    flagHQ = false;
                }

                if (completeRedraw)
                    task = getDrawPageTask(mPatchBm, patchViewSize.x, patchViewSize.y,
                            patchArea.left, patchArea.top,
                            patchArea.width(), patchArea.height());
                else
                    task = getUpdatePageTask(mPatchBm, patchViewSize.x, patchViewSize.y,
                            patchArea.left, patchArea.top,
                            patchArea.width(), patchArea.height());

                mDrawPatch = new CancellableAsyncTask<Void, Void>(task) {

                    @Override
                    public void cancelAndWait() {
                        super.cancelAndWait();
                        flagHQ = false;
                    }

                    public void onPostExecute(Void result) {
                        mPatchViewSize = patchViewSize;
                        mPatchArea = patchArea;

//                        if (mPatchBm != null && !mPatchBm.isRecycled()) {

//                            if (mInvertColor)
                        mPatch.setImageBitmap(invert(mPatchBm));
//                            else
//                                mPatch.setImageBitmap(mPatchBm);
                        mPatch.invalidate();
//                        }

                        //requestLayout();
                        // Calling requestLayout here doesn't lead to a later call to layout. No idea
                        // why, but apparently others have run into the problem.
                        mPatch.layout(mPatchArea.left, mPatchArea.top, mPatchArea.right, mPatchArea.bottom);

//                        if (mPatchBm != null && !mPatchBm.equals(oldPatchBm)) {
//                            recycleBitmap(oldPatchBm);
//                        }
                        flagHQ = false;
                    }
                };

                mDrawPatch.execute();
            }
        }
    }

    public void update() {
        // Cancel pending render task
        if (mDrawEntire != null) {
            mDrawEntire.cancelAndWait();
            mDrawEntire = null;
        }

        if (mDrawPatch != null) {
            mDrawPatch.cancelAndWait();
            mDrawPatch = null;
        }

        mDrawEntire = new CancellableAsyncTask<Void, Void>(getUpdatePageTask(mEntireBm, mSize.x, mSize.y, 0, 0, mSize.x, mSize.y)) {

            @Override
            public void cancelAndWait() {
                super.cancelAndWait();
                flagHQ = false;
            }

            public void onPostExecute(Void result) {
                if (mEntireBm != null && !mEntireBm.isRecycled()) {
//                    Canvas entireCanvas = new Canvas(mEntireBm);
//                    drawBitmaps(entireCanvas, null, null);
//                    if (pageView.mInvertColor) {
//                        l(pageView.mEntireBm);
//                    }
//                    if (mInvertColor)
                    mEntire.setImageBitmap(invert(mEntireBm));
//                    else
//                        mEntire.setImageBitmap(mEntireBm);
                    mEntire.invalidate();
                    flagHQ = false;
                }
            }
        };

        mDrawEntire.execute();

        updateHq(true);
    }

    public void removeHq() {
        // Stop the drawing of the patch if still going
        if (mDrawPatch != null) {
            mDrawPatch.cancelAndWait();
            mDrawPatch = null;
        }

        // And get rid of it
        mPatchViewSize = null;
        mPatchArea = null;
        if (mPatch != null) {
            mPatch.setImageBitmap(null);
            mPatch.invalidate();
        }
        flagHQ = false;
    }

    public int getPage() {
        return mPageNumber;
    }

    @Override
    public boolean isOpaque() {
        return true;
    }

    public void setParentSize(Point parentSize) {
        this.mParentSize = parentSize;
    }

    public void recycleBitmap(Bitmap bitmap) {
        if (bitmap != null) {
            Log.d(TAG, "Recycling bitmap " + bitmap.toString());
            bitmap.recycle();
            if (!bitmap.isRecycled()) {
                Log.e(TAG, "NOT Recycled bitmap " + bitmap.toString());
            }
        }
    }

    private Bitmap invert(Bitmap src) {

        Log.d(TAG, "invert: " + mColorType.name());
        switch (mColorType) {
            case GREY: {

//                Canvas canvas = new Canvas(src);
//                Paint paint = new Paint();
//
//
//                ColorMatrixColorFilter filter = new ColorMatrixColorFilter(ColorType.GREY.getMatrix());
//                paint.setColorFilter(filter);
//
//                canvas.drawBitmap(src, 0, 0, paint);

                return src;
            }

            case YELLOW: {
//                Canvas canvas = new Canvas(src);
//                Paint paint = new Paint();
//
//
//                ColorMatrixColorFilter filter = new ColorMatrixColorFilter(ColorType.YELLOW.getMatrix());
//                paint.setColorFilter(filter);
//
//                canvas.drawBitmap(src, 0, 0, paint);

                return src;
            }

            case BLACK: {
//                Canvas canvas = new Canvas(src);
//                Paint paint = new Paint();
//
//
//                ColorMatrixColorFilter filter = new ColorMatrixColorFilter(ColorType.BLACK.getMatrix());
//                paint.setColorFilter(filter);
//
//                canvas.drawBitmap(src, 0, 0, paint);
                return src;
            }

            default:
                return src;
        }

//        ColorMatrix matrixGrayscale = new ColorMatrix();

//        matrixGrayscale.setSaturation(0);


//        matrixInvert.set(new float[]
//                {
//                        0.98f, 0.0f, 0.0f, 0.0f, 0.0f,
//                        0.0f, 0.99f, 0.0f, 0.0f, 0.0f,
//                        0.0f, 0.0f, 0.81f, 0.0f, 0.0f,
//                        0.0f, 0.0f, 0.0f, 1.0f, 0.0f
//                });
//        matrixInvert.preConcat(matrixGrayscale);


        // src.recycle();

    }

    public void setInvertColor(ColorType colorType) {
        mColorType = colorType;
    }

    public float getCurrentScale() {
        float f6 = this.mSourceScale;
        if (f6 == 0.0f) {
            return 9.07563f;
        }
        return f6;
    }

}
