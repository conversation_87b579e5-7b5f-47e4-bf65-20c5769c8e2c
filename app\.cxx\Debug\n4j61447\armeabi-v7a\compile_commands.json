[{"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfthirdparty.dir\\thirdparty\\mujs\\one.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\mujs\\one.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\mujs\\one.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfthirdparty.dir\\thirdparty\\jbig2dec\\jbig2.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\jbig2dec\\jbig2.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\jbig2dec\\jbig2.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfthirdparty.dir\\thirdparty\\freetype\\src\\type1\\type1.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\freetype\\src\\type1\\type1.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\thirdparty\\freetype\\src\\type1\\type1.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\bbox-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\bbox-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\bbox-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\bitmap.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\bitmap.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\bitmap.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\buffer.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\buffer.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\buffer.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\colorspace.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\colorspace.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\colorspace.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\compressed-buffer.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\compressed-buffer.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\compressed-buffer.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\context.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\context.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\context.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\crypt-aes.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-aes.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-aes.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\crypt-arc4.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-arc4.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-arc4.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\crypt-md5.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-md5.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-md5.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\crypt-sha2.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-sha2.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\crypt-sha2.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\document-all.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\document-all.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\document-all.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\document.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\document.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\document.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-affine.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-affine.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-affine.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-blend.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-blend.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-blend.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-edge.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-edge.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-edge.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-glyph.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-glyph.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-glyph.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-mesh.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-mesh.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-mesh.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-paint.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-paint.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-paint.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-path.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-path.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-path.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-scale-simple.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-scale-simple.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-scale-simple.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\draw-unpack.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-unpack.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\draw-unpack.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\error.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\error.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\error.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-basic.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-basic.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-basic.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-dct.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-dct.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-dct.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-fax.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-fax.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-fax.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-flate.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-flate.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-flate.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-jbig2.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-jbig2.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-jbig2.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-leech.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-leech.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-leech.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-lzw.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-lzw.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-lzw.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\filter-predict.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-predict.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\filter-predict.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\font.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\font.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\font.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\ftoa.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\ftoa.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\ftoa.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\function.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\function.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\function.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\geometry.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\geometry.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\geometry.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\getopt.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\getopt.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\getopt.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\glyph.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\glyph.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\glyph.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\halftone.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\halftone.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\halftone.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\hash.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\hash.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\hash.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\image.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\image.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\image.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\jmemcust.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\jmemcust.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\jmemcust.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\link.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\link.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\link.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\list-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\list-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\list-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-gif.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-gif.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-gif.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-jpeg.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jpeg.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jpeg.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-jpx.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jpx.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jpx.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-jxr.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jxr.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-jxr.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-png.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-png.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-png.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\load-tiff.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-tiff.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\load-tiff.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\memento.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\memento.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\memento.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\memory.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\memory.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\memory.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\outline.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\outline.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\outline.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\output-pcl.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output-pcl.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output-pcl.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\output-pwg.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output-pwg.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output-pwg.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\output.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\output.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\path.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\path.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\path.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\pixmap.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\pixmap.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\pixmap.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\printf.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\printf.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\printf.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\separation.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\separation.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\separation.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\shade.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\shade.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\shade.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stext-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stext-output.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-output.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-output.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stext-paragraph.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-paragraph.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-paragraph.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stext-search.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-search.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stext-search.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\store.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\store.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\store.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stream-open.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-open.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-open.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stream-prog.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-prog.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-prog.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\stream-read.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-read.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\stream-read.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\string.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\string.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\string.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\strtod.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\strtod.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\strtod.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\svg-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\svg-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\svg-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\tempfile.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\tempfile.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\tempfile.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\test-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\test-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\test-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\text.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\text.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\text.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\time.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\time.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\time.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\trace-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\trace-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\trace-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\transition.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\transition.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\transition.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\tree.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\tree.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\tree.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\ucdn.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\ucdn.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\ucdn.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\unzip.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\unzip.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\unzip.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\util.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\util.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\util.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\fitz\\xml.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\xml.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\fitz\\xml.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-annot-edit.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-annot-edit.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-annot-edit.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-annot.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-annot.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-annot.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-appearance.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-appearance.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-appearance.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-clean-file.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-clean-file.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-clean-file.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-clean.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-clean.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-clean.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-cmap-load.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-load.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-load.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-cmap-parse.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-parse.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-parse.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-cmap-table.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-table.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap-table.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-cmap.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-cmap.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-colorspace.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-colorspace.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-colorspace.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-crypt.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-crypt.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-crypt.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-device.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-device.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-device.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-encoding.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-encoding.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-encoding.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-event.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-event.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-event.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-field.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-field.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-field.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-font.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-font.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-font.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-fontfile.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-fontfile.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-fontfile.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-form.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-form.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-form.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-function.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-function.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-function.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-image.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-image.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-image.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-interpret.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-interpret.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-interpret.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-lex.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-lex.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-lex.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-metrics.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-metrics.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-metrics.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-nametree.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-nametree.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-nametree.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-object.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-object.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-object.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-op-buffer.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-buffer.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-buffer.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-op-filter.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-filter.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-filter.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-op-run.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-run.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-op-run.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-outline.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-outline.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-outline.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-page.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-page.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-page.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-parse.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-parse.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-parse.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-pattern.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-pattern.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-pattern.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-pkcs7.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-pkcs7.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-pkcs7.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-repair.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-repair.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-repair.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-run.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-run.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-run.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-shade.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-shade.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-shade.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-store.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-store.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-store.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-stream.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-stream.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-stream.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-type3.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-type3.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-type3.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-unicode.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-unicode.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-unicode.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-write.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-write.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-write.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-xobject.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-xobject.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-xobject.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\pdf-xref.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-xref.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\pdf-xref.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-common.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-common.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-common.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-doc.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-doc.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-doc.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-glyphs.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-glyphs.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-glyphs.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-gradient.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-gradient.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-gradient.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-image.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-image.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-image.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-link.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-link.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-link.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-outline.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-outline.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-outline.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-path.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-path.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-path.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-resource.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-resource.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-resource.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-tile.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-tile.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-tile.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-util.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-util.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-util.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\xps\\xps-zip.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-zip.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\xps\\xps-zip.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\cbz\\mucbz.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\mucbz.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\mucbz.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\cbz\\muimg.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\muimg.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\muimg.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\cbz\\mutiff.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\mutiff.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\cbz\\mutiff.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\gprf\\gprf-doc.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\gprf\\gprf-doc.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\gprf\\gprf-doc.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\gprf\\gprf-skeleton.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\gprf\\gprf-skeleton.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\gprf\\gprf-skeleton.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\css-apply.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\css-apply.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\css-apply.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\css-parse.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\css-parse.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\css-parse.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\epub-doc.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\epub-doc.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\epub-doc.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\html-doc.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-doc.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-doc.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\html-font.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-font.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-font.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\html\\html-layout.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-layout.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\html\\html-layout.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\js\\pdf-js.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\js\\pdf-js.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\js\\pdf-js.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DAA_BITS=8 -DARCH_ARM -DARCH_ARM_CAN_LOAD_UNALIGNED -DARCH_THUMB -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -DSUPPORT_GPROOF -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\mupdfcore.dir\\source\\pdf\\js\\pdf-jsimp-mu.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\js\\pdf-jsimp-mu.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\source\\pdf\\js\\pdf-jsimp-mu.c"}, {"directory": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/.cxx/Debug/n4j61447/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFT2_BUILD_LIBRARY -DFT_CONFIG_OPTIONS_H=\\\"slimftoptions.h\\\" -Dadnan_EXPORTS -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec -IC:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\adnan.dir\\mupdf.c.o -c C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\mupdf.c", "file": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\mupdf.c"}]