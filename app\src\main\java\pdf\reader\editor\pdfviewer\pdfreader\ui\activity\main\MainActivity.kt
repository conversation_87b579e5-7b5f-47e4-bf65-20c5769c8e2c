package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main

import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Log
import androidx.activity.viewModels
import androidx.navigation.Navigation.findNavController
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.InAppUpdateManager.Companion.inAppUpdate
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.UpdateType
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseActivity
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivityMainBinding
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AppOpenManager
import pdf.reader.editor.pdfviewer.pdfreader.manager.language.LanguageData
import pdf.reader.editor.pdfviewer.pdfreader.manager.language.LocalHelper
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import java.io.File
import java.io.FileOutputStream
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : BaseActivity<ActivityMainBinding, MainViewModel>() {

    override val viewModel: Class<MainViewModel>
        get() = MainViewModel::class.java

    override fun getViewBinding(): ActivityMainBinding = ActivityMainBinding.inflate(layoutInflater)

    private var storage: Storage? = null
    private var position: Int? = null

    @Inject
    lateinit var appOpenManager: AppOpenManager

    @Inject
    lateinit var adsIds: AdsIds
    private val adViewModel by viewModels<AdViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.Theme_Pr_NoActionBar)
        appOpenManager.setValues(
            adsIds.openAd.adUnitId,
            adViewModel.getRemoteConfigModel().adConfigModel.openAd.show
        )
        appOpenManager.fetchAd({}, {})
    }

    //    override fun attachBaseContext(newBase: Context?) {
//        storage = Storage(newBase!!)
//        position = storage?.getLocalization()
//        if (position == null || position == 0)
//            position = AppLanguageData.getAppLanguageCodeList().indexOf("en")
//
//
//        if (position != null && position == 100)
//            position = 0
//
//        val local = Locale(AppLanguageData.getAppLanguageCodeList()[position!!])
//        val localeUpdatedContext: ContextWrapper = LocalHelper.updateLocale(newBase, local)
//        super.attachBaseContext(localeUpdatedContext)
//    }
    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        checkUpdate()
    }

    private fun checkUpdate() {

        inAppUpdate { configure { updateType = UpdateType.FLEXIBLE } }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        logs("MainActivity>>onNewIntent")
        if (intent?.data != null) {
            val doc = getFileFromUri(intent.data!!)
            if (doc != null) {
                mViewModel.selectedDoc = doc
                intent.data = null
                val controller = findNavController(this, R.id.navHostFragment)
                if (controller.currentDestination?.id != R.id.homeFragment) {
                    controller.popBackStack(R.id.homeFragment, false)
                    findNavController(this, R.id.navHostFragment)
                        .navigate(R.id.activityToViewer)
                } else {
                    findNavController(this, R.id.navHostFragment)
                        .navigate(R.id.activityToViewer)
                }

            }


        }
    }

    private fun getFileFromUri(uri: Uri): DocumentsModel? {
        var documentModel: DocumentsModel? = null
        try {

            uri.scheme?.let { scheme ->
                if (scheme == "file") {
                    val path = uri.path!!
                    val file = File(path)
                    val name = file.nameWithoutExtension
                    val date = file.lastModified() / 1000 //convert to seconds
                    val size = file.length()
                    documentModel = DocumentsModel()
                    documentModel?.fileName = name
                    documentModel?.absolutePath = path
                    documentModel?.fileSize = mViewModel.getReadableSize(size)
                    documentModel?.dateInDigit = date
                    documentModel?.fileDate = mViewModel.getFormattedDate(date)
                    documentModel?.parentFile = file.parent!!
                } else {
                    val pdfExt = "_data LIKE '%.pdf'"
                    var cursor: Cursor? = null
                    val docsProjection = arrayOf(MediaStore.Files.FileColumns.DATA)

                    cursor = contentResolver.query(
                        uri,
                        docsProjection,
                        pdfExt,
                        null,
                        null
                    )


                    if (cursor != null && cursor?.columnCount!! > 0) {

                        while (cursor?.moveToNext() == true) {
                            documentModel = try {
                                val path =
                                    cursor?.getString(cursor?.getColumnIndex(MediaStore.Files.FileColumns.DATA)!!)
                                Log.d("TAG", "getFileFromUri: get file try $path")
                                if (path == null) {
                                    val path =
                                        copyFileToInternalStorage(uri, "filesWithoutExtension")
                                    getPDFFile(path)
                                } else
                                    getPDFFile(path)
                            } catch (ee: java.lang.Exception) {
                                val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                                Log.d("TAG", "getFileFromUri: get file catch")
                                getPDFFile(path)
                            }
                        }
                        cursor?.close()
                    } else {
                        val path = copyFileToInternalStorage(uri, "filesWithoutExtension")
                        documentModel = getPDFFile(path)
                    }
                }
            }
            return documentModel
        } catch (ee: java.lang.Exception) {
            return null
        }
    }

    private fun copyFileToInternalStorage(
        uri: Uri,
        newDirName: String,
    ): String {

        val returnCursor = contentResolver?.query(
            uri,
            arrayOf(OpenableColumns.DISPLAY_NAME, OpenableColumns.SIZE),
            null,
            null,
            null
        )
        /*
         * Get the column indexes of the data in the Cursor,
         *     * move to the first row in the Cursor, get the data,
         *     * and display it.
         * */
        if (returnCursor?.columnCount ?: 0 > 0) {
            val column_index = returnCursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME) ?: -1
            if (returnCursor?.moveToFirst() == true && column_index >= 0) {
                val name = returnCursor.getString(column_index)
                val output: File = if (newDirName != "") {
                    val dir = File("${filesDir}/$newDirName")
                    if (!dir.exists()) {
                        dir.mkdir()
                    }
                    File("${filesDir}/$newDirName/$name")
                } else {
                    File("${filesDir}/$name")
                }
                try {
                    val inputStream = contentResolver?.openInputStream(uri)
                    val outputStream = FileOutputStream(output)
                    var read: Int
                    val bufferSize = 1024
                    val buffers = ByteArray(bufferSize)
                    while (inputStream!!.read(buffers).also { read = it } != -1) {
                        outputStream.write(buffers, 0, read)
                    }
                    inputStream.close()
                    outputStream.close()
                } catch (e: Exception) {
                    Log.e("error", e.message!!)
                }
                return output.path
            }
        }
        return ""
    }

    private fun getPDFFile(path: String?): DocumentsModel? {
        return if (path != null) {
            val name: String?
            val date: Long?
            val size: Long?
            val file = File(path)
            name = file.nameWithoutExtension
            date = file.lastModified() / 1000 //convert to seconds
            size = file.length()
            DocumentsModel(
                name,
                mViewModel
                    .getReadableSize(size),
                fileDate = mViewModel.getReadableSize(date),
                parentFile = file.parent!!,
                absolutePath = path,
                dateInDigit = date,
                sizeInDigit = size
            )
        } else null
    }

    override fun attachBaseContext(newBase: Context?) {
        storage = newBase?.let { Storage(it) }
        var position = storage?.preferences?.getInt("localization", 0) ?: 0
        if (position == 0)
            position = LanguageData.getAppLanguageCodeList().indexOf("en")


        if (position == 100)
            position = 0

        val local = Locale(LanguageData.getAppLanguageCodeList()[position])
        val localeUpdatedContext: ContextWrapper = LocalHelper.updateLocale(newBase!!, local)
        super.attachBaseContext(localeUpdatedContext)
    }

    override fun onDestroy() {
        super.onDestroy()
        adViewModel.destroyBannerAd(adsIds.homeBanner.adKey)
        adViewModel.destroyNativeAd(adsIds.homeNative.adKey)
        adViewModel.destroyNativeAd(adsIds.exitNative.adKey)
        adViewModel.destroyNativeAd(adsIds.homeSearchNative.adKey)
    }
}