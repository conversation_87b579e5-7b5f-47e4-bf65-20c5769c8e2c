package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.sort

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.itextpdf.text.Document
import com.itextpdf.text.pdf.PdfCopy
import com.itextpdf.text.pdf.PdfReader
import com.shockwave.pdfium.PdfPasswordException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentSortPDFBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getFormattedDate
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getReadableSize
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showCreateSheet
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.GridSpacingItemDecoration
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.tool.ToolsActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogClass
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.sort.adapter.SortAdapter
import java.io.File
import java.io.FileOutputStream
import java.io.IOException


@Suppress("DEPRECATION")
class SortPDFFragment : BaseFragment<FragmentSortPDFBinding, SortViewModel>() {
    override val viewModel: Class<SortViewModel>
        get() = SortViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentSortPDFBinding = FragmentSortPDFBinding
        .inflate(inflater, container, false)

    private var documentModel: DocumentsModel? = null

    //    private val args: SortPDFFragmentArgs by navArgs()
    private var splitAdapter: SortAdapter? = null
    private var selectedPages: ArrayList<PhotoModel> = ArrayList()
    private var password: String = ""
    private var fileName = ""

    private val itemTouchHelper by lazy {
        val simpleItemTouchCallback =
            object : ItemTouchHelper.Callback() {

                override fun isLongPressDragEnabled(): Boolean {
                    return true
                }

                override fun onMove(
                    recyclerView: RecyclerView,
                    viewHolder: RecyclerView.ViewHolder,
                    target: RecyclerView.ViewHolder
                ): Boolean {

                    val from = viewHolder.absoluteAdapterPosition
                    val to = target.absoluteAdapterPosition
                    splitAdapter?.swap(from, to)
                    splitAdapter?.notifyItemMoved(from, to)

                    val fromItem = selectedPages[from]
                    selectedPages.remove(fromItem)
                    selectedPages.add(to, fromItem)

                    return true
                }

                override fun onSwiped(
                    viewHolder: RecyclerView.ViewHolder,
                    direction: Int
                ) {
                }

                override fun getMovementFlags(
                    recyclerView: RecyclerView,
                    viewHolder: RecyclerView.ViewHolder
                ): Int {
                    return makeFlag(
                        ItemTouchHelper.ACTION_STATE_DRAG,
                        ItemTouchHelper.DOWN or ItemTouchHelper.UP or ItemTouchHelper.START or ItemTouchHelper.END
                    )
                }
            }
        ItemTouchHelper(simpleItemTouchCallback)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        documentModel = args.pdfFile
        documentModel = sharedViewModel.selectedDoc
        password = documentModel?.password!!
        initViews()

        mViewDataBinding.btnSortFiles.setOnClickListener {

            DialogNewPdfName.getInstance(fileName)
            { it2, fileName ->
                when (it2) {
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                        this.fileName = fileName
                        sortPDF()
                    }

                    ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                        Toast.makeText(
                            activity,
                            getString(R.string.txt_pdf_special_charac),
                            Toast.LENGTH_LONG
                        ).show()
                    }

                    ViewPdfActions.INVALID_EXTENSION -> {
                        Toast.makeText(
                            activity,
                            getString(R.string.txt_pdf_invalid_extension),
                            Toast.LENGTH_LONG
                        ).show()
                    }

                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                        showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                    }

                    else -> {}
                }
            }.show(childFragmentManager, "newFileDialog")


        }

        mViewDataBinding.imgBack.setOnClickListener {
            if (activity != null && activity is ToolsActivity)
                activity?.finish()
            else
                isAlive {
                    findNavController().popBackStack()
                }
        }
    }

    private fun initViews() {

        isAlive {
            splitAdapter = SortAdapter()
            mViewDataBinding.sortRecyclerView.apply {

                layoutManager = GridLayoutManager(it, 2)
                adapter = splitAdapter
                addItemDecoration(
                    GridSpacingItemDecoration(
                        2,
                        50,
                        true
                    )
                )

                val touchHelper = itemTouchHelper
                touchHelper.attachToRecyclerView(this)
            }
            loadingDialog = DialogClass.loadingDialog(it)

            loadPages()
        }

    }

    private fun loadPages() {
        isAlive {
            val file = File(documentModel?.absolutePath!!)
            if (file.exists() && file.length() > 0) {
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        FileUtils.getPDFPagesThumbnail(
                            documentModel?.absolutePath!!,
                            it,
                            password
                        )
                            .collect {
                                withContext(Dispatchers.Main) {
                                    selectedPages.add(it.second)
                                    splitAdapter?.setData(it.second, it.first)
                                }
                            }
                    } catch (e: PdfPasswordException) {
                        withContext(Dispatchers.Main) {
                            DialogPdfPassword.getInstance { action, password ->
                                when (action) {
                                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                                        <EMAIL> = password
                                        loadPages()

                                    }

                                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                                        activity?.showToast(getString(R.string.txt_pdf_null_password_warning))
                                    }

                                    ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
                                        isAlive {
                                            findNavController().popBackStack()
                                        }


                                    }

                                    else -> {}
                                }
                            }.show(childFragmentManager, "Password Dialog")
                        }
                    } catch (e: IOException) {
                        withContext(Dispatchers.Main) {

                            activity?.showToast(getString(R.string.file_is_corrupted_or_not_an_pdf))
                        }
                    }
                }
            } else
                activity?.showToast(getString(R.string.file_not_exist_or_empty))
        }

    }

    private fun sortPDF() {
        loadingDialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {

                val reader = PdfReader(documentModel?.absolutePath!!, password.toByteArray())
                PdfReader.unethicalreading = true
                val document = Document(reader.getPageSizeWithRotation(1))
                val sortFile = FileUtils.saveFileToExternalStorage(fileName)
                val writer = PdfCopy(document, FileOutputStream(sortFile))
                document.open()
                selectedPages.forEachIndexed { _, photoModel ->
                    val page = writer.getImportedPage(reader, photoModel.position + 1)
                    writer.addPage(page)
                }
                document.close()
                writer.close()
                activity?.sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            sortFile
                        )
                    )
                )
                withContext(Dispatchers.Main) {
                    isAlive {
                        sharedViewModel.loadFiles(requireActivity())
                        loadingDialog.hide()
                        it.showCreateSheet(sortFile?.absolutePath!!) { flag ->

                            if (flag) {
                                isAlive { con ->
                                    val doc = DocumentsModel()
                                    doc.absolutePath = sortFile.absolutePath
                                    doc.fileName = sortFile.name
                                    doc.parentFile = sortFile.parent?:""
                                    doc.sizeInDigit = sortFile.length()
                                    doc.dateInDigit = sortFile.lastModified()
                                    doc.fileDate = con.getFormattedDate(sortFile.lastModified())
                                    doc.fileSize = con.getReadableSize(sortFile.length())

                                    sharedViewModel.selectedDoc = doc
                                    isAlive { activity ->
                                        val intent = Intent(activity, ViewerActivity::class.java)
                                        intent.flags =
                                            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                                        intent.putExtra("pdf_file", doc)
                                        activity.startActivity(intent)
                                        if (activity is ToolsActivity)
                                            activity.finish()
                                        else
                                            findNavController().popBackStack()

                                    }
                                    /*    if (isFragmentInBackStack(R.id.action_homeFragment_to_pdfViewFragment)) {
                                            findNavController().popBackStack()
                                        } else {
                                            val action = SortPDFFragmentDirections
                                                .actionSortPDFFragmentToPdfViewFragment()
                                            findNavController().navigate(action)
                                        }*/
                                }
                            } else {
                                isAlive {
                                    findNavController().popBackStack()
                                }
                            }


                        }
                    }

                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
//                    sharedViewModel.loadFiles(requireActivity())
                    loadingDialog.hide()
//                    findNavController().popBackStack()
                    showToast(getString(R.string.failed_to_sort_pdf))
                    activity?.sendFirebaseLog("Sort_Screen", "Failed to sort")
                }
            }

        }
    }


}