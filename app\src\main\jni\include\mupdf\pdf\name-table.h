enum
{
	PDF_OBJ_ENUM__DUMMY,
#define PDF_NAME_3D  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_3D)
	PDF_OBJ_ENUM_NAME_3D,
#define PDF_NAME_A  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_A)
	PDF_OBJ_ENUM_NAME_A,
#define PDF_NAME_A85  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_A85)
	PDF_OBJ_ENUM_NAME_A85,
#define PDF_NAME_AA  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AA)
	PDF_OBJ_ENUM_NAME_AA,
#define PDF_NAME_AESV2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AESV2)
	PDF_OBJ_ENUM_NAME_AESV2,
#define PDF_NAME_AESV3  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AESV3)
	PDF_OBJ_ENUM_NAME_AESV3,
#define PDF_NAME_AHx  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AHx)
	PDF_OBJ_ENUM_NAME_AHx,
#define PDF_NAME_AP  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AP)
	PDF_OBJ_ENUM_NAME_AP,
#define PDF_NAME_AS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AS)
	PDF_OBJ_ENUM_NAME_AS,
#define PDF_NAME_ASCII85Decode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ASCII85Decode)
	PDF_OBJ_ENUM_NAME_ASCII85Decode,
#define PDF_NAME_ASCIIHexDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ASCIIHexDecode)
	PDF_OBJ_ENUM_NAME_ASCIIHexDecode,
#define PDF_NAME_AcroForm  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AcroForm)
	PDF_OBJ_ENUM_NAME_AcroForm,
#define PDF_NAME_Adobe_PPKLite  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Adobe_PPKLite)
	PDF_OBJ_ENUM_NAME_Adobe_PPKLite,
#define PDF_NAME_AllOff  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AllOff)
	PDF_OBJ_ENUM_NAME_AllOff,
#define PDF_NAME_AllOn  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AllOn)
	PDF_OBJ_ENUM_NAME_AllOn,
#define PDF_NAME_Alpha  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Alpha)
	PDF_OBJ_ENUM_NAME_Alpha,
#define PDF_NAME_Alternate  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Alternate)
	PDF_OBJ_ENUM_NAME_Alternate,
#define PDF_NAME_Annot  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Annot)
	PDF_OBJ_ENUM_NAME_Annot,
#define PDF_NAME_Annots  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Annots)
	PDF_OBJ_ENUM_NAME_Annots,
#define PDF_NAME_AnyOff  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_AnyOff)
	PDF_OBJ_ENUM_NAME_AnyOff,
#define PDF_NAME_ArtBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ArtBox)
	PDF_OBJ_ENUM_NAME_ArtBox,
#define PDF_NAME_Ascent  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ascent)
	PDF_OBJ_ENUM_NAME_Ascent,
#define PDF_NAME_B  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_B)
	PDF_OBJ_ENUM_NAME_B,
#define PDF_NAME_BBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BBox)
	PDF_OBJ_ENUM_NAME_BBox,
#define PDF_NAME_BC  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BC)
	PDF_OBJ_ENUM_NAME_BC,
#define PDF_NAME_BG  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BG)
	PDF_OBJ_ENUM_NAME_BG,
#define PDF_NAME_BM  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BM)
	PDF_OBJ_ENUM_NAME_BM,
#define PDF_NAME_BPC  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BPC)
	PDF_OBJ_ENUM_NAME_BPC,
#define PDF_NAME_BS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BS)
	PDF_OBJ_ENUM_NAME_BS,
#define PDF_NAME_Background  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Background)
	PDF_OBJ_ENUM_NAME_Background,
#define PDF_NAME_BaseEncoding  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BaseEncoding)
	PDF_OBJ_ENUM_NAME_BaseEncoding,
#define PDF_NAME_BaseFont  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BaseFont)
	PDF_OBJ_ENUM_NAME_BaseFont,
#define PDF_NAME_BaseState  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BaseState)
	PDF_OBJ_ENUM_NAME_BaseState,
#define PDF_NAME_BitsPerComponent  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BitsPerComponent)
	PDF_OBJ_ENUM_NAME_BitsPerComponent,
#define PDF_NAME_BitsPerCoordinate  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BitsPerCoordinate)
	PDF_OBJ_ENUM_NAME_BitsPerCoordinate,
#define PDF_NAME_BitsPerFlag  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BitsPerFlag)
	PDF_OBJ_ENUM_NAME_BitsPerFlag,
#define PDF_NAME_BitsPerSample  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BitsPerSample)
	PDF_OBJ_ENUM_NAME_BitsPerSample,
#define PDF_NAME_BlackIs1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BlackIs1)
	PDF_OBJ_ENUM_NAME_BlackIs1,
#define PDF_NAME_BleedBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_BleedBox)
	PDF_OBJ_ENUM_NAME_BleedBox,
#define PDF_NAME_Blinds  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Blinds)
	PDF_OBJ_ENUM_NAME_Blinds,
#define PDF_NAME_Bounds  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Bounds)
	PDF_OBJ_ENUM_NAME_Bounds,
#define PDF_NAME_Box  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Box)
	PDF_OBJ_ENUM_NAME_Box,
#define PDF_NAME_Bt  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Bt)
	PDF_OBJ_ENUM_NAME_Bt,
#define PDF_NAME_Btn  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Btn)
	PDF_OBJ_ENUM_NAME_Btn,
#define PDF_NAME_ByteRange  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ByteRange)
	PDF_OBJ_ENUM_NAME_ByteRange,
#define PDF_NAME_C  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_C)
	PDF_OBJ_ENUM_NAME_C,
#define PDF_NAME_C0  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_C0)
	PDF_OBJ_ENUM_NAME_C0,
#define PDF_NAME_C1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_C1)
	PDF_OBJ_ENUM_NAME_C1,
#define PDF_NAME_CA  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CA)
	PDF_OBJ_ENUM_NAME_CA,
#define PDF_NAME_CCF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CCF)
	PDF_OBJ_ENUM_NAME_CCF,
#define PDF_NAME_CCITTFaxDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CCITTFaxDecode)
	PDF_OBJ_ENUM_NAME_CCITTFaxDecode,
#define PDF_NAME_CF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CF)
	PDF_OBJ_ENUM_NAME_CF,
#define PDF_NAME_CFM  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CFM)
	PDF_OBJ_ENUM_NAME_CFM,
#define PDF_NAME_CIDFontType0  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CIDFontType0)
	PDF_OBJ_ENUM_NAME_CIDFontType0,
#define PDF_NAME_CIDFontType0C  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CIDFontType0C)
	PDF_OBJ_ENUM_NAME_CIDFontType0C,
#define PDF_NAME_CIDFontType2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CIDFontType2)
	PDF_OBJ_ENUM_NAME_CIDFontType2,
#define PDF_NAME_CIDSystemInfo  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CIDSystemInfo)
	PDF_OBJ_ENUM_NAME_CIDSystemInfo,
#define PDF_NAME_CIDToGIDMap  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CIDToGIDMap)
	PDF_OBJ_ENUM_NAME_CIDToGIDMap,
#define PDF_NAME_CMYK  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CMYK)
	PDF_OBJ_ENUM_NAME_CMYK,
#define PDF_NAME_CS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CS)
	PDF_OBJ_ENUM_NAME_CS,
#define PDF_NAME_CalCMYK  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CalCMYK)
	PDF_OBJ_ENUM_NAME_CalCMYK,
#define PDF_NAME_CalGray  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CalGray)
	PDF_OBJ_ENUM_NAME_CalGray,
#define PDF_NAME_CalRGB  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CalRGB)
	PDF_OBJ_ENUM_NAME_CalRGB,
#define PDF_NAME_CapHeight  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CapHeight)
	PDF_OBJ_ENUM_NAME_CapHeight,
#define PDF_NAME_Caret  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Caret)
	PDF_OBJ_ENUM_NAME_Caret,
#define PDF_NAME_Catalog  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Catalog)
	PDF_OBJ_ENUM_NAME_Catalog,
#define PDF_NAME_Ch  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ch)
	PDF_OBJ_ENUM_NAME_Ch,
#define PDF_NAME_CharProcs  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CharProcs)
	PDF_OBJ_ENUM_NAME_CharProcs,
#define PDF_NAME_Circle  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Circle)
	PDF_OBJ_ENUM_NAME_Circle,
#define PDF_NAME_ColorSpace  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ColorSpace)
	PDF_OBJ_ENUM_NAME_ColorSpace,
#define PDF_NAME_ColorTransform  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ColorTransform)
	PDF_OBJ_ENUM_NAME_ColorTransform,
#define PDF_NAME_Colors  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Colors)
	PDF_OBJ_ENUM_NAME_Colors,
#define PDF_NAME_Columns  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Columns)
	PDF_OBJ_ENUM_NAME_Columns,
#define PDF_NAME_Configs  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Configs)
	PDF_OBJ_ENUM_NAME_Configs,
#define PDF_NAME_Contents  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Contents)
	PDF_OBJ_ENUM_NAME_Contents,
#define PDF_NAME_Coords  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Coords)
	PDF_OBJ_ENUM_NAME_Coords,
#define PDF_NAME_Count  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Count)
	PDF_OBJ_ENUM_NAME_Count,
#define PDF_NAME_Cover  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Cover)
	PDF_OBJ_ENUM_NAME_Cover,
#define PDF_NAME_Creator  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Creator)
	PDF_OBJ_ENUM_NAME_Creator,
#define PDF_NAME_CropBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_CropBox)
	PDF_OBJ_ENUM_NAME_CropBox,
#define PDF_NAME_Crypt  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Crypt)
	PDF_OBJ_ENUM_NAME_Crypt,
#define PDF_NAME_D  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_D)
	PDF_OBJ_ENUM_NAME_D,
#define PDF_NAME_DA  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DA)
	PDF_OBJ_ENUM_NAME_DA,
#define PDF_NAME_DC  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DC)
	PDF_OBJ_ENUM_NAME_DC,
#define PDF_NAME_DCT  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DCT)
	PDF_OBJ_ENUM_NAME_DCT,
#define PDF_NAME_DCTDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DCTDecode)
	PDF_OBJ_ENUM_NAME_DCTDecode,
#define PDF_NAME_DOS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DOS)
	PDF_OBJ_ENUM_NAME_DOS,
#define PDF_NAME_DP  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DP)
	PDF_OBJ_ENUM_NAME_DP,
#define PDF_NAME_DR  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DR)
	PDF_OBJ_ENUM_NAME_DR,
#define PDF_NAME_DV  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DV)
	PDF_OBJ_ENUM_NAME_DV,
#define PDF_NAME_DW  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DW)
	PDF_OBJ_ENUM_NAME_DW,
#define PDF_NAME_DW2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DW2)
	PDF_OBJ_ENUM_NAME_DW2,
#define PDF_NAME_DamagedRowsBeforeError  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DamagedRowsBeforeError)
	PDF_OBJ_ENUM_NAME_DamagedRowsBeforeError,
#define PDF_NAME_Decode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Decode)
	PDF_OBJ_ENUM_NAME_Decode,
#define PDF_NAME_DecodeParms  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DecodeParms)
	PDF_OBJ_ENUM_NAME_DecodeParms,
#define PDF_NAME_Default  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Default)
	PDF_OBJ_ENUM_NAME_Default,
#define PDF_NAME_DescendantFonts  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DescendantFonts)
	PDF_OBJ_ENUM_NAME_DescendantFonts,
#define PDF_NAME_Descent  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Descent)
	PDF_OBJ_ENUM_NAME_Descent,
#define PDF_NAME_Dest  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Dest)
	PDF_OBJ_ENUM_NAME_Dest,
#define PDF_NAME_Dests  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Dests)
	PDF_OBJ_ENUM_NAME_Dests,
#define PDF_NAME_DeviceCMYK  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DeviceCMYK)
	PDF_OBJ_ENUM_NAME_DeviceCMYK,
#define PDF_NAME_DeviceGray  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DeviceGray)
	PDF_OBJ_ENUM_NAME_DeviceGray,
#define PDF_NAME_DeviceN  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DeviceN)
	PDF_OBJ_ENUM_NAME_DeviceN,
#define PDF_NAME_DeviceRGB  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_DeviceRGB)
	PDF_OBJ_ENUM_NAME_DeviceRGB,
#define PDF_NAME_Di  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Di)
	PDF_OBJ_ENUM_NAME_Di,
#define PDF_NAME_Differences  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Differences)
	PDF_OBJ_ENUM_NAME_Differences,
#define PDF_NAME_Dissolve  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Dissolve)
	PDF_OBJ_ENUM_NAME_Dissolve,
#define PDF_NAME_Dm  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Dm)
	PDF_OBJ_ENUM_NAME_Dm,
#define PDF_NAME_Domain  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Domain)
	PDF_OBJ_ENUM_NAME_Domain,
#define PDF_NAME_Dur  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Dur)
	PDF_OBJ_ENUM_NAME_Dur,
#define PDF_NAME_E  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_E)
	PDF_OBJ_ENUM_NAME_E,
#define PDF_NAME_EarlyChange  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_EarlyChange)
	PDF_OBJ_ENUM_NAME_EarlyChange,
#define PDF_NAME_Encode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Encode)
	PDF_OBJ_ENUM_NAME_Encode,
#define PDF_NAME_EncodedByteAlign  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_EncodedByteAlign)
	PDF_OBJ_ENUM_NAME_EncodedByteAlign,
#define PDF_NAME_Encoding  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Encoding)
	PDF_OBJ_ENUM_NAME_Encoding,
#define PDF_NAME_Encrypt  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Encrypt)
	PDF_OBJ_ENUM_NAME_Encrypt,
#define PDF_NAME_EncryptMetadata  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_EncryptMetadata)
	PDF_OBJ_ENUM_NAME_EncryptMetadata,
#define PDF_NAME_EndOfBlock  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_EndOfBlock)
	PDF_OBJ_ENUM_NAME_EndOfBlock,
#define PDF_NAME_EndOfLine  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_EndOfLine)
	PDF_OBJ_ENUM_NAME_EndOfLine,
#define PDF_NAME_Exclude  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Exclude)
	PDF_OBJ_ENUM_NAME_Exclude,
#define PDF_NAME_ExtGState  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ExtGState)
	PDF_OBJ_ENUM_NAME_ExtGState,
#define PDF_NAME_Extend  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Extend)
	PDF_OBJ_ENUM_NAME_Extend,
#define PDF_NAME_F  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_F)
	PDF_OBJ_ENUM_NAME_F,
#define PDF_NAME_FL  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FL)
	PDF_OBJ_ENUM_NAME_FL,
#define PDF_NAME_FRM  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FRM)
	PDF_OBJ_ENUM_NAME_FRM,
#define PDF_NAME_FS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FS)
	PDF_OBJ_ENUM_NAME_FS,
#define PDF_NAME_FT  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FT)
	PDF_OBJ_ENUM_NAME_FT,
#define PDF_NAME_Fade  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Fade)
	PDF_OBJ_ENUM_NAME_Fade,
#define PDF_NAME_Ff  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ff)
	PDF_OBJ_ENUM_NAME_Ff,
#define PDF_NAME_Fields  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Fields)
	PDF_OBJ_ENUM_NAME_Fields,
#define PDF_NAME_FileAttachment  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FileAttachment)
	PDF_OBJ_ENUM_NAME_FileAttachment,
#define PDF_NAME_Filter  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Filter)
	PDF_OBJ_ENUM_NAME_Filter,
#define PDF_NAME_First  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_First)
	PDF_OBJ_ENUM_NAME_First,
#define PDF_NAME_FirstChar  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FirstChar)
	PDF_OBJ_ENUM_NAME_FirstChar,
#define PDF_NAME_Fit  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Fit)
	PDF_OBJ_ENUM_NAME_Fit,
#define PDF_NAME_FitB  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitB)
	PDF_OBJ_ENUM_NAME_FitB,
#define PDF_NAME_FitBH  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitBH)
	PDF_OBJ_ENUM_NAME_FitBH,
#define PDF_NAME_FitBV  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitBV)
	PDF_OBJ_ENUM_NAME_FitBV,
#define PDF_NAME_FitH  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitH)
	PDF_OBJ_ENUM_NAME_FitH,
#define PDF_NAME_FitR  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitR)
	PDF_OBJ_ENUM_NAME_FitR,
#define PDF_NAME_FitV  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FitV)
	PDF_OBJ_ENUM_NAME_FitV,
#define PDF_NAME_Fl  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Fl)
	PDF_OBJ_ENUM_NAME_Fl,
#define PDF_NAME_Flags  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Flags)
	PDF_OBJ_ENUM_NAME_Flags,
#define PDF_NAME_FlateDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FlateDecode)
	PDF_OBJ_ENUM_NAME_FlateDecode,
#define PDF_NAME_Fly  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Fly)
	PDF_OBJ_ENUM_NAME_Fly,
#define PDF_NAME_Font  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Font)
	PDF_OBJ_ENUM_NAME_Font,
#define PDF_NAME_FontBBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontBBox)
	PDF_OBJ_ENUM_NAME_FontBBox,
#define PDF_NAME_FontDescriptor  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontDescriptor)
	PDF_OBJ_ENUM_NAME_FontDescriptor,
#define PDF_NAME_FontFile  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontFile)
	PDF_OBJ_ENUM_NAME_FontFile,
#define PDF_NAME_FontFile2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontFile2)
	PDF_OBJ_ENUM_NAME_FontFile2,
#define PDF_NAME_FontFile3  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontFile3)
	PDF_OBJ_ENUM_NAME_FontFile3,
#define PDF_NAME_FontMatrix  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontMatrix)
	PDF_OBJ_ENUM_NAME_FontMatrix,
#define PDF_NAME_FontName  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FontName)
	PDF_OBJ_ENUM_NAME_FontName,
#define PDF_NAME_Form  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Form)
	PDF_OBJ_ENUM_NAME_Form,
#define PDF_NAME_FormType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FormType)
	PDF_OBJ_ENUM_NAME_FormType,
#define PDF_NAME_FreeText  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FreeText)
	PDF_OBJ_ENUM_NAME_FreeText,
#define PDF_NAME_Function  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Function)
	PDF_OBJ_ENUM_NAME_Function,
#define PDF_NAME_FunctionType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_FunctionType)
	PDF_OBJ_ENUM_NAME_FunctionType,
#define PDF_NAME_Functions  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Functions)
	PDF_OBJ_ENUM_NAME_Functions,
#define PDF_NAME_G  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_G)
	PDF_OBJ_ENUM_NAME_G,
#define PDF_NAME_Glitter  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Glitter)
	PDF_OBJ_ENUM_NAME_Glitter,
#define PDF_NAME_GoTo  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_GoTo)
	PDF_OBJ_ENUM_NAME_GoTo,
#define PDF_NAME_GoToR  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_GoToR)
	PDF_OBJ_ENUM_NAME_GoToR,
#define PDF_NAME_Group  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Group)
	PDF_OBJ_ENUM_NAME_Group,
#define PDF_NAME_H  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_H)
	PDF_OBJ_ENUM_NAME_H,
#define PDF_NAME_Height  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Height)
	PDF_OBJ_ENUM_NAME_Height,
#define PDF_NAME_Highlight  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Highlight)
	PDF_OBJ_ENUM_NAME_Highlight,
#define PDF_NAME_I  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_I)
	PDF_OBJ_ENUM_NAME_I,
#define PDF_NAME_ICCBased  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ICCBased)
	PDF_OBJ_ENUM_NAME_ICCBased,
#define PDF_NAME_ID  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ID)
	PDF_OBJ_ENUM_NAME_ID,
#define PDF_NAME_IM  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_IM)
	PDF_OBJ_ENUM_NAME_IM,
#define PDF_NAME_Identity  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Identity)
	PDF_OBJ_ENUM_NAME_Identity,
#define PDF_NAME_Identity_H  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Identity_H)
	PDF_OBJ_ENUM_NAME_Identity_H,
#define PDF_NAME_Identity_V  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Identity_V)
	PDF_OBJ_ENUM_NAME_Identity_V,
#define PDF_NAME_Image  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Image)
	PDF_OBJ_ENUM_NAME_Image,
#define PDF_NAME_ImageMask  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ImageMask)
	PDF_OBJ_ENUM_NAME_ImageMask,
#define PDF_NAME_Index  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Index)
	PDF_OBJ_ENUM_NAME_Index,
#define PDF_NAME_Indexed  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Indexed)
	PDF_OBJ_ENUM_NAME_Indexed,
#define PDF_NAME_Info  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Info)
	PDF_OBJ_ENUM_NAME_Info,
#define PDF_NAME_Ink  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ink)
	PDF_OBJ_ENUM_NAME_Ink,
#define PDF_NAME_InkList  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_InkList)
	PDF_OBJ_ENUM_NAME_InkList,
#define PDF_NAME_Intent  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Intent)
	PDF_OBJ_ENUM_NAME_Intent,
#define PDF_NAME_Interpolate  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Interpolate)
	PDF_OBJ_ENUM_NAME_Interpolate,
#define PDF_NAME_IsMap  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_IsMap)
	PDF_OBJ_ENUM_NAME_IsMap,
#define PDF_NAME_ItalicAngle  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ItalicAngle)
	PDF_OBJ_ENUM_NAME_ItalicAngle,
#define PDF_NAME_JBIG2Decode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_JBIG2Decode)
	PDF_OBJ_ENUM_NAME_JBIG2Decode,
#define PDF_NAME_JBIG2Globals  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_JBIG2Globals)
	PDF_OBJ_ENUM_NAME_JBIG2Globals,
#define PDF_NAME_JPXDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_JPXDecode)
	PDF_OBJ_ENUM_NAME_JPXDecode,
#define PDF_NAME_JS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_JS)
	PDF_OBJ_ENUM_NAME_JS,
#define PDF_NAME_JavaScript  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_JavaScript)
	PDF_OBJ_ENUM_NAME_JavaScript,
#define PDF_NAME_K  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_K)
	PDF_OBJ_ENUM_NAME_K,
#define PDF_NAME_Kids  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Kids)
	PDF_OBJ_ENUM_NAME_Kids,
#define PDF_NAME_L  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_L)
	PDF_OBJ_ENUM_NAME_L,
#define PDF_NAME_LC  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LC)
	PDF_OBJ_ENUM_NAME_LC,
#define PDF_NAME_LJ  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LJ)
	PDF_OBJ_ENUM_NAME_LJ,
#define PDF_NAME_LW  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LW)
	PDF_OBJ_ENUM_NAME_LW,
#define PDF_NAME_LZ  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LZ)
	PDF_OBJ_ENUM_NAME_LZ,
#define PDF_NAME_LZW  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LZW)
	PDF_OBJ_ENUM_NAME_LZW,
#define PDF_NAME_LZWDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LZWDecode)
	PDF_OBJ_ENUM_NAME_LZWDecode,
#define PDF_NAME_Lab  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Lab)
	PDF_OBJ_ENUM_NAME_Lab,
#define PDF_NAME_LastChar  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_LastChar)
	PDF_OBJ_ENUM_NAME_LastChar,
#define PDF_NAME_Launch  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Launch)
	PDF_OBJ_ENUM_NAME_Launch,
#define PDF_NAME_Length  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Length)
	PDF_OBJ_ENUM_NAME_Length,
#define PDF_NAME_Length1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Length1)
	PDF_OBJ_ENUM_NAME_Length1,
#define PDF_NAME_Length2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Length2)
	PDF_OBJ_ENUM_NAME_Length2,
#define PDF_NAME_Length3  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Length3)
	PDF_OBJ_ENUM_NAME_Length3,
#define PDF_NAME_Limits  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Limits)
	PDF_OBJ_ENUM_NAME_Limits,
#define PDF_NAME_Line  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Line)
	PDF_OBJ_ENUM_NAME_Line,
#define PDF_NAME_Linearized  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Linearized)
	PDF_OBJ_ENUM_NAME_Linearized,
#define PDF_NAME_Link  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Link)
	PDF_OBJ_ENUM_NAME_Link,
#define PDF_NAME_Luminosity  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Luminosity)
	PDF_OBJ_ENUM_NAME_Luminosity,
#define PDF_NAME_M  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_M)
	PDF_OBJ_ENUM_NAME_M,
#define PDF_NAME_MK  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_MK)
	PDF_OBJ_ENUM_NAME_MK,
#define PDF_NAME_ML  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ML)
	PDF_OBJ_ENUM_NAME_ML,
#define PDF_NAME_MMType1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_MMType1)
	PDF_OBJ_ENUM_NAME_MMType1,
#define PDF_NAME_Mac  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Mac)
	PDF_OBJ_ENUM_NAME_Mac,
#define PDF_NAME_Mask  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Mask)
	PDF_OBJ_ENUM_NAME_Mask,
#define PDF_NAME_Matrix  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Matrix)
	PDF_OBJ_ENUM_NAME_Matrix,
#define PDF_NAME_Matte  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Matte)
	PDF_OBJ_ENUM_NAME_Matte,
#define PDF_NAME_MaxLen  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_MaxLen)
	PDF_OBJ_ENUM_NAME_MaxLen,
#define PDF_NAME_MediaBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_MediaBox)
	PDF_OBJ_ENUM_NAME_MediaBox,
#define PDF_NAME_MissingWidth  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_MissingWidth)
	PDF_OBJ_ENUM_NAME_MissingWidth,
#define PDF_NAME_Movie  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Movie)
	PDF_OBJ_ENUM_NAME_Movie,
#define PDF_NAME_N  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_N)
	PDF_OBJ_ENUM_NAME_N,
#define PDF_NAME_Name  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Name)
	PDF_OBJ_ENUM_NAME_Name,
#define PDF_NAME_Named  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Named)
	PDF_OBJ_ENUM_NAME_Named,
#define PDF_NAME_Names  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Names)
	PDF_OBJ_ENUM_NAME_Names,
#define PDF_NAME_NewWindow  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_NewWindow)
	PDF_OBJ_ENUM_NAME_NewWindow,
#define PDF_NAME_Next  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Next)
	PDF_OBJ_ENUM_NAME_Next,
#define PDF_NAME_None  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_None)
	PDF_OBJ_ENUM_NAME_None,
#define PDF_NAME_Normal  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Normal)
	PDF_OBJ_ENUM_NAME_Normal,
#define PDF_NAME_O  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_O)
	PDF_OBJ_ENUM_NAME_O,
#define PDF_NAME_OC  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OC)
	PDF_OBJ_ENUM_NAME_OC,
#define PDF_NAME_OCG  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OCG)
	PDF_OBJ_ENUM_NAME_OCG,
#define PDF_NAME_OCGs  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OCGs)
	PDF_OBJ_ENUM_NAME_OCGs,
#define PDF_NAME_OCMD  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OCMD)
	PDF_OBJ_ENUM_NAME_OCMD,
#define PDF_NAME_OCProperties  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OCProperties)
	PDF_OBJ_ENUM_NAME_OCProperties,
#define PDF_NAME_OE  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OE)
	PDF_OBJ_ENUM_NAME_OE,
#define PDF_NAME_OFF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OFF)
	PDF_OBJ_ENUM_NAME_OFF,
#define PDF_NAME_ON  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ON)
	PDF_OBJ_ENUM_NAME_ON,
#define PDF_NAME_ObjStm  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ObjStm)
	PDF_OBJ_ENUM_NAME_ObjStm,
#define PDF_NAME_Of  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Of)
	PDF_OBJ_ENUM_NAME_Of,
#define PDF_NAME_Off  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Off)
	PDF_OBJ_ENUM_NAME_Off,
#define PDF_NAME_OpenType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_OpenType)
	PDF_OBJ_ENUM_NAME_OpenType,
#define PDF_NAME_Opt  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Opt)
	PDF_OBJ_ENUM_NAME_Opt,
#define PDF_NAME_Ordering  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ordering)
	PDF_OBJ_ENUM_NAME_Ordering,
#define PDF_NAME_Outlines  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Outlines)
	PDF_OBJ_ENUM_NAME_Outlines,
#define PDF_NAME_P  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_P)
	PDF_OBJ_ENUM_NAME_P,
#define PDF_NAME_PDF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PDF)
	PDF_OBJ_ENUM_NAME_PDF,
#define PDF_NAME_PS  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PS)
	PDF_OBJ_ENUM_NAME_PS,
#define PDF_NAME_Page  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Page)
	PDF_OBJ_ENUM_NAME_Page,
#define PDF_NAME_PageMode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PageMode)
	PDF_OBJ_ENUM_NAME_PageMode,
#define PDF_NAME_Pages  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Pages)
	PDF_OBJ_ENUM_NAME_Pages,
#define PDF_NAME_PaintType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PaintType)
	PDF_OBJ_ENUM_NAME_PaintType,
#define PDF_NAME_Parent  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Parent)
	PDF_OBJ_ENUM_NAME_Parent,
#define PDF_NAME_Pattern  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Pattern)
	PDF_OBJ_ENUM_NAME_Pattern,
#define PDF_NAME_PatternType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PatternType)
	PDF_OBJ_ENUM_NAME_PatternType,
#define PDF_NAME_PolyLine  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PolyLine)
	PDF_OBJ_ENUM_NAME_PolyLine,
#define PDF_NAME_Polygon  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Polygon)
	PDF_OBJ_ENUM_NAME_Polygon,
#define PDF_NAME_Popup  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Popup)
	PDF_OBJ_ENUM_NAME_Popup,
#define PDF_NAME_Predictor  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Predictor)
	PDF_OBJ_ENUM_NAME_Predictor,
#define PDF_NAME_Prev  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Prev)
	PDF_OBJ_ENUM_NAME_Prev,
#define PDF_NAME_Print  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Print)
	PDF_OBJ_ENUM_NAME_Print,
#define PDF_NAME_PrinterMark  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_PrinterMark)
	PDF_OBJ_ENUM_NAME_PrinterMark,
#define PDF_NAME_ProcSet  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ProcSet)
	PDF_OBJ_ENUM_NAME_ProcSet,
#define PDF_NAME_Producer  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Producer)
	PDF_OBJ_ENUM_NAME_Producer,
#define PDF_NAME_Properties  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Properties)
	PDF_OBJ_ENUM_NAME_Properties,
#define PDF_NAME_Push  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Push)
	PDF_OBJ_ENUM_NAME_Push,
#define PDF_NAME_Q  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Q)
	PDF_OBJ_ENUM_NAME_Q,
#define PDF_NAME_QuadPoints  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_QuadPoints)
	PDF_OBJ_ENUM_NAME_QuadPoints,
#define PDF_NAME_R  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_R)
	PDF_OBJ_ENUM_NAME_R,
#define PDF_NAME_RGB  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_RGB)
	PDF_OBJ_ENUM_NAME_RGB,
#define PDF_NAME_RI  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_RI)
	PDF_OBJ_ENUM_NAME_RI,
#define PDF_NAME_RL  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_RL)
	PDF_OBJ_ENUM_NAME_RL,
#define PDF_NAME_Range  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Range)
	PDF_OBJ_ENUM_NAME_Range,
#define PDF_NAME_Rect  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Rect)
	PDF_OBJ_ENUM_NAME_Rect,
#define PDF_NAME_Ref  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Ref)
	PDF_OBJ_ENUM_NAME_Ref,
#define PDF_NAME_Registry  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Registry)
	PDF_OBJ_ENUM_NAME_Registry,
#define PDF_NAME_ResetForm  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ResetForm)
	PDF_OBJ_ENUM_NAME_ResetForm,
#define PDF_NAME_Resources  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Resources)
	PDF_OBJ_ENUM_NAME_Resources,
#define PDF_NAME_Root  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Root)
	PDF_OBJ_ENUM_NAME_Root,
#define PDF_NAME_Rotate  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Rotate)
	PDF_OBJ_ENUM_NAME_Rotate,
#define PDF_NAME_Rows  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Rows)
	PDF_OBJ_ENUM_NAME_Rows,
#define PDF_NAME_RunLengthDecode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_RunLengthDecode)
	PDF_OBJ_ENUM_NAME_RunLengthDecode,
#define PDF_NAME_S  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_S)
	PDF_OBJ_ENUM_NAME_S,
#define PDF_NAME_SMask  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_SMask)
	PDF_OBJ_ENUM_NAME_SMask,
#define PDF_NAME_SMaskInData  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_SMaskInData)
	PDF_OBJ_ENUM_NAME_SMaskInData,
#define PDF_NAME_Screen  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Screen)
	PDF_OBJ_ENUM_NAME_Screen,
#define PDF_NAME_Separation  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Separation)
	PDF_OBJ_ENUM_NAME_Separation,
#define PDF_NAME_Shading  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Shading)
	PDF_OBJ_ENUM_NAME_Shading,
#define PDF_NAME_ShadingType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ShadingType)
	PDF_OBJ_ENUM_NAME_ShadingType,
#define PDF_NAME_Si  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Si)
	PDF_OBJ_ENUM_NAME_Si,
#define PDF_NAME_Sig  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Sig)
	PDF_OBJ_ENUM_NAME_Sig,
#define PDF_NAME_SigFlags  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_SigFlags)
	PDF_OBJ_ENUM_NAME_SigFlags,
#define PDF_NAME_Size  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Size)
	PDF_OBJ_ENUM_NAME_Size,
#define PDF_NAME_Sound  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Sound)
	PDF_OBJ_ENUM_NAME_Sound,
#define PDF_NAME_Split  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Split)
	PDF_OBJ_ENUM_NAME_Split,
#define PDF_NAME_Square  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Square)
	PDF_OBJ_ENUM_NAME_Square,
#define PDF_NAME_Squiggly  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Squiggly)
	PDF_OBJ_ENUM_NAME_Squiggly,
#define PDF_NAME_Stamp  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Stamp)
	PDF_OBJ_ENUM_NAME_Stamp,
#define PDF_NAME_Standard  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Standard)
	PDF_OBJ_ENUM_NAME_Standard,
#define PDF_NAME_StdCF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_StdCF)
	PDF_OBJ_ENUM_NAME_StdCF,
#define PDF_NAME_StmF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_StmF)
	PDF_OBJ_ENUM_NAME_StmF,
#define PDF_NAME_StrF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_StrF)
	PDF_OBJ_ENUM_NAME_StrF,
#define PDF_NAME_StrikeOut  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_StrikeOut)
	PDF_OBJ_ENUM_NAME_StrikeOut,
#define PDF_NAME_SubFilter  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_SubFilter)
	PDF_OBJ_ENUM_NAME_SubFilter,
#define PDF_NAME_Subtype  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Subtype)
	PDF_OBJ_ENUM_NAME_Subtype,
#define PDF_NAME_Subtype2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Subtype2)
	PDF_OBJ_ENUM_NAME_Subtype2,
#define PDF_NAME_T  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_T)
	PDF_OBJ_ENUM_NAME_T,
#define PDF_NAME_TR  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TR)
	PDF_OBJ_ENUM_NAME_TR,
#define PDF_NAME_TR2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TR2)
	PDF_OBJ_ENUM_NAME_TR2,
#define PDF_NAME_Text  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Text)
	PDF_OBJ_ENUM_NAME_Text,
#define PDF_NAME_TilingType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TilingType)
	PDF_OBJ_ENUM_NAME_TilingType,
#define PDF_NAME_Title  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Title)
	PDF_OBJ_ENUM_NAME_Title,
#define PDF_NAME_ToUnicode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ToUnicode)
	PDF_OBJ_ENUM_NAME_ToUnicode,
#define PDF_NAME_Trans  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Trans)
	PDF_OBJ_ENUM_NAME_Trans,
#define PDF_NAME_Transparency  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Transparency)
	PDF_OBJ_ENUM_NAME_Transparency,
#define PDF_NAME_TrapNet  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TrapNet)
	PDF_OBJ_ENUM_NAME_TrapNet,
#define PDF_NAME_TrimBox  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TrimBox)
	PDF_OBJ_ENUM_NAME_TrimBox,
#define PDF_NAME_TrueType  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_TrueType)
	PDF_OBJ_ENUM_NAME_TrueType,
#define PDF_NAME_Tx  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Tx)
	PDF_OBJ_ENUM_NAME_Tx,
#define PDF_NAME_Type  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Type)
	PDF_OBJ_ENUM_NAME_Type,
#define PDF_NAME_Type0  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Type0)
	PDF_OBJ_ENUM_NAME_Type0,
#define PDF_NAME_Type1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Type1)
	PDF_OBJ_ENUM_NAME_Type1,
#define PDF_NAME_Type1C  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Type1C)
	PDF_OBJ_ENUM_NAME_Type1C,
#define PDF_NAME_Type3  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Type3)
	PDF_OBJ_ENUM_NAME_Type3,
#define PDF_NAME_U  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_U)
	PDF_OBJ_ENUM_NAME_U,
#define PDF_NAME_UE  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_UE)
	PDF_OBJ_ENUM_NAME_UE,
#define PDF_NAME_UF  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_UF)
	PDF_OBJ_ENUM_NAME_UF,
#define PDF_NAME_URI  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_URI)
	PDF_OBJ_ENUM_NAME_URI,
#define PDF_NAME_URL  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_URL)
	PDF_OBJ_ENUM_NAME_URL,
#define PDF_NAME_Unchanged  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Unchanged)
	PDF_OBJ_ENUM_NAME_Unchanged,
#define PDF_NAME_Uncover  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Uncover)
	PDF_OBJ_ENUM_NAME_Uncover,
#define PDF_NAME_Underline  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Underline)
	PDF_OBJ_ENUM_NAME_Underline,
#define PDF_NAME_Unix  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Unix)
	PDF_OBJ_ENUM_NAME_Unix,
#define PDF_NAME_Usage  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Usage)
	PDF_OBJ_ENUM_NAME_Usage,
#define PDF_NAME_UseCMap  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_UseCMap)
	PDF_OBJ_ENUM_NAME_UseCMap,
#define PDF_NAME_UseOutlines  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_UseOutlines)
	PDF_OBJ_ENUM_NAME_UseOutlines,
#define PDF_NAME_UserUnit  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_UserUnit)
	PDF_OBJ_ENUM_NAME_UserUnit,
#define PDF_NAME_V  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_V)
	PDF_OBJ_ENUM_NAME_V,
#define PDF_NAME_V2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_V2)
	PDF_OBJ_ENUM_NAME_V2,
#define PDF_NAME_VE  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_VE)
	PDF_OBJ_ENUM_NAME_VE,
#define PDF_NAME_Version  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Version)
	PDF_OBJ_ENUM_NAME_Version,
#define PDF_NAME_VerticesPerRow  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_VerticesPerRow)
	PDF_OBJ_ENUM_NAME_VerticesPerRow,
#define PDF_NAME_W  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_W)
	PDF_OBJ_ENUM_NAME_W,
#define PDF_NAME_W2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_W2)
	PDF_OBJ_ENUM_NAME_W2,
#define PDF_NAME_WMode  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_WMode)
	PDF_OBJ_ENUM_NAME_WMode,
#define PDF_NAME_Watermark  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Watermark)
	PDF_OBJ_ENUM_NAME_Watermark,
#define PDF_NAME_Widget  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Widget)
	PDF_OBJ_ENUM_NAME_Widget,
#define PDF_NAME_Width  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Width)
	PDF_OBJ_ENUM_NAME_Width,
#define PDF_NAME_Widths  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Widths)
	PDF_OBJ_ENUM_NAME_Widths,
#define PDF_NAME_WinAnsiEncoding  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_WinAnsiEncoding)
	PDF_OBJ_ENUM_NAME_WinAnsiEncoding,
#define PDF_NAME_Wipe  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_Wipe)
	PDF_OBJ_ENUM_NAME_Wipe,
#define PDF_NAME_XHeight  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XHeight)
	PDF_OBJ_ENUM_NAME_XHeight,
#define PDF_NAME_XObject  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XObject)
	PDF_OBJ_ENUM_NAME_XObject,
#define PDF_NAME_XRef  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XRef)
	PDF_OBJ_ENUM_NAME_XRef,
#define PDF_NAME_XRefStm  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XRefStm)
	PDF_OBJ_ENUM_NAME_XRefStm,
#define PDF_NAME_XStep  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XStep)
	PDF_OBJ_ENUM_NAME_XStep,
#define PDF_NAME_XYZ  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_XYZ)
	PDF_OBJ_ENUM_NAME_XYZ,
#define PDF_NAME_YStep  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_YStep)
	PDF_OBJ_ENUM_NAME_YStep,
#define PDF_NAME_adbe_pkcs7_detached  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_adbe_pkcs7_detached)
	PDF_OBJ_ENUM_NAME_adbe_pkcs7_detached,
#define PDF_NAME_ca  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_ca)
	PDF_OBJ_ENUM_NAME_ca,
#define PDF_NAME_n0  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_n0)
	PDF_OBJ_ENUM_NAME_n0,
#define PDF_NAME_n1  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_n1)
	PDF_OBJ_ENUM_NAME_n1,
#define PDF_NAME_n2  ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME_n2)
	PDF_OBJ_ENUM_NAME_n2,
#define PDF_OBJ_NAME__LIMIT ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NAME__LIMIT)
	PDF_OBJ_ENUM_NAME__LIMIT,
#define PDF_OBJ_FALSE ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_BOOL_FALSE)
	PDF_OBJ_ENUM_BOOL_FALSE = PDF_OBJ_ENUM_NAME__LIMIT,
#define PDF_OBJ_TRUE ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_BOOL_TRUE)
	PDF_OBJ_ENUM_BOOL_TRUE,
#define PDF_OBJ_NULL ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM_NULL)
	PDF_OBJ_ENUM_NULL,
#define PDF_OBJ__LIMIT ((pdf_obj *)(intptr_t)PDF_OBJ_ENUM__LIMIT)
	PDF_OBJ_ENUM__LIMIT
};
