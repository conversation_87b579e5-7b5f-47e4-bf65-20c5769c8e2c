package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogClearHistoryBinding
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ListActionEnums


class DialogClearHistory : BaseDialog<DialogClearHistoryBinding>() {

    companion object {
        //arguments
        private var callback: ((ListActionEnums) -> Unit)? = null
        fun getInstance(
            callback: ((ListActionEnums) -> Unit)
        ): DialogClearHistory {
            this.callback = callback
            return DialogClearHistory()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        binding.btnClearHistory.setOnClickListener {
            callback?.invoke(ListActionEnums.CLEAR_HISTORY_TRIGGERED)
            dismiss()
        }

    }

    override fun onStart() {
        super.onStart()
        setTopAnimation()
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogClearHistoryBinding = DialogClearHistoryBinding.inflate(inflater, container, false)
}