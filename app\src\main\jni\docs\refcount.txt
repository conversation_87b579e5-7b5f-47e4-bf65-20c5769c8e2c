Reference counting uses special words in functions to make it easy to remember
and follow the rules.

Words that take ownership: new, find, load, open, keep.

Words that release ownership: drop, free, close.

If an object is returned by a function with one of the special words that take
ownership, you are responsible for freeing it by calling "drop" or "free", or
"close" before you return. You may pass ownership of an owned object by return
it only if you name the function using one of the special words.

Any objects returned by functions that do not have any of these special words,
are borrowed and have a limited life time. Do not hold on to them past the
duration of the current function, or stow them away inside structs. If you need
to keep the object for longer than that, you have to either "keep" it or make
your own copy.
