.TH MUPDF 1 "May 25, 2015"
.\" Please adjust this date whenever revising the manpage.

.SH NAME
mupdf \- MuPDF is a lightweight PDF viewer written in portable C

.SH SYNOPSIS
.B mupdf
.RI [ options ] " PDFfile"

.SH DESCRIPTION
This manual page briefly describes the
.B mupdf
command.
.PP

.SH OPTIONS
A description of each of the supported options is included below.

.TP
.B \-p password
Uses the given password to open an encrypted PDF file.
The password is tried both as user and owner password.
.TP
.B \-r resolution
Changes the initial zoom level, specified as the resolution in dpi.
The default value is 72.
.TP
.B \-A bits
Changes the anti-aliasing quality, specified as a number of bits between 0
(off) and 8 (best). The default value is 8.
.TP
.B \-C RRGGBB
Sets the full-page tint using hexadecimal color syntax. The default value
is FFFAF0.
.TP
.B \-W width
Page width in points for EPUB layout.
.TP
.B \-H height
Page height in points for EPUB layout.
.TP
.B \-S size
Font size in points for EPUB layout.

.SH MOUSE BEHAVIOR

.TP
.B Left mouse button click
A left click on a hyper link follows the link.
.TP
.B Left mouse button drag
Pan the page. Panning beyond the bottom or top
edge will go to the next or previous page.
.TP
.B Right mouse button drag
Select text in an area. On X11, the selected text
can be pasted in another application with a middle click.
Press Ctl+C to copy the selected text to the clipboard.
On windows, the selected text will automatically be copied
to the clipboard.
.TP
.B Scroll wheel
Pan page up or down. Does not change page
when reaching the bottom or top edge.
.TP
.B Shift + Scroll wheel
Pan page left or right.
.TP
.B Control + Scroll wheel
Zoom in or out.

.SH KEY BINDINGS

.TP
.B L, R
Rotate page left (counter-clockwise) or right (clockwise).
.TP
.B h, j, k, l
Pan page left, down, up, or right.
.TP
.B \+, \-
Zoom in or out.
.TP
.B W, H
Zoom page to exactly fit width or height of window.
.TP
.B w
Shrinkwrap window to fit the page.
.TP
.B r
Reload file.
.TP
.B . pgdn right space
Go to the next page
.TP
.B , pgup left b backspace
Go to the previous page
.TP
.B <, >
Skip back/forth 10 pages at a time.
.TP
.B m
Mark current page for snap back. Up to 256 pages can be marked.
.TP
.B t
Pop back to the latest mark.
.TP
.B [0-9]m
Save the current page number in the numbered register.
.TP
.B [0-9]t
Go to the page saved in the numbered register.
.TP
.B 123g
Go to page 123.
.TP
.B g, G
Go to the first or last page.
.TP
.B /, ?
Search for text forwards or backwards.
.TP
.B n, N
Find the next/previous search result.
.TP
.B f
Toggles fullscreen mode.
.TP
.B p
Toggle presentation mode.
.TP
.B c
Toggle between color and grayscale rendering.
.TP
.B C
Toggle full-page color tinting.
.B i
Toggle between normal and inverted color rendering.
.TP
.B q
Quit.

.SH SIGNALS

.TP
.B SIGHUP
Sending a \fBSIGHUP\fR signal to the mupdf process will also cause the viewed
file to be reloaded automatically, for use in e.g. build scripts.

.SH SEE ALSO
.BR mutool (1).

.SH AUTHOR
MuPDF is Copyright 2006-2014 Artifex Software, Inc.
