package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogPermissionLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions

class PermissionDialog: BaseDialog<DialogPermissionLayoutBinding>() {

    companion object {
        //arguments
        private var callback: ((ViewPdfActions) -> Unit)? = null
        fun getInstance(
            callback: ((ViewPdfActions) -> Unit)
        ): PermissionDialog {
            this.callback = callback
            return PermissionDialog()
        }
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogPermissionLayoutBinding.inflate(inflater, container, false)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = false

        binding.btnAllowPermission.setOnClickListener {
            callback?.invoke(ViewPdfActions.RUNTIME_PERMISSION_ALLOW)
            dismiss()
        }

    }

    override fun onStart() {
        super.onStart()
        setTopAnimationForPermissionDialog()
    }


}