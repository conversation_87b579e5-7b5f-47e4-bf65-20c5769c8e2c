package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.language

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.edit
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.google.android.gms.ads.nativead.NativeAd
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivityLanguageBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.startAppActivity
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AdUtility.isSplashAdFailed
import pdf.reader.editor.pdfviewer.pdfreader.manager.language.LanguageData
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main.MainActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.splash.SplashViewModel
import javax.inject.Inject

@Suppress("DEPRECATION")
@AndroidEntryPoint
class LanguageActivity : AppCompatActivity() {

    private val binding: ActivityLanguageBinding by lazy {
        ActivityLanguageBinding.inflate(layoutInflater)
    }
    private val splashViewModel: SplashViewModel by viewModels()
    private var selected = 0
    private var languageAdapter: LanguageAdapter? = null
    private val storage: Storage by lazy {
        Storage(this)
    }
    private val adViewModel by viewModels<AdViewModel>()

    @Inject
    lateinit var adsIds: AdsIds

    private fun applySystemBarsPadding(targetView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(targetView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= 35) {
            enableEdgeToEdge()
        }
        setContentView(binding.root)
        if (Build.VERSION.SDK_INT >= 35) {
            applySystemBarsPadding(binding.root)
        }
        initViews()
        storage.preferences.edit {
            putBoolean("isLanguageSet", true)
        }
    }

    private fun initViews() {
        selected = storage.preferences.getInt("localization", 0)
        if (selected == 0)
            selected = LanguageData.getAppLanguageCodeList().indexOf("en")
        languageAdapter =
            LanguageAdapter(LanguageData.getAppLanguageList(), selected) {
                selected = it
            }
        binding.apply {

            languageToolbar.apply {
                setOnMenuItemClickListener { item ->

                    if (item.itemId == R.id.btnLanguageDone) {
                        if (selected == 0) {
                            selected = 100
                        }
                        storage.preferences.edit {
                            putInt("localization", selected)
                        }
                        startAppActivity<MainActivity>()

                        showInterstitialFailAd()
                        finish()
                        true
                    } else
                        false
                }
            }

            languageRecyclerView.adapter = languageAdapter
        }

        splashViewModel.getNative().observe(this) {
            Log.d("TAG", "initViews: lang natiive")
            inflateNativeAd(it)
        }
    }

    private fun showInterstitialFailAd() {
        if (isSplashAdFailed) {
            isSplashAdFailed = false

            adsIds.apply {
                adViewModel.apply {
                    splashInterWhenFail.run {
                        canRequestAd = storage.isConsentDone
                        remoteConfig =
                            adViewModel.getRemoteConfigModel().adConfigModel.splashInterWhenFail.show
                    }
                    showInterstitialAd(
                        activity = this@LanguageActivity,
                        adInfo = splashInterWhenFail,
                    )

                }
            }
        }
    }

    private fun inflateNativeAd(nativeAd: NativeAd?) {
        if (nativeAd == null)
            return
//        binding.adLoadingLabel.visibility = View.GONE
        binding.tvAdTitle.text = nativeAd.headline
        binding.nativeAdView.mediaView = binding.adMedia
        binding.nativeAdView.mediaView?.setOnHierarchyChangeListener(object :
            ViewGroup.OnHierarchyChangeListener {
            override fun onChildViewAdded(parent: View?, child: View?) {
                if (child is ImageView) {
                    child.scaleType = ImageView.ScaleType.CENTER_INSIDE
                }
            }

            override fun onChildViewRemoved(parent: View?, child: View?) {
            }
        })

        if (nativeAd.body == null)
            binding.tvAdDesc.visibility = View.GONE
        else {
            binding.tvAdDesc.visibility = View.VISIBLE
            binding.tvAdDesc.text = nativeAd.body
        }
        if (nativeAd.icon == null)
            binding.ivAdImg.visibility = View.GONE
        else {
            binding.ivAdImg.visibility = View.VISIBLE
            binding.ivAdImg.setImageDrawable(nativeAd.icon?.drawable)
        }
        if (nativeAd.callToAction == null)
            binding.btnRedirection.visibility = View.GONE
        else {
            binding.btnRedirection.visibility = View.VISIBLE
            binding.btnRedirection.text = nativeAd.callToAction
            binding.nativeAdView.callToActionView = binding.btnRedirection
        }

        binding.nativeAdView.setNativeAd(nativeAd)

        binding.nativeAdView.visibility = View.VISIBLE
    }

    @Deprecated(
        "Deprecated in Java",
        ReplaceWith("super.onBackPressed()", "androidx.appcompat.app.AppCompatActivity")
    )
    override fun onBackPressed() {
        super.onBackPressed()
        startAppActivity<MainActivity>()
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        adViewModel.destroyNativeAd(adsIds.languageNative.adKey)
        adViewModel.destroyInterstitialAd(adsIds.splashInterWhenFail.adKey)
    }
}