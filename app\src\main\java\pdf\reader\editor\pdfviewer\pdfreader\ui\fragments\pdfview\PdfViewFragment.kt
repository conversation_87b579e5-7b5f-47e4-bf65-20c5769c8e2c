package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentPdfViewBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.toPdfStoreItem
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.BottomSheetFragment
import java.io.File


@AndroidEntryPoint
class PdfViewFragment : BaseFragment<FragmentPdfViewBinding, PDFViewerViewModel>() {
    override val viewModel: Class<PDFViewerViewModel>
        get() = PDFViewerViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentPdfViewBinding = FragmentPdfViewBinding.inflate(
        inflater, container, false
    )

    private var pdfPassword = ""

    //    private val args: PdfViewFragmentArgs by navArgs()
    private var bottomSheetFragment: BottomSheetFragment? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
    }

    private fun initViews() {
//        mViewModel.documentModel = args.pdfFile
        mViewDataBinding.txtPDFName.text = mViewModel.documentModel?.fileName

        loadFile(mViewModel.documentModel, pdfPassword)
        mViewDataBinding.btnBack.setOnClickListener {
            findNavController().popBackStack()
        }

        mViewDataBinding.btnSharePDF.setOnClickListener {
            FileUtils.shareFile(mViewModel.documentModel?.absolutePath!!, requireContext())
        }

        mViewDataBinding.btnSearchPage.setOnClickListener {
//            DialogGoToPage(mViewDataBinding.pdfView.pageCount) { pageNo, action ->
//                when (action) {
//                    ViewPdfActions.OK_GOTO_PAGE -> {
//                        mViewDataBinding.pdfView.jumpTo(pageNo)
//                    }
//                    ViewPdfActions.PAGE_SIZE_EXCEDED -> {
//                        showToast(getString(R.string.txt_pdf_PAGE_404))
//                    }
//                    ViewPdfActions.VALID_PAGE_SIZE -> {
//                        showToast(getString(R.string.txt_pdf_PAGE_valid))
//                    }
//                }
//            }.show(childFragmentManager, "dialogGoToPage")
        }

        mViewModel.insertRecentFiles(
            mViewModel.documentModel?.toPdfStoreItem(
                getLocalDate(),
                getLocalTimeStamp()
            )!!
        )

        mViewDataBinding.btnOptions.setOnClickListener {
//            bottomSheetFragment = BottomSheetFragment(0, mViewModel.documentModel!!, update = {
//
//            }) {
////                when (it) {
////                    "split" -> {
////                        bottomSheetFragment?.dismiss()
////
////                        val direc = PdfViewFragmentDirections.actionPdfViewFragmentToSplitFragment(
////                            mViewModel.documentModel!!
////                        )
////                        requireActivity().findNavController(R.id.navHostFragment)
////                            .navigate(direc)
////
////                    }
////                    "merge" -> {
////                        bottomSheetFragment?.dismiss()
////                        val direc = PdfViewFragmentDirections.actionPdfViewFragmentToMergeFragment(
////                            mViewModel.documentModel!!
////                        )
////                        requireActivity().findNavController(R.id.navHostFragment)
////                            .navigate(direc)
////                    }
////                    else -> {
////                        bottomSheetFragment?.dismiss()
////                        val direc =
////                            PdfViewFragmentDirections.actionPdfViewFragmentToSortPDFFragment(
////                                mViewModel.documentModel!!
////                            )
////                        requireActivity().findNavController(R.id.navHostFragment)
////                            .navigate(direc)
////                    }
////                }
//            }
//
//            bottomSheetFragment
//                ?.show(childFragmentManager, "BottomSheetDialog")

        }

        mViewModel.listData.observe(viewLifecycleOwner) {
            loadFile(mViewModel.documentModel, pdfPassword)
        }
    }

    private fun loadFile(pdfFile: DocumentsModel?, password: String) {
//        val defaultScrollHandle = DefaultScrollHandle(requireContext(), false)
//        defaultScrollHandle.setHandler {
////                if (it) tvPageIndex.visibility = View.VISIBLE
////                else tvPageIndex.visibility = View.GONE
//        }
        mViewDataBinding.pdfView.apply {


            fromFile(File(pdfFile?.absolutePath!!))
                .password(password)
                .spacing(10)
//                .swipeHorizontal(PreferenceUtil.scrollHorizontal)
//                .nightMode(PreferenceUtil.pdfNightMode).onLoad { count ->
//                    totalPages = count
//                }
                .onTap {
//                    toggleFullScreenMode()
                    true
                }
                .onError {
                    DialogPdfPassword.getInstance { action, password ->
                        when (action) {
                            ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                                pdfPassword = password
                                loadFile(pdfFile, pdfPassword)
                            }
                            ViewPdfActions.NULL_PDF_PASSWORD -> {
                                showToast(getString(R.string.txt_pdf_null_password_warning))
                            }
                            ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
                                findNavController().popBackStack()

                            }

                            else -> {}
                        }
                    }.show(childFragmentManager, "Password Dialog")
                }
//                .scrollHandle(defaultScrollHandle)
                .load()
        }
    }

}