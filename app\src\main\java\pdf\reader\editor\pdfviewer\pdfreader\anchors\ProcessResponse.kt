package pdf.reader.editor.pdfviewer.pdfreader.anchors

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName

data class ProcessResponse(
    @SerializedName("download_filename") val download_filename: String?,
    @SerializedName("filesize") val fileSize: Int,
    @SerializedName("output_filesize") val outputFileSize: Int,
    @SerializedName("output_filenumber") val outputFileNumber: Int,
    @SerializedName("output_extensions") val outputExtensions: String?,
    @SerializedName("timer") val timer: Double,
    @SerializedName("status") val status: String?
):Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readDouble(),
        parcel.readString()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(download_filename)
        parcel.writeInt(fileSize)
        parcel.writeInt(outputFileSize)
        parcel.writeInt(outputFileNumber)
        parcel.writeString(outputExtensions)
        parcel.writeDouble(timer)
        parcel.writeString(status)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ProcessResponse> {
        override fun createFromParcel(parcel: Parcel): ProcessResponse {
            return ProcessResponse(parcel)
        }

        override fun newArray(size: Int): Array<ProcessResponse?> {
            return arrayOfNulls(size)
        }
    }
}