package pdf.reader.editor.pdfviewer.pdfreader.anchors

import android.graphics.Bitmap
import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import pdf.reader.editor.pdfviewer.pdfreader.local.database.entity.PdfStoreItems

@Keep
class DocumentsModel(
    var fileName: String = "Unknown",
    var fileSize: String = "0kb",
    var fileMimeType: String = "",
    var fileDate: String = "",
    var parentFile: String = "",
    var absolutePath: String = "",
    var sizeInDigit: Long = 0,
    var dateInDigit: Long = 0,
    var isBookmarked: Int = 0,
    private var isSelected: Int = 0,
    var bitmap: Bitmap? = null,
    var password: String = ""
) : Parcelable {
    constructor(parcel: Parcel) : this(

        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt(),
        parcel.readInt(),
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(fileName)
        parcel.writeString(fileSize)
        parcel.writeString(fileMimeType)
        parcel.writeString(fileDate)
        parcel.writeString(parentFile)
        parcel.writeString(absolutePath)
        parcel.writeLong(sizeInDigit)
        parcel.writeInt(isBookmarked)
        parcel.writeInt(isSelected)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DocumentsModel> {
        override fun createFromParcel(parcel: Parcel): DocumentsModel {
            return DocumentsModel(parcel)
        }

        override fun newArray(size: Int): Array<DocumentsModel?> {
            return arrayOfNulls(size)
        }
    }

    fun isSelected(): Boolean = isSelected == 1

    fun setSelected(selected: Boolean) {
        isSelected = if (selected) 1 else 0
    }

    fun isBookmarked(): Boolean = isBookmarked == 1

    fun setBookmarked(bookmarked: Boolean) {
        isBookmarked = if (bookmarked) 1 else 0
    }


    fun toPdfStoreModel(): PdfStoreItems {

        return PdfStoreItems(
            absolutePath,
            fileName,
            fileSize,
            fileDate,
            fileDate,
            fileDate,
            fileDate
        )
    }

    override fun toString(): String {
        return "DocumentsModel(fileName='$fileName', fileSize='$fileSize', fileMimeType='$fileMimeType', fileDate='$fileDate', parentFile='$parentFile', absolutePath='$absolutePath', sizeInDigit=$sizeInDigit, dateInDigit=$dateInDigit, isBookmarked=$isBookmarked, isSelected=$isSelected, bitmap=$bitmap, password='$password')"
    }
}
