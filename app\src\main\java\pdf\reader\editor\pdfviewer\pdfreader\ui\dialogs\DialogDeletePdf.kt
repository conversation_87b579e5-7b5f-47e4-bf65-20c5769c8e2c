package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogDeletePdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.PdfActionsEnums
import javax.inject.Inject

@AndroidEntryPoint
class DialogDeletePdf : BaseDialog<DialogDeletePdfBinding>() {

    companion object {
        //arguments
        private var callback: ((PdfActionsEnums) -> Unit)? = null
        private var documentsModel: DocumentsModel? = null
        fun getInstance(
            documentsModel: DocumentsModel,
            callback: ((PdfActionsEnums) -> Unit)
        ): DialogDeletePdf {
            this.callback = callback
            this.documentsModel = documentsModel
            return DialogDeletePdf()
        }
    }

    @Inject
    lateinit var repositoryLocal: RepositoryLocal

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        binding.btnDelete.setOnClickListener {
            logs("DeleteDialog_delete btn clicked")
            FileUtils.deleteFile(documentsModel?.absolutePath!!, requireContext()) {
                callback?.invoke(PdfActionsEnums.DELETE_PDF)
                repositoryLocal.deleteFromRecent(documentsModel?.absolutePath!!)
                dismiss()
            }
        }

    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogDeletePdfBinding =
        DialogDeletePdfBinding.inflate(LayoutInflater.from(requireContext()), null, false)
}