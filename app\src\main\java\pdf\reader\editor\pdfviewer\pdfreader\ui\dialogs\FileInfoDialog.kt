package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.LayoutDialogFileInfoBinding

class FileInfoDialog : BaseDialog<LayoutDialogFileInfoBinding>() {

    companion object {

        private var documentsModel: DocumentsModel? = null
        fun getInstance(
            documentsModel: DocumentsModel,
        ): FileInfoDialog {
            this.documentsModel = documentsModel
            return FileInfoDialog()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnDone.setOnClickListener { dismiss() }

        binding.txtDetailFileName.text = documentsModel?.fileName
        binding.txtDetailFilePath.text = documentsModel?.absolutePath
        binding.txtDetailDate.text = documentsModel?.fileDate
        binding.txtDetailFileSize.text = documentsModel?.fileSize
        binding.txtDetailFileType.text = "PDF"
    }


    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): LayoutDialogFileInfoBinding =
        LayoutDialogFileInfoBinding.inflate(LayoutInflater.from(requireContext()), null, false)
}