package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.wordtopdf

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentWordConverterBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.shared.Progress
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.wordtopdf.adapter.WordAdapter
import java.io.File

@AndroidEntryPoint
class WordConverterFragment : BaseFragment<FragmentWordConverterBinding, WordViewModel>() {
    override val viewModel: Class<WordViewModel>
        get() = WordViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentWordConverterBinding =
        FragmentWordConverterBinding.inflate(inflater, container, false)

    private var fileAdapter: WordAdapter? = null
    private var selectedDoc: DocumentsModel? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()

    }

    private fun initViews() {
        isAlive { activity ->
            fileAdapter = WordAdapter { documentsModel ->
                selectedDoc = documentsModel
            }
            mViewDataBinding.filesRecyclerView.apply {
                layoutManager = LinearLayoutManager(activity)
                adapter = fileAdapter

            }

            mViewDataBinding.btnConvertFiles.setOnClickListener {
                if (selectedDoc != null) {
                    if (this.activity?.checkInternetConnection() == true) {
                        loadingDialog.show()
                        mViewModel.convertToWord(File(selectedDoc?.absolutePath!!),
                            type = FileType.PDF,
                            progress = object : Progress {
                                override fun progress(progress: Int) {

                                }

                                override fun fail() {
                                    loadingDialog.dismiss()
                                    <EMAIL>?.runOnUiThread {
                                        showToast(getString(R.string.failed_to_convert))
                                    }
                                }

                                override fun currentStatus(status: String) {

                                }

                                override fun downloadingSuccess(file: File) {

                                    <EMAIL>?.runOnUiThread {
                                        loadingDialog.dismiss()
                                        showToast(getString(R.string.converted_successfully))
                                        findNavController().popBackStack()
                                    }
                                }
                            })
                    } else
                        showToast(getString(R.string.this_feature_required_internet))

                } else
                    showToast(getString(R.string.please_select_file_first))
            }

            mViewModel.loadFiles(requireActivity())

            mViewModel.getDocuments().observe(viewLifecycleOwner) { files ->
                if (files != null) {
                    fileAdapter?.setData(files)

                }
            }

            mViewDataBinding.imgBack.setOnClickListener {
                isAlive {

                    findNavController().popBackStack()
                }
            }
        }


    }


}