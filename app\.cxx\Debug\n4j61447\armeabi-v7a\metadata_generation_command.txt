                        -HC:\Users\<USER>\StudioProjects\pdf-editor9D\app\src\main\jni
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\StudioProjects\pdf-editor9D\app\build\intermediates\cxx\Debug\n4j61447\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\StudioProjects\pdf-editor9D\app\.cxx\Debug\n4j61447\armeabi-v7a
-GNinja
-D SUPPORT_GPROOF=ON
                        Build command args: []
                        Version: 2