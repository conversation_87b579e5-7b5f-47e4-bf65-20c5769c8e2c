package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.artifex.mupdfdemo.ActionType
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.BottomSheetViewPagerBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isPortrait
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.edit.EditFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.file.SheetFileFragment


class BottomSheetFragment(
    private val pageCount: Int,
    private val documentsModel: DocumentsModel,
    private val pages: (Int) -> Unit,
    private val navigation: (ActionType) -> Unit
) :
    BottomSheetDialogFragment() {

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        if (activity?.isPortrait() == false)
            dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?

    ): View {

        val binding: BottomSheetViewPagerBinding =
            BottomSheetViewPagerBinding.inflate(inflater, container, false)


        val adapter = ViewPageAdapter(childFragmentManager)
        val bundle = Bundle()
        bundle.putParcelable("documentModel", documentsModel)

        val sheetFragment = SheetFileFragment(pageCount, navigation) { pages ->
            if (pages != null) {
                pages(pages)
            } else {

                dismiss()
                findNavController().popBackStack()
            }
        }
        sheetFragment.arguments = bundle
        adapter.addFragment(sheetFragment, "File")

        val editFragment = EditFragment(navigation)
        editFragment.arguments = bundle
        adapter.addFragment(editFragment, "Edit")

//        adapter.addFragment(EditFragment(), "Annotation")
//        adapter.addFragment(EditFragment(), "View")
        binding.viewPager.adapter = adapter
        binding.tabLayout112.setupWithViewPager(binding.viewPager)

        binding.imgCloseSheet.setOnClickListener {
            dismiss()
        }

        return binding.root
    }

}