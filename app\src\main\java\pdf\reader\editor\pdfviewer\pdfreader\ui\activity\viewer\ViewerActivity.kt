package pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer

import android.app.Service
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.PopupWindow
import android.widget.SeekBar
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.toRect
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.navArgs
import com.artifex.mupdfdemo.ActionType
import com.artifex.mupdfdemo.Annotation
import com.artifex.mupdfdemo.AsyncTask
import com.artifex.mupdfdemo.ColorPalette
import com.artifex.mupdfdemo.ColorType
import com.artifex.mupdfdemo.Hit
import com.artifex.mupdfdemo.MuPDFAlert
import com.artifex.mupdfdemo.MuPDFCore
import com.artifex.mupdfdemo.MuPDFPageAdapter
import com.artifex.mupdfdemo.MuPDFReaderView
import com.artifex.mupdfdemo.MuPDFReaderViewListener
import com.artifex.mupdfdemo.MuPDFView
import com.artifex.mupdfdemo.MyThreadExecutor
import com.artifex.mupdfdemo.OutlineActivityData
import com.artifex.mupdfdemo.ReaderView
import com.artifex.mupdfdemo.SearchTask
import com.artifex.mupdfdemo.SearchTaskResult
import com.artifex.mupdfdemo.ViewerFragmentArgs
import com.google.android.gms.ads.nativead.NativeAd
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseActivity
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ActivityViewerBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AnnotationDeleteLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.HomeNativeAdLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.HorizontalThumbBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ThumbViewLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ViewerColorSheetLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.extensions.dateDifferenceInDays
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.invisible
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isPortrait
import pdf.reader.editor.pdfviewer.pdfreader.extensions.keepScreenOn
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.extensions.setDeviceBrightness
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showCreateSheet
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.extensions.startAppActivity
import pdf.reader.editor.pdfviewer.pdfreader.extensions.toPdfStoreItem
import pdf.reader.editor.pdfviewer.pdfreader.extensions.toString
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.manager.MyBottomSheet
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.AdUtility
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.getInterstitialVsStartAppAdObject
import pdf.reader.editor.pdfviewer.pdfreader.newads.AdsIds
import pdf.reader.editor.pdfviewer.pdfreader.newads.data.BannerAdInfo
import pdf.reader.editor.pdfviewer.pdfreader.newads.data.NativeAdInfo
import pdf.reader.editor.pdfviewer.pdfreader.newads.displayBannerAd
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.newads.manageFrameLayoutView
import pdf.reader.editor.pdfviewer.pdfreader.newads.presentation.AdViewModel
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.shared.PDF_IMAGES
import pdf.reader.editor.pdfviewer.pdfreader.shared.PDF_PAGES_IMAGES
import pdf.reader.editor.pdfviewer.pdfreader.shared.Progress
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main.MainActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.pdfsplash.PdfSplashActivity.Companion.fromIntent
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.tool.ToolsActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets.AnnotationColorSheet
import pdf.reader.editor.pdfviewer.pdfreader.ui.bottomsheets.InkSettingFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.RatingsDialog
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.BottomSheetFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.pdfview.PDFViewerViewModel
import java.io.File
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.max

@AndroidEntryPoint
class ViewerActivity : BaseActivity<ActivityViewerBinding, PDFViewerViewModel>(),
    MuPDFReaderViewListener {

    override fun getViewBinding() = ActivityViewerBinding.inflate(layoutInflater)
    override val viewModel: Class<PDFViewerViewModel>
        get() = PDFViewerViewModel::class.java

    private val storage: Storage by lazy {
        Storage(this)
    }
    private val pdfViewViewModel: PDFViewerViewModel by viewModels()
    private val args: ViewerFragmentArgs by navArgs()
    private var bottomSheetFragment: BottomSheetFragment? = null
    private var colorSheetFragment: AnnotationColorSheet? = null
    private var inkSettingSheetFragment: InkSettingFragment? = null
    private var fileUri: Uri? = null
    private var documentFile: File? = null
    private var core: MuPDFCore? = null
    private var dialog: AlertDialog? = null
    private var searchTask: SearchTask? = null
    private var pdfAdapter: MuPDFPageAdapter? = null
    private var windowManager: WindowManager? = null
    private var deleteAnnotBinding: AnnotationDeleteLayoutBinding? = null
    private var popupWindow: PopupWindow? = null
    private var isEdit = false
    private var isPDFEncrypted = false
    private var mAlertTask: AsyncTask<Void, Void, MuPDFAlert>? = null
    private var mAlertBuilder: AlertDialog.Builder? = null
    private var mAlertsActive = false
    private var mAlertDialog: AlertDialog? = null
    private var readerView: MuPDFReaderView? = null
    private var actionType: ActionType? = null
    private var mPageSliderRes = 0
    private var verticalThumb: ThumbViewLayoutBinding? = null
    private var horizontalThumb: HorizontalThumbBinding? = null
    private var isReadingMode = false
    private var annotMode: Annotation.Type = Annotation.Type.UNKNOWN
    private var nativeAd: NativeAd? = null
    private val adViewModel by viewModels<AdViewModel>()

    @Inject
    lateinit var adsIds: AdsIds

    companion object {
        var isFromAssetViewer: Boolean = false
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

//        val scrolling = storage.getScrollDirection()
//        requestedOrientation = when {
//            scrolling -> ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
//
//            else -> ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//
//        }

        mAlertBuilder = AlertDialog.Builder(this)
        windowManager = getSystemService(Service.WINDOW_SERVICE) as WindowManager
        deleteAnnotBinding = AnnotationDeleteLayoutBinding.inflate(
            LayoutInflater.from(this), null, false
        )
        verticalThumb = ThumbViewLayoutBinding.inflate(LayoutInflater.from(this), null, false)
        horizontalThumb = HorizontalThumbBinding.inflate(LayoutInflater.from(this), null, false)
        isEdit = args.isEdit
        val pdfFile: DocumentsModel? = intent.getParcelableExtra("pdf_file")
        logs("ViewerActivity::fromIntent> $fromIntent")
        pdfFile?.let {
            loadBackPressInterAd()
//            if (checkInternetConnection() && storage.isConsentDone) getNativeAdObject(
//                "getString(R.string.viewer_native_ad_id)",
//                null,
//                onResult = {
//                    it?.let { it1 -> populateNative(it1) }
//                },
//                onAdLoadFailed = {
//                    val bannerAd = AdView(this)
//                    loadAdaptiveBanner(bannerAd)
//                    bannerAd.adListener = object : AdListener() {
//                        override fun onAdLoaded() {
//                            super.onAdLoaded()
//                            if (mViewDataBinding.bannerAdLayout.childCount > 0) {
//                                mViewDataBinding.bannerAdLayout.removeAllViews()
//                            }
//                            if (bannerAd.parent != null) {
//                                (bannerAd.parent as ViewGroup).removeView(bannerAd)
//                            }
//                            mViewDataBinding.bannerAdLayout.addView(bannerAd)
//                        }
//                    }
//                    Unit
//                })
            if (isPortrait()) {
                loadNativeCall()
            }else{
                hideAdViews()
            }
            pdfViewViewModel.documentModel = it
//            loadingDialog.show()
            Handler(mainLooper!!).postDelayed({
                loadFile()
            }, 400)
        }
    }

    private fun loadNativeCall() {
        adViewModel.apply {
            adsIds.apply {
                bannerViewReader.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.bannerViewReader.show
                }
                nativeViewReader.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.nativeViewReader.show
                }
                fileViewIntentBannerAd.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.fileViewIntentBannerAd.show
                }
                fileViewIntentNative.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.fileViewIntentNative.show
                }
                if (fromIntent) {
                    if (getRemoteConfigModel().fileViewerPlacementAdIntent == 1L) {
                        loadNativeAdCall(fileViewIntentNative)
                    } else {
                        loadBannerCall(fileViewIntentBannerAd)
                    }
                } else {
                    if (getRemoteConfigModel().fileViewerPlacementAd == 1L) {
                        loadNativeAdCall(nativeViewReader)
                    } else {
                        loadBannerCall(bannerViewReader)
                    }
                }
            }
        }
    }

    private fun loadBannerCall(bannerAdInfo: BannerAdInfo) {
        mViewDataBinding.apply {
            shimmer.shimmer.hide()
            nativeAdLayout.nativeAdView.hide()
            bannerView.adViewContainer.show()
            adViewModel.loadBanner(bannerAdInfo = bannerAdInfo,
                context = this@ViewerActivity,
                view = bannerView.adViewContainer,
                onBannerNotFound = { manageFrameLayoutView(bannerView.adViewContainer) },
                onBannerLoaded = { i, hashMap ->
                    displayBannerAd(bannerView.adViewContainer, hashMap[i]?.adView)
                },
                onBannerFailedToLoad = { manageFrameLayoutView(bannerView.adViewContainer) })
        }
    }

    private fun loadNativeAdCall(nativeAdInfo: NativeAdInfo) {
        mViewDataBinding.apply {
            bannerView.adViewContainer.hide()
            shimmer.shimmer.show()

            adViewModel.loadNativeAd(nativeAdInfo = nativeAdInfo,
                onAdNotFound = {
                    shimmer.shimmer.hide()
                    nativeAdLayout.nativeAdView.hide()
                },
                onAdLoaded = { i, hashMap ->
                    shimmer.shimmer.hide()
                    nativeAdLayout.nativeAdView.show()
                    hashMap[i]?.nativeAd?.let { populateNative(it) }
                },
                onAdFailedToLoad = {
                    shimmer.shimmer.hide()
                    nativeAdLayout.nativeAdView.hide()
                })
        }


    }

    private fun loadBackPressInterAd() {
        adsIds.apply {
            adViewModel.apply {
                fileViewInterAdBackPress.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig =
                        getRemoteConfigModel().adConfigModel.fileViewInterAdBackPress.show
                }
                fileViewInterIntentBack.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.fileViewInterIntentBack.show
                }
                if (fromIntent) {
                    if (AdUtility.isFromIntentInterOpenFailed) {
                        loadInterstitialAd(
                            adInfo = fileViewInterIntentBack
                        )
                    }
                } else {
                    if (AdUtility.isFromInterOpenFailed) {
                        loadInterstitialAd(adInfo = fileViewInterAdBackPress)
                    }
                }
            }
        }
    }

    private fun populateNative(nativeAd: NativeAd) {

        val binding: HomeNativeAdLayoutBinding = mViewDataBinding.nativeAdLayout
        binding.nativeAdView.headlineView = binding.txtTitle
        binding.nativeAdView.callToActionView = binding.btnCallToAction

        binding.btnCallToAction.text = nativeAd.callToAction
        binding.txtAdDescription.text = nativeAd.body
        (binding.nativeAdView.headlineView as AppCompatTextView).text = nativeAd.headline
        binding.imgIcon.setImageDrawable(nativeAd.icon?.drawable)

        binding.nativeAdView.setNativeAd(nativeAd)
        binding.parent.show()


    }

    private fun loadAd() {


        if (checkInternetConnection() && storage.isConsentDone) {
            getInterstitialVsStartAppAdObject("getString(R.string.viewer_back_int_ad_id)",
                onResult = {
                    pdfViewViewModel.interstitialAd = it
                },
                onAdClosed = {},
                onAdLoadFailed = {})

        }

    }

    private fun loadFile() {

//        windowManager = requireActivity().getSystemService(Service.WINDOW_SERVICE) as WindowManager
//        deleteAnnotBinding = AnnotationDeleteLayoutBinding.inflate(
//            LayoutInflater.from(requireContext()),
//            null,
//            false
//        )
//        mViewModel.documentModel = args.pdfFile
//        isEdit = args.isEdit

        val buffer: ByteArray?
//        fileUri =
//            FileUtils.getUriFromPath(requireContext(), mViewModel.documentModel?.absolutePath!!)

        if (fileUri != null && fileUri.toString().startsWith("content://")) {
            var reason: String? = null
            try {
                val `is` = contentResolver?.openInputStream(fileUri!!)
                val len = `is`?.available()
                buffer = ByteArray(len!!)
                `is`.read(buffer, 0, len)
                `is`.close()
            } catch (e: OutOfMemoryError) {
                reason = e.toString()
            } catch (e: Exception) {
                try {
                    val cursor = contentResolver?.query(
                        fileUri!!, arrayOf("_data"), null, null, null
                    )
                    if (cursor!!.moveToFirst()) {
                        val str = cursor.getString(0)
                        if (str == null) {
                            reason = "Couldn't parse data in intent"
                        } else {
                            fileUri = Uri.parse(str)
                        }
                    }
                    cursor.close()
                } catch (e2: Exception) {
                    reason = e2.toString()
                }
            }
//            if (reason != null) {
//                showErrorDialog(
//                    String.format(
//                        getString(R.string.cannot_open_document_Reason),
//                        reason
//                    )
//                )
//                return
//            }

//            core = if (buffer != null) {
//                openBuffer(buffer)
//            } else {
//                openFile(Uri.decode(fileUri?.encodedPath))
//            }
//            if (mViewModel.core == null) {
//                mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
//                core = mViewModel.core
//            } else {

//            mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
            try {
                core = openFile(pdfViewViewModel.documentModel?.absolutePath!!)
            } catch (e: UnsatisfiedLinkError) {
            }

//            }


            SearchTaskResult.set(null)

            try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    val result =
                        core?.authenticatePassword(pdfViewViewModel.documentModel?.password!!)
                    if (result == true) {
                        initViews()
                    }
//                requestPassword()
                } else {
                    initViews()
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }


            try {
                if (core == null) showErrorDialog(getString(R.string.cannot_open_document))
            } catch (e: UnsatisfiedLinkError) {
            }
        } else {
//            if (mViewModel.core == null) {
//                mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
//                core = mViewModel.core
//            } else {
//            lifecycleScope
//                .launch(Dispatchers.Default) {

//            mViewModel.core = openFile(mViewModel.documentModel?.absolutePath!!)
            try {
                core = openFile(pdfViewViewModel.documentModel?.absolutePath!!)
            } catch (e: UnsatisfiedLinkError) {
                loadingDialog.dismiss()
            }

            SearchTaskResult.set(null)

            /*try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    val result =
                        core?.authenticatePassword(mViewModel.documentModel?.password!!)
                    if (result == true) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            initViews()
                        }
                    }

//                requestPassword()
                } else {
                    lifecycleScope.launch(Dispatchers.Main) {
                        initViews()
                    }
                }
            } catch (e: UnsatisfiedLinkError) {
            }*/

            /*try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core == null)
                    lifecycleScope.launch(Dispatchers.Main) {
                        loadingDialog?.dismiss()
                        showErrorDialog(getString(R.string.cannot_open_document))
                    }
            } catch (e: UnsatisfiedLinkError) {
            }*/
//                }

//            }


            try {
                if (core != null && core?.needsPassword() == true && core?.countPages() != 0) {
                    loadingDialog.dismiss()
                    val result =
                        core?.authenticatePassword(pdfViewViewModel.documentModel?.password!!)
                    if (result == true) {
                        initViews()
                    } else requestPassword()
                } else {

                    initViews()
                }
            } catch (e: UnsatisfiedLinkError) {
            }


            try {
                if (core != null && core?.countPages() == 0) {
                    core = null
                }
            } catch (e: UnsatisfiedLinkError) {
            }

            try {
                if (core == null) {
                    loadingDialog.dismiss()
                    showErrorDialog(getString(R.string.cannot_open_document))
                }
            } catch (e: UnsatisfiedLinkError) {
            }
        }


    }

    private fun initViews() {
        if (core != null) {
            core?.onSharedPreferenceChanged()
            readerView = MuPDFReaderView(this)
            mViewDataBinding.readerContainer.addView(readerView)
            readerView?.setListener(this)
            loadAd()

            val colorType = when (storage.backgroundColor) {
                ColorType.GREY.name -> {
                    readerView?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources, R.color.grey_400, null
                        )
                    )
                    ColorType.GREY
                }

                ColorType.YELLOW.name -> {
                    readerView?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources, R.color.yellow_100, null
                        )
                    )
                    ColorType.YELLOW
                }

                ColorType.BLACK.name -> {
                    readerView?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources, R.color.black, null
                        )
                    )
                    ColorType.BLACK
                }

                else -> {
                    readerView?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources, R.color.white, null
                        )
                    )
                    ColorType.WHITE
                }
            }
            pdfAdapter = MuPDFPageAdapter(
                this, core, colorType
            )

            handleViewerScrolling()

            readerView?.adapter = pdfAdapter
            readerView?.setMode(MuPDFReaderView.Mode.Viewing)
            readerView?.setLinksEnabled(true)
            searchTask = object : SearchTask(this, core) {
                override fun onTextFound(result: SearchTaskResult) {
                    SearchTaskResult.set(result)

                    readerView?.displayedViewIndex = result.pageNumber
                    readerView?.resetupChildren()
                }
            }
            this.documentFile = File(pdfViewViewModel.documentModel?.absolutePath!!)
            mViewDataBinding.txtPDFName.text = pdfViewViewModel.documentModel?.fileName

            if (!storage.isBrightnessAuto) {
                val brightness = abs(storage.brightness)
                setBrightness(brightness)
            }

            mViewDataBinding.imgCloseReadMode.setOnClickListener {

                mViewDataBinding.imgCloseReadMode.hide()
                isReadingMode = false
                keepScreenOn(false)
                hideOrShow()

            }

            mViewDataBinding.btnBack.setOnClickListener {

                keepScreenOn(false)
                setDeviceBrightness()
                onBackPressed()

            }

            mViewDataBinding.btnPrintPDF.setOnClickListener {
                FileUtils.printPDF(
                    pdfViewViewModel.documentModel?.absolutePath!!,
                    pdfViewViewModel.documentModel?.password!!,
                    this
                )
            }

            pdfViewViewModel.insertRecentFiles(
                pdfViewViewModel.documentModel?.toPdfStoreItem(
                    getLocalDate(), getLocalTimeStamp()
                )!!
            )
            if (isFromAssetViewer) {
                mViewDataBinding.btnOptions.hide()
            } else {
                mViewDataBinding.btnOptions.show()
            }
            mViewDataBinding.btnOptions.setOnClickListener {

                bottomSheetFragment = BottomSheetFragment(readerView?.adapter?.count!!,
                    pdfViewViewModel.documentModel!!,
                    pages = { page ->
                        readerView?.displayedViewIndex = page
                    }) {
                    when (it) {
                        ActionType.SPLIT -> {

                            bottomSheetFragment?.dismiss()

                            try {
                                if (core != null && core?.hasChanges() == true) {
                                    showSaveDialog { flag ->
                                        if (flag) {
                                            actionType = ActionType.SPLIT
                                            savePDF()
                                        } else {
                                            startAppActivity<ToolsActivity>(
                                                Pair(
                                                    "action", ActionType.SPLIT.name
                                                ), Pair("doc", pdfViewViewModel.documentModel)
                                            )

                                        }
                                    }
                                } else {
                                    Log.d("TAG", "initViews: ${pdfViewViewModel.documentModel}")
//                                    destroyCore()
                                    startAppActivity<ToolsActivity>(
                                        Pair(
                                            "action", ActionType.SPLIT.name
                                        ), Pair("doc", pdfViewViewModel.documentModel)
                                    )
                                }
                            } catch (e: UnsatisfiedLinkError) {
                            }


                        }

                        ActionType.MERGE -> {
                            bottomSheetFragment?.dismiss()
                            try {
                                if (core != null && core?.hasChanges() == true) {
                                    showSaveDialog { flag ->
                                        if (flag) {
                                            actionType = ActionType.MERGE
                                            savePDF()
                                        } else {

                                            startAppActivity<ToolsActivity>(
                                                Pair(
                                                    "action", ActionType.MERGE.name
                                                ), Pair("doc", pdfViewViewModel.documentModel)
                                            )
                                        }
                                    }
                                } else {
                                    startAppActivity<ToolsActivity>(
                                        Pair(
                                            "action", ActionType.MERGE.name
                                        ), Pair("doc", pdfViewViewModel.documentModel)
                                    )
                                }
                            } catch (
                                e: UnsatisfiedLinkError
                            ) {
                            }


                        }

                        ActionType.ANNOTATION -> {
                            bottomSheetFragment?.dismiss()
                            if (!isPDFEncrypted) enableAnnotation()
                            else showToast(getString(R.string.protected_file_can_t_be_annotate))


                        }

                        ActionType.INK -> {
                            bottomSheetFragment?.dismiss()
                            if (!isPDFEncrypted) {
                                mViewDataBinding.mainToolbar.hide()
                                mViewDataBinding.fabEdit.hide()
//                                    mViewDataBinding.pdfView.hide()
//                                    mViewDataBinding.readerContainer.show()
//                                    readerView?.displayedViewIndex =
//                                        mViewDataBinding.pdfView.currentPage
                                readerView?.setMode(MuPDFReaderView.Mode.Drawing)
                                mViewDataBinding.applyToolbar.show()
                                updateInkSetting()
                            } else {
                                showToast(getString(R.string.protected_file_can_t_be_annotate))
                            }


//                            mViewDataBinding.mainToolbar.hideWithAnimation(object :
//                                Animation.AnimationListener {
//                                override fun onAnimationStart(animation: Animation) {}
//                                override fun onAnimationRepeat(animation: Animation) {}
//                                override fun onAnimationEnd(animation: Animation) {
//                                    mViewDataBinding.mainToolbar.hide()
//                                    readerView?.setMode(MuPDFReaderView.Mode.Drawing)
//                                    mViewDataBinding.applyToolbar.show()
//                                }
//                            })
                        }

                        ActionType.PDF_EXPORT -> {
                            bottomSheetFragment?.dismiss()
                            try {
                                if (core != null && core?.hasChanges() == true) {
                                    showSaveDialog { flag ->
                                        if (flag) {
                                            actionType = ActionType.PDF_EXPORT
                                            savePDF()
                                        } else {
                                            PdfToImages()

                                        }
                                    }
                                } else {
                                    PdfToImages()

                                }
                            } catch (e: UnsatisfiedLinkError) {
                            }


                        }

                        ActionType.IMAGES_EXPORT -> {
                            bottomSheetFragment?.dismiss()
                            loadingDialog.show()
                            lifecycleScope.launch(Dispatchers.IO) {
                                kotlin.runCatching {
                                    FileUtils.extractPDFImages(
                                        pdfViewViewModel.documentModel?.absolutePath!!,
                                        pdfViewViewModel.documentModel?.fileName!!,
                                        pdfViewViewModel.documentModel?.password!!
                                    ) { flag ->
                                        runOnUiThread {
                                            loadingDialog.dismiss()
                                            val root =
                                                Environment.getExternalStorageDirectory().toString()
                                            val myDir =
                                                root + "$PDF_PAGES_IMAGES${pdfViewViewModel.documentModel?.fileName}/"
                                            showCreateSheet(myDir) { flag ->
                                                if (flag) {
                                                    openFile(myDir)
                                                }
                                            }
                                            if (flag) showToast(getString(R.string.images_are_saved))
                                            else showToast(getString(R.string.no_images_found_to_export))
                                        }
                                    }
                                }.onFailure {
                                    loadingDialog.dismiss()
                                    showToast(getString(R.string.no_images_found_to_export))
                                }
                            }

                        }

                        ActionType.COMPRESSION -> {
                            bottomSheetFragment?.dismiss()
                            if (checkInternetConnection()) {
                                try {
                                    if (core != null && core?.hasChanges() == true) {
                                        showSaveDialog { flag ->
                                            if (flag) {
                                                actionType = ActionType.COMPRESSION
                                                savePDF()
                                            } else {
                                                compressPDF()
                                            }
                                        }
                                    } else {
                                        compressPDF()
                                    }
                                } catch (e: UnsatisfiedLinkError) {
                                }
                            } else showToast(getString(R.string.this_feature_required_internet))
                        }

                        ActionType.SCROLL -> {

                            destroyCore()
                            requestedOrientation = if (isPortrait()) {
                                hideAdViews()
                                ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                            } else {
                                loadNativeCall()
                                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                            }

                            bottomSheetFragment?.dismiss()

//                                try {
//                                    if (core?.countPages()!! == 1) {
//                                        mViewDataBinding.scrollHandle.hide()
//                                        mViewDataBinding.horizontalScroll.hide()
//                                    }
//                                } catch (e: UnsatisfiedLinkError) {
//                                }
//                                bottomSheetFragment?.dismiss()
//                                readerView?.setHorizontalScrolling(scroll)
//                                readerView?.requestLayout()
                        }

                        ActionType.VIEW_MODE -> {

                            handleViewerScrolling()
                            bottomSheetFragment?.dismiss()
//                                try {
//                                    if (core?.countPages()!! == 1) {
//                                        mViewDataBinding.scrollHandle.hide()
//                                        mViewDataBinding.horizontalScroll.hide()
//                                    }
//                                } catch (e: UnsatisfiedLinkError) {
//                                }
//                                bottomSheetFragment?.dismiss()
//                                readerView?.setHorizontalScrolling(scroll)
//                                readerView?.requestLayout()
                        }

                        ActionType.SHARE -> {
                            bottomSheetFragment?.dismiss()

                            try {
                                if (core != null && core?.hasChanges() == true) {

                                    showSaveDialog { flag ->
                                        if (flag) {
                                            actionType = ActionType.SHARE
                                            savePDF()
                                        } else {
                                            FileUtils.shareFile(
                                                pdfViewViewModel.documentModel?.absolutePath!!, this
                                            )
                                        }
                                    }

                                } else {
                                    FileUtils.shareFile(documentFile?.absolutePath!!, this)
                                }
                            } catch (e: UnsatisfiedLinkError) {

                            }


                        }

                        ActionType.READING_MODE -> {

                            isReadingMode = true
                            keepScreenOn(true)
                            hideOrShow()
                            mViewDataBinding.imgCloseReadMode.show()
                            bottomSheetFragment?.dismiss()

                        }

                        ActionType.DARK_MODE -> {

                            bottomSheetFragment?.dismiss()
                            val data = !storage.pdfNightMode
                            storage.pdfNightMode = data
                            if (data) {
                                storage.backgroundColor = ColorType.BLACK.name
                            } else {
                                storage.backgroundColor = ColorType.WHITE.name
                            }
//                                    pdfAdapter?.setDarkMode(storage.pdfNightMode)
                            destroyCore()
                            loadingDialog.show()
                            Handler(mainLooper!!).postDelayed({
                                loadFile()
                            }, 500)
//                                    readerView?.invalidate()


                        }

                        ActionType.READING_SETTING -> {
                            bottomSheetFragment?.dismiss()
                            val settingBinding = ViewerColorSheetLayoutBinding.inflate(
                                LayoutInflater.from(this), null, false
                            )
                            MyBottomSheet.getInstance(this)
                                ?.setContentView(settingBinding.root, false)?.showDialog()

                            settingBinding.apply {
                                imgSheetClose.setOnClickListener {
                                    MyBottomSheet.getInstance(this@ViewerActivity)?.dismissDialog()
                                }

                                brightnessSeekBar.max = 230
                                brightnessSeekBar.progress = abs(storage.brightness)
                                brightnessSeekBar.setOnSeekBarChangeListener(object :
                                    SeekBar.OnSeekBarChangeListener {
                                    override fun onProgressChanged(
                                        seekBar: SeekBar?, progress: Int, fromUser: Boolean
                                    ) {
                                        if (fromUser) setBrightness(progress + 25)
                                    }

                                    override fun onStartTrackingTouch(seekBar: SeekBar?) {
                                        val brightness = abs(storage.brightness)
                                        storage.brightness = brightness
                                        setBrightness(brightness)
                                        storage.isBrightnessAuto = false
                                        switchAutoBright.isChecked = false


                                    }

                                    override fun onStopTrackingTouch(seekBar: SeekBar?) {
                                        val bright: Int = seekBar?.progress!!
                                        storage.brightness = bright + 25
                                    }
                                })

                                switchAutoBright.isChecked = storage.isBrightnessAuto
                                switchAlways.isChecked = storage.isScreenOnEnabled

                                switchAutoBright.setOnCheckedChangeListener { _, isChecked ->
                                    if (isChecked) {
                                        storage.isBrightnessAuto = true
                                        setDeviceBrightness()
                                    } else {
                                        storage.isBrightnessAuto = false
                                        val brightness = abs(storage.brightness)
                                        setBrightness(brightness)
                                    }
                                }

                                switchAlways.setOnCheckedChangeListener { _, isChecked ->
                                    storage.isScreenOnEnabled = isChecked
                                    keepScreenOn(isChecked)
                                }
                            }


                            /*  settingBinding.imgColorWhite.setOnClickListener {
                                  if (storage.backgroundColor != ColorType.WHITE.name) {
                                      MyBottomSheet.getInstance(activity)?.dismissDialog()
                                      storage.backgroundColor = ColorType.WHITE.name
                                      destroyCore()
                                      loadingDialog.show()
                                      Handler(activity?.mainLooper!!).postDelayed({
                                          loadFile()
                                      }, 500)
                                  }

                              }

                              settingBinding.imgColorGrey.setOnClickListener {
                                  if (storage.backgroundColor != ColorType.GREY.name) {
                                      MyBottomSheet.getInstance(activity)?.dismissDialog()
                                      storage.backgroundColor = ColorType.GREY.name
                                      destroyCore()
                                      loadingDialog.show()
                                      Handler(activity?.mainLooper!!).postDelayed({
                                          loadFile()
                                      }, 500)
                                  }
                              }

                              settingBinding.imgColorYellow.setOnClickListener {
                                  if (storage.backgroundColor != ColorType.YELLOW.name) {
                                      MyBottomSheet.getInstance(activity)?.dismissDialog()
                                      storage.backgroundColor = ColorType.YELLOW.name
                                      destroyCore()
                                      loadingDialog.show()
                                      Handler(activity?.mainLooper!!).postDelayed({
                                          loadFile()
                                      }, 500)
                                  }
                              }

                              settingBinding.imgColorBlack.setOnClickListener {
                                  if (storage.backgroundColor != ColorType.BLACK.name) {
                                      MyBottomSheet.getInstance(activity)?.dismissDialog()
                                      storage.backgroundColor = ColorType.BLACK.name
                                      destroyCore()
                                      loadingDialog.show()
                                      Handler(activity?.mainLooper!!).postDelayed({
                                          loadFile()
                                      }, 500)
                                  }
                              }*/


                        }

                        else -> {
                            bottomSheetFragment?.dismiss()

                            try {
                                if (core != null && core?.hasChanges() == true) {
                                    showSaveDialog { flag ->
                                        if (flag) {
                                            actionType = ActionType.SORT
                                            savePDF()
                                        } else {

                                            startAppActivity<ToolsActivity>(
                                                Pair(
                                                    "action", ActionType.SORT.name
                                                ), Pair("doc", pdfViewViewModel.documentModel)
                                            )

                                        }
                                    }
                                } else {

                                    startAppActivity<ToolsActivity>(
                                        Pair(
                                            "action", ActionType.SORT.name
                                        ), Pair("doc", pdfViewViewModel.documentModel)
                                    )


                                }
                            } catch (e: UnsatisfiedLinkError) {
                            }

                        }
                    }
                }


                bottomSheetFragment?.show(supportFragmentManager, "BottomSheetDialog")

            }

            mViewDataBinding.imgEditHigh.setOnClickListener {
                if (annotMode != Annotation.Type.HIGHLIGHT) updateAnnotViews(Annotation.Type.HIGHLIGHT)
            }

            mViewDataBinding.imgEditUnder.setOnClickListener {
                if (annotMode != Annotation.Type.UNDERLINE) updateAnnotViews(Annotation.Type.UNDERLINE)
            }

            mViewDataBinding.imgEditStrik.setOnClickListener {
                if (annotMode != Annotation.Type.STRIKEOUT) updateAnnotViews(
                    Annotation.Type.STRIKEOUT
                )

            }

            mViewDataBinding.imgEditCopy.setOnClickListener {
                val pageView = readerView?.displayedView as MuPDFView?
                val result = pageView?.copySelection()
                if (result == true) showToast(getString(R.string.text_copied))
            }

            mViewDataBinding.imgEditDone.setOnClickListener {
                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                mViewDataBinding.applyToolbar.hide()
                disableAnnotation()

            }

            mViewDataBinding.imgEditInk.setOnClickListener {
                if (!isPDFEncrypted) {
                    if (annotMode != Annotation.Type.INK) updateAnnotViews(Annotation.Type.INK)

                } else {
                    showToast(getString(R.string.protected_file_can_t_be_annotate))
                }
            }

            mViewDataBinding.btnSearch.setOnClickListener {
                enableSearch()
            }

            mViewDataBinding.searchBack.setOnClickListener {

                disableSearch()
            }

            mViewDataBinding.edtSearch.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?, start: Int, count: Int, after: Int
                ) {

                }

                override fun onTextChanged(
                    s: CharSequence?, start: Int, before: Int, count: Int
                ) {
                    if (s != null && s.isNotEmpty()) {
                        mViewDataBinding.imgClearText.show()
                    } else {
                        mViewDataBinding.imgClearText.invisible()
                    }

                }

                override fun afterTextChanged(s: Editable?) {

                }
            })

            mViewDataBinding.edtSearch.setOnEditorActionListener { _, i, _ ->
                if (i == EditorInfo.IME_ACTION_SEARCH) {

                    val text = mViewDataBinding.edtSearch.text
                    mViewDataBinding.edtSearch.hideKeyboard()
                    if (text?.isNotEmpty() == true) {

                        search(0, text.toString())
                        true
                    } else false


                } else false


            }

            mViewDataBinding.imgClearText.setOnClickListener {
                mViewDataBinding.edtSearch.setText("")
                SearchTaskResult.set(null)
                readerView?.resetupChildren()
            }

            mViewDataBinding.btnApplyBack.setOnClickListener {
                when {
                    mViewDataBinding.annotationLayout.isVisible -> {

                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                        val pageView = readerView?.displayedView as MuPDFView?
                        pageView?.deselectText()
                        pageView?.cancelDraw()
                        mViewDataBinding.applyToolbar.hide()
                        disableAnnotation()
                    }

                    else -> {
                        mViewDataBinding.mainToolbar.show()
                        mViewDataBinding.fabEdit.show()
                        mViewDataBinding.applyToolbar.hide()
                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                        val pageView = readerView?.displayedView as MuPDFView?
                        pageView?.deselectText()
                        pageView?.cancelDraw()
                    }
                }


//                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation) {
//                        mViewDataBinding.mainToolbar.show()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation) {}
//                    override fun onAnimationEnd(animation: Animation) {
//                        mViewDataBinding.applyToolbar.hide()
//                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                        val pageView = readerView?.displayedView as MuPDFView
//                        pageView.deselectText()
//                        pageView.cancelDraw()
//                    }
//                })
            }

            mViewDataBinding.btnApply.setOnClickListener {

                when {
                    mViewDataBinding.annotationLayout.isVisible -> {
                        val pageView = readerView?.displayedView as MuPDFView?
                        pageView?.saveDraw()
                    }

                    else -> {
                        mViewDataBinding.mainToolbar.show()
                        mViewDataBinding.fabEdit.show()
                        mViewDataBinding.applyToolbar.hide()
                        val pageView = readerView?.displayedView as MuPDFView?
                        pageView?.saveDraw()
                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                    }
                }

            }

            mViewDataBinding.fabEdit.setOnClickListener {
                enableAnnotation()
            }

            mViewDataBinding.btnAnnotBack.setOnClickListener {
                disableAnnotation()
            }

            mViewDataBinding.btnAnnotDone.setOnClickListener {

                addAnnotation(annotMode)
            }

            mViewDataBinding.imgAnnotColor.setOnClickListener {
                when (annotMode) {
                    Annotation.Type.HIGHLIGHT -> {
                        colorSheetFragment = AnnotationColorSheet.getInstance(
                            Annotation.Type.HIGHLIGHT, storage.highlightColor
                        ) {
                            colorSheetFragment?.dismiss()
                            storage.highlightColor = it
                            mViewDataBinding.imgAnnotColor.setColorFilter(
                                ColorPalette.getHex(
                                    it, Annotation.Type.HIGHLIGHT
                                )
                            )
                            core?.setHighlightColor()

                            addAnnotation(annotMode)
                        }



                        colorSheetFragment?.show(supportFragmentManager, "Color Fragment")
                    }

                    Annotation.Type.UNDERLINE -> {
                        colorSheetFragment = AnnotationColorSheet.getInstance(
                            Annotation.Type.UNDERLINE, storage.underlineColor
                        ) {
                            colorSheetFragment?.dismiss()
                            storage.underlineColor = it
//                                if (it == 0) {
//                                    mViewDataBinding.imgAnnotColor.setColorFilter(
//                                        Color.argb(
//                                            1,
//                                            0, 0, 1
//                                        )
//                                    )
//                                } else {
                            mViewDataBinding.imgAnnotColor.setColorFilter(
                                ColorPalette.getHex(
                                    it, Annotation.Type.UNDERLINE
                                )
                            )

//                                }
                            core?.setUnderlineColor()
                            addAnnotation(annotMode)
                        }
                        colorSheetFragment?.show(supportFragmentManager, "Color Fragment")
                    }

                    Annotation.Type.STRIKEOUT -> {
                        colorSheetFragment = AnnotationColorSheet.getInstance(
                            Annotation.Type.STRIKEOUT, storage.strikeoutColor
                        ) {
                            colorSheetFragment?.dismiss()
                            storage.strikeoutColor = it
//                                if (it == 0) {
//                                    mViewDataBinding.imgAnnotColor.setColorFilter(
//                                        Color.argb(
//                                            1,
//                                            1, 0, 0
//                                        )
//                                    )
//                                } else {
                            mViewDataBinding.imgAnnotColor.setColorFilter(
                                ColorPalette.getHex(
                                    it, Annotation.Type.STRIKEOUT
                                )
                            )

//                                }
                            core?.setStrikeoutColor()
                            addAnnotation(annotMode)
                        }
                        colorSheetFragment?.show(supportFragmentManager, "Color Fragment")
                    }

                    else -> {}
                }
            }

            mViewDataBinding.imgInkColor.setOnClickListener {


                inkSettingSheetFragment =
                    InkSettingFragment.getInstance(storage.inkThickness, storage.inkColor, thick = {
                        storage.inkThickness = it
                        core?.setInkThickness()
                        updateInkSetting()
                        inkSettingSheetFragment?.dismiss()
                    }) {
                        inkSettingSheetFragment?.dismiss()
                        storage.inkColor = it
                        updateInkSetting()
                    }

                inkSettingSheetFragment?.show(supportFragmentManager, "Ink Color Fragment")

            }

            mViewDataBinding.btnSavePDF.setOnClickListener {
                try {
                    if (core != null && core?.hasChanges() == true) {

                        val alert = mAlertBuilder?.create()
                        alert?.setTitle(getString(R.string.save_changing))
                        alert?.cancel()
                        alert?.setMessage(getString(R.string.document_has_changes_save_them_))
                        alert?.setButton(
                            AlertDialog.BUTTON_POSITIVE,
                            getString(R.string.yes),

                            ) { _, _ ->
                            alert?.dismiss()
                            saveAsPDF()
                        }
                        alert?.setButton(
                            AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
                        ) { _, _ ->
                            alert.dismiss()

                        }
                        alert?.show()
                    }
                } catch (e: UnsatisfiedLinkError) {
                }
            }


            if (FileUtils.isPDFEncrypted(pdfViewViewModel.documentModel?.absolutePath!!)) {
                isPDFEncrypted = true
                mViewDataBinding.fabEdit.hide()
            }

            if (isEdit) {
                if (!isPDFEncrypted) enableAnnotation()
                else {
                    mViewDataBinding.fabEdit.hide()
                }
            }

            keepScreenOn(storage.isScreenOnEnabled)


            // Set up the page slider

            try {
                val smax = max(core?.countPages()!! - 1, 1)
                mPageSliderRes = (10 + smax - 1) / smax * 2
                mViewDataBinding.mySeekBar.max = (core?.countPages()!! - 1) * mPageSliderRes
                mViewDataBinding.horizontalScroll.max = (core?.countPages()!! - 1) * mPageSliderRes

            } catch (e: UnsatisfiedLinkError) {
            }


            mViewDataBinding.mySeekBar.setOnSeekBarChangeListener(object :
                SeekBar.OnSeekBarChangeListener {
                override fun onStopTrackingTouch(seekBar: SeekBar) {
                    readerView?.displayedViewIndex =
                        (seekBar.progress + mPageSliderRes / 2) / mPageSliderRes
                    mViewDataBinding.txtAnnotationNot.hide()
                }

                override fun onStartTrackingTouch(seekBar: SeekBar) {
                    mViewDataBinding.txtAnnotationNot.show()
                }

                override fun onProgressChanged(
                    seekBar: SeekBar, progress: Int, fromUser: Boolean
                ) {
                    val index = (progress + mPageSliderRes / 2) / mPageSliderRes
                    updatePageNumView(index)
                    seekBar.thumb = getVerticalThumb(index + 1)

                }
            })

            mViewDataBinding.horizontalScroll.setOnSeekBarChangeListener(object :
                SeekBar.OnSeekBarChangeListener {
                override fun onStopTrackingTouch(seekBar: SeekBar) {
                    readerView?.displayedViewIndex =
                        (seekBar.progress + mPageSliderRes / 2) / mPageSliderRes
                    mViewDataBinding.txtAnnotationNot.hide()
                }

                override fun onStartTrackingTouch(seekBar: SeekBar) {
                    mViewDataBinding.txtAnnotationNot.show()
                }

                override fun onProgressChanged(
                    seekBar: SeekBar, progress: Int, fromUser: Boolean
                ) {
                    val index = (progress + mPageSliderRes / 2) / mPageSliderRes
                    updatePageNumView(index)
                    seekBar.thumb = getHorizontalThumb(index + 1)

                }
            })

            // Update page number text and slider
            val index: Int = readerView?.displayedViewIndex!!
            mViewDataBinding.mySeekBar.progress = index * mPageSliderRes
            mViewDataBinding.horizontalScroll.progress = index * mPageSliderRes
            mViewDataBinding.horizontalScroll.thumb = getHorizontalThumb(index + 1)
            updatePageNumView(index)
            try {

                if (core != null && core?.countPages() == 1) {
                    mViewDataBinding.scrollHandle.hide()
                    mViewDataBinding.horizontalScroll.hide()
                }
            } catch (e: UnsatisfiedLinkError) {
            }
//            if (loadingDialog.isShowing)
//                loadingDialog.dismiss()

        }

    }

    private fun hideAdViews() {
        mViewDataBinding.apply {
            nativeAdLayout.nativeAdView.hide()
            shimmer.shimmer.hide()
            bannerView.adViewContainer.hide()

        }
    }

    private fun openFile(path: String): MuPDFCore? {

        try {

            core = MuPDFCore(this, path)
            Log.d("TAG", "openFile: $core")
            OutlineActivityData.set(null)
        } catch (e: UnsatisfiedLinkError) {
            e.printStackTrace()
            sendFirebaseLog(
                "unsatisfied_link_error", "This error because of library issue"
            )
            return null
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return null
        }
        return core
    }

    private fun handleViewerScrolling() {

        if (isPortrait()) {

            when (storage.isViewContinues()) {
                true -> {
                    readerView?.setHorizontalScrolling(false)
                    readerView?.setContinuousScrolling(true)
                    mViewDataBinding.scrollHandle.show()
                    mViewDataBinding.horizontalScroll.hide()
                }

                else -> {
                    readerView?.setHorizontalScrolling(true)
                    readerView?.setContinuousScrolling(false)
                    mViewDataBinding.scrollHandle.hide()
                    mViewDataBinding.horizontalScroll.show()
                }
            }
        } else {
            when (storage.isViewContinues()) {
                true -> {
                    readerView?.setHorizontalScrolling(false)
                    readerView?.setContinuousScrolling(true)
                    mViewDataBinding.scrollHandle.show()
                    mViewDataBinding.horizontalScroll.hide()
                }

                else -> {
                    readerView?.setHorizontalScrolling(true)
                    readerView?.setContinuousScrolling(false)
                    mViewDataBinding.scrollHandle.hide()
                    mViewDataBinding.horizontalScroll.show()
                }
            }
        }
    }

    private fun compressPDF() {
        loadingDialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            pdfViewViewModel.compressFile(File(
                pdfViewViewModel.documentModel?.absolutePath!!
            ), type = FileType.COMPRESS, progress = object : Progress {
                override fun progress(progress: Int) {

                }

                override fun fail() {
                    runOnUiThread {
                        loadingDialog.dismiss()
                        showToast(getString(R.string.failed_to_compress))
                    }
                }

                override fun currentStatus(status: String) {

                }

                override fun downloadingSuccess(file: File) {
                    runOnUiThread {
                        loadingDialog.dismiss()
                        showToast(getString(R.string.compress_successfully))
                    }
                }
            })

        }
    }

    private fun updateInkSetting() {
        val color = storage.inkColor
        val thickness = storage.inkThickness
        mViewDataBinding.imgInkColor.setColorFilter(
            ColorPalette.getHex(
                color, Annotation.Type.INK
            )
        )
        core?.setInkColor()
        val pageView = readerView?.displayedView as MuPDFView?
        pageView?.setInkSetting(color, thickness * 2)
    }

    private fun requestPassword() {
        if (pdfViewViewModel.documentModel?.password?.isNotEmpty() == true) {
            val result = core?.authenticatePassword(pdfViewViewModel.documentModel?.password!!)
            if (result == false) {
                pdfViewViewModel.documentModel?.password = ""
                requestPassword()
            } else initViews()
        } else {
            DialogPdfPassword.getInstance { action, password ->
                when (action) {
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                        pdfViewViewModel.documentModel?.password = password
                        val result =
                            core?.authenticatePassword(pdfViewViewModel.documentModel?.password!!)
                        if (result == false) {
                            showToast(getString(R.string.txt_pdf_wrong_password))
                            requestPassword()
                        } else initViews()
                    }

                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                        showToast(getString(R.string.txt_pdf_null_password_warning))
                    }

                    ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
                        finish()
                    }

                    else -> {}
                }
            }.show(supportFragmentManager, "Password Dialog")
        }

    }

    override fun onTapMainDocArea() {
//        hideOrShow()
    }

    override fun onDocMotion() {
    }

    override fun onHit(item: Hit?) {
        when (item) {
            Hit.Annotation -> {
                val pageView = readerView?.displayedView as MuPDFView?
                val selectedAnnot = pageView?.selectedAnnotation
                if (selectedAnnot != null) {
                    val rect = selectedAnnot.toRect()
                    val x =
                        ((windowManager?.defaultDisplay?.width!! / 2) - ((rect.right + rect.left) / 2))
                    val y =
                        -(((windowManager?.defaultDisplay?.height!! / 2) - (((rect.bottom + rect.top) - rect.height()) / 2)))

                    showDeleteAnnotWindow(x, y)
                }
            }

            Hit.Nothing -> {
                if (!isReadingMode) hideOrShow()
            }

            else -> {

            }
        }
    }

    override fun onMoveToChild(i: Int) {
//        mViewDataBinding.pdfView.jumpTo(i)
        updatePageNumView(i)
        mViewDataBinding.mySeekBar.progress = i * mPageSliderRes
        mViewDataBinding.horizontalScroll.progress = i * mPageSliderRes
    }

    private fun hideOrShow() {
        when {
            mViewDataBinding.topLayout.isVisible -> {
                mViewDataBinding.topLayout.hide()

                mViewDataBinding.fabEdit.hide()
                val scrollMode = storage.getScrollDirection()

                if (scrollMode) {
//                    mViewDataBinding.scrollHandle.hide()
                    mViewDataBinding.horizontalScroll.hide()
                } else {

                    mViewDataBinding.scrollHandle.hide()
//                    mViewDataBinding.horizontalScroll.hide()
                }
//                mViewDataBinding.mainToolbar.hideWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation?) {
//
//                    }
//
//                    override fun onAnimationEnd(animation: Animation?) {
//                        mViewDataBinding.mainToolbar.hide()
//                        mViewDataBinding.fabEdit.hide()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation?) {
//
//                    }
//                })
            }

            else -> {
                if (!mViewDataBinding.applyToolbar.isVisible && !mViewDataBinding.searchToolbar.isVisible && !mViewDataBinding.annotationLayout.isVisible) {
                    mViewDataBinding.topLayout.show()
                    mViewDataBinding.fabEdit.hide()
                    val scrollMode = storage.getScrollDirection()

                    if (scrollMode) mViewDataBinding.horizontalScroll.show()
                    else mViewDataBinding.scrollHandle.show()

                    if (!isPDFEncrypted) mViewDataBinding.fabEdit.show()
                }

//                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                    Animation.AnimationListener {
//                    override fun onAnimationStart(animation: Animation?) {
//                        mViewDataBinding.mainToolbar.show()
//
//                    }
//
//                    override fun onAnimationEnd(animation: Animation?) {
//                        mViewDataBinding.fabEdit.show()
//                    }
//
//                    override fun onAnimationRepeat(animation: Animation?) {
//
//                    }
//                })
            }
        }
    }

    private fun search(direction: Int, text: String) {
        val displayPage = readerView?.displayedViewIndex
        val r = SearchTaskResult.get()
        val searchPage = r?.pageNumber ?: -1
        try {
            searchTask?.go(text, direction, displayPage!!, searchPage)
        } catch (e: UnsatisfiedLinkError) {
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun showErrorDialog(title: String, message: String = "") {

        val builder = AlertDialog.Builder(this)
        builder.setTitle(title)
        builder.setMessage(message)
        builder.setPositiveButton(getString(R.string.dismiss)) { _, _ ->

            dialog?.dismiss()
            finish()
        }

        dialog = builder.create()
        dialog?.show()


    }

    private fun addAnnotation(type: Annotation.Type) {
        if (type != Annotation.Type.UNKNOWN) {

            val pageView = readerView?.displayedView as MuPDFView?
            if (pageView?.selectedText != null) {
                pageView.markupSelection(type)
            }
        }

    }

    private fun showDeleteAnnotWindow(x: Int = 0, y: Int = 0) {

        popupWindow = PopupWindow(
            deleteAnnotBinding?.root,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow?.elevation = 20f
        deleteAnnotBinding?.root?.setOnClickListener {
            val pageView = readerView?.displayedView as MuPDFView?
            pageView?.deleteSelectedAnnotation()
            popupWindow?.dismiss()
        }

        popupWindow?.setOnDismissListener {
            val pageView = readerView?.displayedView as MuPDFView?

            pageView?.deselectAnnotation()
        }

        if (popupWindow?.isShowing == true) {
            popupWindow?.dismiss()
        }
        popupWindow?.showAtLocation(mViewDataBinding.parent, Gravity.CENTER, x, y)
    }

    private fun enableAnnotation() {
        mViewDataBinding.mainToolbar.hide()
        mViewDataBinding.fabEdit.hide()
        readerView?.setMode(MuPDFReaderView.Mode.Selecting)
        mViewDataBinding.annotationLayout.show()
        mViewDataBinding.annotToolbar.show()
        if (!storage.isFirstTime()) {
            mViewDataBinding.selectionAnim.show()
            mViewDataBinding.view.show()
            mViewDataBinding.selectionAnim.playAnimation()
            Handler(mainLooper!!).postDelayed({
                mViewDataBinding.selectionAnim.hide()
                mViewDataBinding.view.hide()
            }, 2500)
            storage.setFirstTime(true)
        }
        updateAnnotViews(annotMode)
        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            else -> {}
        }

//        mViewDataBinding.mainToolbar.hideWithAnimation(object :
//            Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {}
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.mainToolbar.hide()
//                mViewDataBinding.fabEdit.hide()
//                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
//                mViewDataBinding.annotationLayout.show()
//                mViewDataBinding.selectionAnim.show()
//                mViewDataBinding.view.show()
//                mViewDataBinding.selectionAnim.playAnimation()
//                Handler(requireActivity().mainLooper).postDelayed({
//                    mViewDataBinding.selectionAnim.hide()
//                    mViewDataBinding.view.hide()
//                }, 2500)
//            }
//        })
    }

    private fun disableAnnotation() {
        mViewDataBinding.mainToolbar.show()
        mViewDataBinding.fabEdit.show()
        val pageView = readerView?.displayedView as MuPDFView?
        pageView?.deselectText()
        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
        mViewDataBinding.annotationLayout.hide()
        mViewDataBinding.annotToolbar.hide()
        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = Annotation.Type.UNKNOWN
            }

            else -> {}
        }

//        mViewDataBinding.mainToolbar.showWithAnimation(object : Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {
//                mViewDataBinding.mainToolbar.show()
//                mViewDataBinding.fabEdit.show()
//            }
//
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                val pageView = readerView?.displayedView as MuPDFView
//                pageView.deselectText()
//                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                showToast("onAnimationEnd")
//                mViewDataBinding.annotationLayout.hide()
//            }
//        })
    }

    private fun enableSearch() {
        mViewDataBinding.mainToolbar.hide()
        mViewDataBinding.fabEdit.hide()
        mViewDataBinding.searchToolbar.show()
        mViewDataBinding.edtSearch.showKeyboard()
        mViewDataBinding.edtSearch.setText("")
        mViewDataBinding.edtSearch.requestFocus()
        mViewDataBinding.edtSearch.showKeyboard()
//        mViewDataBinding.mainToolbar.hideWithAnimation(object :
//            Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {}
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.mainToolbar.hide()
//                mViewDataBinding.searchToolbar.show()
//                mViewDataBinding.edtSearch.showKeyboard()
//                mViewDataBinding.edtSearch.setText("")
//                mViewDataBinding.edtSearch.requestFocus()
//
//            }
//        })
    }

    private fun disableSearch() {
        mViewDataBinding.mainToolbar.show()
        if (!isPDFEncrypted) mViewDataBinding.fabEdit.show()
        mViewDataBinding.searchToolbar.hide()
        mViewDataBinding.edtSearch.hideKeyboard()
        SearchTaskResult.set(null)
        readerView?.resetupChildren()

//        mViewDataBinding.mainToolbar.showWithAnimation(object : Animation.AnimationListener {
//            override fun onAnimationStart(animation: Animation) {
//                mViewDataBinding.mainToolbar.show()
//            }
//
//            override fun onAnimationRepeat(animation: Animation) {}
//            override fun onAnimationEnd(animation: Animation) {
//                mViewDataBinding.searchToolbar.hide()
//                mViewDataBinding.edtSearch.hideKeyboard()
//                SearchTaskResult.set(null)
//                readerView?.resetupChildren()
//            }
//        })
    }

    override fun onStart() {
        try {
            if (core != null) {
                core?.startAlerts()
                createAlertWaiter()
            }
        } catch (e: UnsatisfiedLinkError) {

        }

        super.onStart()
    }

    override fun onStop() {
        try {
            if (core != null) {
                destroyAlertWaiter()
                core?.stopAlerts()
            }
        } catch (e: UnsatisfiedLinkError) {
        }
        super.onStop()
    }

    override fun onPause() {
        super.onPause()
        try {
            adViewModel.pauseBannerAd(adsIds.bannerViewReader.adKey)
            adViewModel.pauseBannerAd(adsIds.fileViewIntentBannerAd.adKey)
            if (searchTask != null) searchTask?.stop()
        } catch (e: UnsatisfiedLinkError) {
        }
    }

    fun createAlertWaiter() {
        mAlertsActive = true
        if (mAlertTask != null) {
            mAlertTask?.cancel(true)
            mAlertTask = null
        }
        if (mAlertDialog != null) {
            mAlertDialog?.cancel()
            mAlertDialog = null
        }

        mAlertTask = object : AsyncTask<Void, Void, MuPDFAlert>() {
            override fun doInBackground(vararg params: Void?): MuPDFAlert? {
                return try {
                    if (!mAlertsActive) null else core?.waitForAlert()
                } catch (e: UnsatisfiedLinkError) {
                    null
                }

            }

            override fun onPostExecute(result: MuPDFAlert?) {
                super.onPostExecute(result)
                if (result == null) return
                val pressed = arrayOfNulls<MuPDFAlert.ButtonPressed>(3)
                for (i in 0..2) pressed[i] = MuPDFAlert.ButtonPressed.None
                val listener = DialogInterface.OnClickListener { _, which ->
                    mAlertDialog = null
                    if (mAlertsActive) {
                        var index = 0
                        when (which) {
                            AlertDialog.BUTTON1 -> index = 0
                            AlertDialog.BUTTON2 -> index = 1
                            AlertDialog.BUTTON3 -> index = 2
                        }
                        result.buttonPressed = pressed[index]
                        // Send the user's response to the core, so that it can
                        // continue processing.
                        try {
                            core?.replyToAlert(result)
                            createAlertWaiter()
                        } catch (e: UnsatisfiedLinkError) {
                        }

                        // Create another alert-waiter to pick up the next alert.

                    }
                }
                mAlertDialog = mAlertBuilder?.create()
                mAlertDialog?.setTitle(result.title)
                mAlertDialog?.setMessage(result.message)
                when (result.iconType) {
                    MuPDFAlert.IconType.Error -> {}
                    MuPDFAlert.IconType.Warning -> {}
                    MuPDFAlert.IconType.Question -> {}
                    MuPDFAlert.IconType.Status -> {}
                }
                when (result.buttonGroupType) {
                    MuPDFAlert.ButtonGroupType.OkCancel -> {
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON2, getString(R.string.cancel), listener
                        )
                        pressed[1] = MuPDFAlert.ButtonPressed.Cancel
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON1, getString(R.string.okay), listener
                        )
                        pressed[0] = MuPDFAlert.ButtonPressed.Ok
                    }

                    MuPDFAlert.ButtonGroupType.Ok -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.okay), listener
                        )
                        pressed[0] = MuPDFAlert.ButtonPressed.Ok
                    }

                    MuPDFAlert.ButtonGroupType.YesNoCancel -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON3, getString(R.string.cancel), listener
                        )
                        pressed[2] = MuPDFAlert.ButtonPressed.Cancel
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.yes), listener
                        )
                        pressed[0] = MuPDFAlert.ButtonPressed.Yes
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON2, getString(R.string.no), listener
                        )
                        pressed[1] = MuPDFAlert.ButtonPressed.No
                    }

                    MuPDFAlert.ButtonGroupType.YesNo -> {
                        mAlertDialog?.setButton(
                            android.app.AlertDialog.BUTTON1, getString(R.string.yes), listener
                        )
                        pressed[0] = MuPDFAlert.ButtonPressed.Yes
                        mAlertDialog?.setButton(
                            AlertDialog.BUTTON2, getString(R.string.no), listener
                        )
                        pressed[1] = MuPDFAlert.ButtonPressed.No
                    }
                }
                mAlertDialog?.setOnCancelListener {
                    mAlertDialog = null
                    if (mAlertsActive) {
                        result.buttonPressed = MuPDFAlert.ButtonPressed.None
                        try {
                            core?.replyToAlert(result)
                            createAlertWaiter()
                        } catch (e: UnsatisfiedLinkError) {
                        }

                    }
                }
                mAlertDialog?.show()
            }

        }
        mAlertTask?.executeOnExecutor(MyThreadExecutor())
    }

    private fun destroyAlertWaiter() {
        mAlertsActive = false
        if (mAlertDialog != null) {
            mAlertDialog?.cancel()
            mAlertDialog = null
        }
        if (mAlertTask != null) {
            mAlertTask?.cancel(true)
            mAlertTask = null
        }
    }

    private fun savePDF() {
        val saveAsyncTask: AsyncTask<Void, Void, Void?> = object : AsyncTask<Void, Void, Void?>() {

            override fun onPreExecute() {
                super.onPreExecute()
                loadingDialog.show()
            }

            override fun doInBackground(vararg params: Void?): Void? {
                try {
                    core?.save()
                } catch (e: UnsatisfiedLinkError) {
                }


                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                loadingDialog.dismiss()
                if (pdfViewViewModel.documentModel?.absolutePath != null) {
                    sendBroadcast(
                        Intent(
                            Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                                File(pdfViewViewModel.documentModel?.absolutePath!!)
                            )
                        )
                    )

                    if (actionType != null) {
                        when (actionType) {
                            ActionType.SHARE -> {
                                FileUtils.shareFile(
                                    pdfViewViewModel.documentModel?.absolutePath!!,
                                    this@ViewerActivity
                                )
                            }

                            ActionType.MERGE -> {
                                destroyCore()
                                startAppActivity<ToolsActivity>(
                                    Pair(
                                        "action", ActionType.MERGE.name
                                    ), Pair("doc", pdfViewViewModel.documentModel)
                                )

                            }

                            ActionType.SPLIT -> {
                                destroyCore()
                                startAppActivity<ToolsActivity>(
                                    Pair(
                                        "action", ActionType.SPLIT.name
                                    ), Pair("doc", pdfViewViewModel.documentModel)
                                )
                            }

                            ActionType.SORT -> {
                                destroyCore()
                                startAppActivity<ToolsActivity>(
                                    Pair(
                                        "action", ActionType.SORT.name
                                    ), Pair("doc", pdfViewViewModel.documentModel)
                                )


                            }

                            ActionType.PDF_EXPORT -> {
                                PdfToImages()

                            }

                            ActionType.COMPRESSION -> {
                                compressPDF()
                            }

                            else -> {
//                                when {
//                                    pdfViewViewModel.interstitialAd != null -> {
//                                        showAd()
//                                    }
//
//                                    !storage.isUserGaveRating() && dateDifferenceInDays(
//                                        storage.getRatingDate()
//                                    )!! >= 1L -> {
//                                        storage.setRatingDate()
//                                        RatingsDialog.getInstance(false) {
//                                            finish()
//
//                                        }.show(supportFragmentManager, "Rating Dialog")
//
//                                    }
//
//                                    else -> {
//                                        showAd()
//                                    }
//                                }
                                showInterBackPressAd()
                            }
                        }
                    } else {
                        when {
                            pdfViewViewModel.interstitialAd != null -> {
                                showAd()
                            }

                            !storage.isUserGaveRating() && dateDifferenceInDays(
                                storage.getRatingDate()
                            )!! >= 1L -> {
                                storage.setRatingDate()
                                RatingsDialog.getInstance(false) {
                                    finish()

                                }.show(supportFragmentManager, "Rating Dialog")

                            }

                            else -> {
                                showAd()

                            }
                        }
                    }
                } else {
                    showAd()

                }


            }
        }

        saveAsyncTask.execute()
    }

    private fun saveAsPDF() {
        val saveAsyncTask: AsyncTask<Void, Void, Void?> = object : AsyncTask<Void, Void, Void?>() {

            override fun onPreExecute() {
                super.onPreExecute()
                loadingDialog.show()
            }

            override fun doInBackground(vararg params: Void?): Void? {
                try {
                    core?.save()
                } catch (e: UnsatisfiedLinkError) {
                }


                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            File(pdfViewViewModel.documentModel?.absolutePath!!)
                        )
                    )
                )
                loadFile()
                loadingDialog.dismiss()
            }
        }

        saveAsyncTask.execute()
    }

    private fun updatePageNumView(index: Int) {
        try {
            if (core != null) {
                val pageNumber = "${index + 1}/${core?.countPages()}"
//                mViewDataBinding.txtPageNumber.text = pageNumber
                mViewDataBinding.txtAnnotationNot.text = pageNumber

            }

        } catch (e: UnsatisfiedLinkError) {
        }
    }

    private fun PdfToImages() {
        lifecycleScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                loadingDialog.show()
            }

            FileUtils.convertPDFToImages(
                pdfViewViewModel.documentModel?.absolutePath!!,
                pdfViewViewModel.documentModel?.fileName!!,
                pdfViewViewModel.documentModel?.password!!,
                this@ViewerActivity
            ) { flag ->
                runOnUiThread {
                    loadingDialog.dismiss()
                    if (flag) {
                        showToast(getString(R.string.images_are_saved))

                        val root = Environment.getExternalStorageDirectory().toString()
                        val myDir =
                            root + "$PDF_IMAGES/${pdfViewViewModel.documentModel?.fileName}/"

                        showCreateSheet(myDir) { flag ->
                            if (flag) {
                                openFile(myDir)
                            }
                        }

                    } else showToast(getString(R.string.failed_to_convert))
//                                        findNavController().popBackStack()
                }
            }

        }
    }

    private fun showSaveDialog(onClick: (Boolean) -> Unit) {
        val alert = mAlertBuilder?.create()
        alert?.setTitle(getString(R.string.save_changing))
        alert?.cancel()
        alert?.setMessage(getString(R.string.document_has_changes_save_them_))
        alert?.setButton(
            AlertDialog.BUTTON_POSITIVE,
            getString(R.string.yes),

            ) { _, _ ->
            onClick(true)
        }
        alert?.setButton(
            AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
        ) { _, _ ->
            onClick(false)
        }
        alert?.show()
    }

    private fun destroyCore() {
        try {
            readerView?.applyToChildren(object : ReaderView.ViewMapper() {
                override fun applyToView(view: View) {
                    (view as MuPDFView?)?.releaseBitmaps()
                }
            })

            if (core != null) core?.onDestroy()

            if (mAlertTask != null) {
                mAlertTask!!.cancel(true)
                mAlertTask = null
            }
            core = null
        } catch (e: UnsatisfiedLinkError) {
        }

    }

    fun getVerticalThumb(progress: Int): Drawable {
        verticalThumb?.tvProgress?.text = progress.toString()
        verticalThumb?.root?.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        val bitmap = Bitmap.createBitmap(
            verticalThumb?.root?.measuredWidth!!,
            verticalThumb?.root?.measuredHeight!!,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        verticalThumb?.root?.layout(
            0, 0, verticalThumb?.root?.measuredWidth!!, verticalThumb?.root?.measuredHeight!!
        )
        verticalThumb?.root?.draw(canvas)
        return BitmapDrawable(resources, bitmap)
    }

    private fun getHorizontalThumb(progress: Int): Drawable {
        horizontalThumb?.tvProgress?.text = progress.toString()
        horizontalThumb?.root?.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        val bitmap = Bitmap.createBitmap(
            horizontalThumb?.root?.measuredWidth!!,
            horizontalThumb?.root?.measuredHeight!!,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        horizontalThumb?.root?.layout(
            0, 0, horizontalThumb?.root?.measuredWidth!!, horizontalThumb?.root?.measuredHeight!!
        )
        horizontalThumb?.root?.draw(canvas)
        return BitmapDrawable(resources, bitmap)
    }

    private fun setBrightness(brightness: Int) {

        val var2: Window = window
        val var3 = var2.attributes
        var3.screenBrightness = brightness.toFloat() / 255.0f
        var2.attributes = var3

    }

    private fun updateAnnotViews(type: Annotation.Type) {

        when (type) {
            Annotation.Type.HIGHLIGHT -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = getString(R.string.highlight)
                val color = storage.highlightColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(.5f, 1f, 1f, 0f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.HIGHLIGHT
                    )
                )

                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.UNDERLINE -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = getString(R.string.underline)

                val color = storage.underlineColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(1f, 0f, 0f, 1f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.UNDERLINE
                    )
                )

                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            Annotation.Type.INK -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.deselectText()
                readerView?.setMode(MuPDFReaderView.Mode.Drawing)
                updateInkSetting()
                mViewDataBinding.annotToolbar.hide()
                mViewDataBinding.applyToolbar.show()
                val color = storage.inkColor
                mViewDataBinding.imgInkColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.HIGHLIGHT
                    )
                )

                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )

            }

            Annotation.Type.STRIKEOUT -> {
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.cancelDraw()
                readerView?.setMode(MuPDFReaderView.Mode.Selecting)
                mViewDataBinding.applyToolbar.hide()
                mViewDataBinding.annotToolbar.show()
                mViewDataBinding.txtAnnotType.text = getString(R.string.strike_out)

                val color = storage.strikeoutColor
//                if (color == 0)
//                    mViewDataBinding.imgAnnotColor.setColorFilter(Color.argb(1f, 1f, 0f, 0f))
//                else
                mViewDataBinding.imgAnnotColor.setColorFilter(
                    ColorPalette.getHex(
                        color, Annotation.Type.STRIKEOUT
                    )
                )

                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.grey_300, null
                    )
                )
            }

            else -> {}
        }

        when (annotMode) {
            Annotation.Type.HIGHLIGHT -> {
                mViewDataBinding.imgEditHigh.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.UNDERLINE -> {
                mViewDataBinding.imgEditUnder.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.INK -> {
                mViewDataBinding.imgEditInk.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            Annotation.Type.STRIKEOUT -> {
                mViewDataBinding.imgEditStrik.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources, R.color.transparent, null
                    )
                )
                annotMode = type
            }

            else -> {
                annotMode = type
            }
        }
    }

    private fun showAd() {
//        when {
//            !storage.preferences.getBoolean(
//                "pdf_splash", false
//            ) && pdfViewViewModel.interstitialAd != null -> {
//                val interstitialAd = pdfViewViewModel.interstitialAd
//                interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
//                    override fun onAdDismissedFullScreenContent() {
//                        super.onAdDismissedFullScreenContent()
//                        loadingDialog.dismiss()
//                        pdfViewViewModel.interstitialAd = null
//                        if (isTaskRoot) {
//                            startAppActivity<MainActivity>()
//                            finish()
//                        } else finish()
//
//                    }
//                }
//                interstitialAd?.show(this)
//            }
//
//            else -> {
//                storage.preferences.edit {
//                    putBoolean("pdf_splash", false)
//                }
//                if (isTaskRoot) {
//                    startAppActivity<MainActivity>()
//                    finish()
//                } else super.onBackPressed()
//            }
//
//        }


    }

    override fun onResume() {
        super.onResume()
        adViewModel.resumeBannerAd(adsIds.bannerViewReader.adKey)
        adViewModel.resumeBannerAd(adsIds.fileViewIntentBannerAd.adKey)
    }

    override fun onDestroy() {

        try {
            destroyCore()
            fromIntent = false
            adViewModel.destroyNativeAd(adsIds.nativeViewReader.adKey)
            adViewModel.destroyNativeAd(adsIds.fileViewIntentNative.adKey)
            adViewModel.destroyBannerAd(adsIds.bannerViewReader.adKey)
            adViewModel.destroyBannerAd(adsIds.fileViewIntentBannerAd.adKey)
            isFromAssetViewer = false
        } catch (e: UnsatisfiedLinkError) {
        }
//        mViewModel.core?.onDestroy()
//        mViewModel.core = null


        super.onDestroy()
    }

    override fun onBackPressed() {

        loadingDialog.dismiss()
        when {
            isReadingMode -> {
                mViewDataBinding.imgCloseReadMode.hide()
                isReadingMode = false
                keepScreenOn(false)
                hideOrShow()
            }

            mViewDataBinding.searchToolbar.isVisible -> {
                disableSearch()
            }

            mViewDataBinding.annotationLayout.isVisible -> {
                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.deselectText()
                pageView?.cancelDraw()
                mViewDataBinding.applyToolbar.hide()
                disableAnnotation()
            }

            mViewDataBinding.applyToolbar.isVisible -> {
                mViewDataBinding.mainToolbar.show()
                mViewDataBinding.applyToolbar.hide()
                readerView?.setMode(MuPDFReaderView.Mode.Viewing)
                val pageView = readerView?.displayedView as MuPDFView?
                pageView?.deselectText()
                pageView?.cancelDraw()

//                                mViewDataBinding.mainToolbar.showWithAnimation(object :
//                                    Animation.AnimationListener {
//                                    override fun onAnimationStart(animation: Animation) {
//                                        mViewDataBinding.mainToolbar.show()
//                                    }
//
//                                    override fun onAnimationRepeat(animation: Animation) {}
//                                    override fun onAnimationEnd(animation: Animation) {
//                                        mViewDataBinding.applyToolbar.hide()
//                                        readerView?.setMode(MuPDFReaderView.Mode.Viewing)
//                                        val pageView =
//                                            readerView?.displayedView as MuPDFView
//                                        pageView.deselectText()
//                                        pageView.cancelDraw()
//                                    }
//                                })


            }

            else -> {

                keepScreenOn(false)
                setDeviceBrightness()

                try {
                    if (core != null && core?.hasChanges() == true) {

                        val alert = mAlertBuilder?.create()
                        alert?.setTitle(getString(R.string.save_changing))
                        alert?.cancel()
                        alert?.setMessage(getString(R.string.document_has_changes_save_them_))
                        alert?.setButton(
                            AlertDialog.BUTTON_POSITIVE,
                            getString(R.string.yes),

                            ) { _, _ ->
                            alert.dismiss()
                            savePDF()
                        }
                        alert?.setButton(
                            AlertDialog.BUTTON_NEGATIVE, getString(R.string.no)
                        ) { _, _ ->
                            alert.dismiss()
//                            showAd()
                            showInterBackPressAd()


                        }
                        alert?.show()
                    } else {

//                        when {
////                            pdfViewViewModel.interstitialAd != null -> showAd()
//
//                            !storage.isUserGaveRating() && dateDifferenceInDays(
//                                storage.getRatingDate()
//                            )!! >= 1L -> {
//                                storage.setRatingDate()
//                                RatingsDialog.getInstance(false) {
//                                    finish()
//
//                                }.show(supportFragmentManager, "Rating Dialog")
//
//                            }
//
//                            else -> {
////                                showAd()
//                                showInterBackPressAd()
//                            }
//                        }
                        showInterBackPressAd()
                    }
                } catch (e: UnsatisfiedLinkError) {
                }
//                                else {
//                                    if (!shareSave) {
//                                        FileUtils.deleteFile(
//                                            documentFile?.absolutePath!!,
//                                            requireActivity()
//                                        ) {}
//                                        findNavController().popBackStack()
//                                    } else {
//                                        findNavController().popBackStack()
//                                    }
//
//                                }
            }
        }

    }

    private fun showInterBackPressAd() {
        adsIds.apply {
            adViewModel.apply {
                fileViewInterAdBackPress.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig =
                        getRemoteConfigModel().adConfigModel.fileViewInterAdBackPress.show
                }
                fileViewInterIntentBack.run {
                    canRequestAd = storage.isConsentDone
                    remoteConfig = getRemoteConfigModel().adConfigModel.fileViewInterIntentBack.show
                }
                loadingDialog.dismiss()
                if (fromIntent) {
                    val intent=  Intent(this@ViewerActivity, MainActivity::class.java)
//                    startAppActivity<MainActivity>()
//                    finish()
                    intent.flags = intent.flags
                    intent.addFlags(
                        Intent.FLAG_ACTIVITY_CLEAR_TOP or
                                Intent.FLAG_ACTIVITY_CLEAR_TASK or
                                Intent.FLAG_ACTIVITY_NEW_TASK or
                                Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
                    )
                    startActivity(intent)
                    finishAffinity()
                } else finish()
                if (fromIntent) {
                    if (AdUtility.isFromIntentInterOpenFailed) {
                        AdUtility.isFromIntentInterOpenFailed = false
                        showInterstitialAd(
                            activity = this@ViewerActivity,
                            adInfo = fileViewInterIntentBack
                        )
                    }
                } else {
                    if (AdUtility.isFromInterOpenFailed) {
                        AdUtility.isFromInterOpenFailed = false
                        showInterstitialAd(
                            activity = this@ViewerActivity,
                            adInfo = fileViewInterAdBackPress
                        )
                    }
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (isPortrait()) {
            logs("onConfigurationChanged->portrait")
        } else {
            logs("onConfigurationChanged->landscape")
        }
    }
}