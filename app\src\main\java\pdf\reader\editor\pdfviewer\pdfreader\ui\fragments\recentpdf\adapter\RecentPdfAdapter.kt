package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AllFileItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.local.database.entity.PdfStoreItems
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileMenuHelper
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.PDFFileActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.recentpdf.RecentPdfFragment.Companion.isFromRecentAsset


class RecentPdfAdapter(
    private val activity: FragmentActivity,
    isFromRecentAsset:Boolean = false,
    private var isSelection: Boolean = false,
    private val onPDFClick: (PDFFileActions, DocumentsModel?) -> Unit
) :
    RecyclerView.Adapter<RecentPdfAdapter.ViewHolder>() {

    private var recentFilesList: ArrayList<PdfStoreItems> = ArrayList()
    private var lastSelected = -1


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding: AllFileItemLayoutBinding =
            AllFileItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val pdfStoreItems = recentFilesList[position]

        holder.binding.apply {
            txtFileName.text = pdfStoreItems.pdfTitle
            txtFileSize.text = pdfStoreItems.pdfSize
            txtFileDate.text = pdfStoreItems.pdfAddedDate

        }



        if (lastSelected == position) {
            holder.binding.imgSelected.apply {
                show()
                setImageResource(R.drawable.ic_selected)
            }
        } else
            holder.binding.imgOptions.hide()

        if (isSelection)
            holder.binding.imgOptions.hide()
        else
            holder.binding.imgOptions.show()

        if (isFromRecentAsset){
            holder.binding.imgOptions.hide()
        }else{
            holder.binding.imgOptions.show()
        }


    }

    override fun getItemCount(): Int = recentFilesList.size

    fun setData(filesList: List<PdfStoreItems>) {
        recentFilesList.clear()
        recentFilesList.addAll(filesList)
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: AllFileItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val documentModel: DocumentsModel
            get() = recentFilesList[layoutPosition].toDocumentModel()

        init {

            binding.root.setOnClickListener {
                onPDFClick(PDFFileActions.SELECT, documentModel)

                if (isSelection) {
                    lastSelected = layoutPosition
                    notifyDataSetChanged()
                }
            }

            binding.imgOptions.setOnClickListener(object :
                FileMenuHelper.OnClickFileMenu(activity) {
                override val documentModel: DocumentsModel
                    get() = <EMAIL>

                override val isRecent: Boolean
                    get() = true

                override fun onComplete(action: PDFFileActions, file: DocumentsModel?) {
                    <EMAIL>(action, file)
                }


            })
        }
    }

}