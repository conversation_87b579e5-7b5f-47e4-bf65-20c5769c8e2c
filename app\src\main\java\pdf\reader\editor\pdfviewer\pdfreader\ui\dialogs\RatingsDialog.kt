package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.RatingDialogLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.browse
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide

class RatingsDialog : BaseDialog<RatingDialogLayoutBinding>() {

    companion object {
        //arguments
        private var isAppClose: Boolean = true
        private var isSetting: Boolean = false
        private var callback: ((Boolean) -> Unit)? = null
        fun getInstance(
            isAppClose: Boolean = true,
            isSetting: Boolean = false,
            callback: ((<PERSON><PERSON>an) -> Unit)
        ): RatingsDialog {
            this.isAppClose = isAppClose
            this.isSetting = isSetting
            this.callback = callback
            return RatingsDialog()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = !isAppClose
        binding.ratingBar.setOnRatingBarChangeListener { simpleRatingBar, rating, fromUser ->
            requireActivity().browse("https://play.google.com/store/apps/details?id=${requireActivity().packageName}")
            dismiss()
            callback?.invoke(isAppClose)

        }

        if (!isAppClose) {
            binding.btnCancel.hide()
            binding.btnExit.hide()
        }

        if (isSetting) {
            binding.btnCancel.hide()
            binding.btnExit.text = getString(R.string.no_thanks)
        }

        binding.btnCancel.setOnClickListener {
            dismiss()
            callback?.invoke((false))
        }
        binding.btnExit.setOnClickListener {
            dismiss()
            callback?.invoke((true))
        }


    }


    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        callback?.invoke((false))
    }

    override fun getViewBinding(
        inflater: LayoutInflater, container: ViewGroup?
    ): RatingDialogLayoutBinding =
        RatingDialogLayoutBinding.inflate(layoutInflater, container, false)
}