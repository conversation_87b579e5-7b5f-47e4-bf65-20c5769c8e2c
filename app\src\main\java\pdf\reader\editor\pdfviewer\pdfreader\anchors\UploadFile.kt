package pdf.reader.editor.pdfviewer.pdfreader.anchors

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName

data class UploadFile(
    @SerializedName("server_filename") val serverFileName: String?,
    @SerializedName("scanned")
    val scanned: String?

):Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(serverFileName)
        parcel.writeString(scanned)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<UploadFile> {
        override fun createFromParcel(parcel: Parcel): UploadFile {
            return UploadFile(parcel)
        }

        override fun newArray(size: Int): Array<UploadFile?> {
            return arrayOfNulls(size)
        }
    }
}