package pdf.reader.editor.pdfviewer.pdfreader.baseclasses

import android.app.Dialog
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.viewbinding.ViewBinding
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main.MainViewModel
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogClass
import java.text.SimpleDateFormat
import java.util.Date

abstract class BaseActivity<T : ViewBinding, V : BaseViewModel> : AppCompatActivity() {

    lateinit var mViewDataBinding: T
//    protected lateinit var mViewModel: V
    protected lateinit var loadingDialog: Dialog

    abstract fun getViewBinding(): T

    val mViewModel: MainViewModel by viewModels()
    /**
     * viewModel variable that will get value from activity which it will implement this
     * we will use this variable viewModel to bind with view through databinding
     */
    abstract val viewModel: Class<V>

    /**
     * layoutId variable to get layout value from activity which will implement this layoutId
     * we will use this layoutId for databinding
     */
//    @get:LayoutRes
//    abstract val layoutId: Int

    /**
     * bindingVariable which will bind with view
     */

//    abstract val bindingVariable: Int

    /**
     * eventbus
     */
//    lateinit var pdfEventBus: PdfEventBus

    //@Inject lateinit var dataStoreProvider: DataStoreProvider

    private fun applySystemBarsPadding(targetView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(targetView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mViewDataBinding = getViewBinding()
        if (Build.VERSION.SDK_INT >= 35) {
            enableEdgeToEdge()
        }
        setContentView(mViewDataBinding.root)
        if (Build.VERSION.SDK_INT >= 35) {
            applySystemBarsPadding(mViewDataBinding.root)
        }
        loadingDialog = DialogClass.loadingDialog(this)
//        mViewModel = ViewModelProviders.of(this)[viewModel]

    }

    open fun getLocalDate(): String {
        val sdf = SimpleDateFormat("dd/M/yyyy")
        return sdf.format(Date())
    }

    open fun getLocalTimeStamp(): String {
        val stamp = System.currentTimeMillis()
        return stamp.toString()
    }


}

