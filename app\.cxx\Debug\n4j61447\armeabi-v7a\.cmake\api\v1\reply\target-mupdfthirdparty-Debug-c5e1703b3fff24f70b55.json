{"archive": {}, "artifacts": [{"path": "libmupdfthirdparty.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 1, "file": 0, "line": 34, "parent": 0}, {"command": 2, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC"}], "defines": [{"backtrace": 2, "define": "FT2_BUILD_LIBRARY"}, {"backtrace": 2, "define": "FT_CONFIG_OPTIONS_H=\"slimftoptions.h\""}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg"}], "language": "C", "sourceIndexes": [0, 1, 2], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "mupdfthirdparty::@6890427a1f51a3e7e1df", "name": "mupdfthirdparty", "nameOnDisk": "libmupdfthirdparty.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/mujs/one.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/type1/type1.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}