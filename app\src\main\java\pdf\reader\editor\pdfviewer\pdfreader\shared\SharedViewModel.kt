package pdf.reader.editor.pdfviewer.pdfreader.shared

import androidx.lifecycle.MutableLiveData
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PdfPaths
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel

class SharedViewModel : BaseViewModel() {

    private val _pdfPath = MutableLiveData("")
    fun setPdfPath(path: String) {
        _pdfPath.value = path
    }
    fun getPdfPath() = _pdfPath.value


    private val _pdfModel = MutableLiveData<DocumentsModel>()
    fun setPdfModel(model: DocumentsModel) {
        _pdfModel.value = model
    }
    fun getPdfModel(): DocumentsModel = _pdfModel.value!!



//    private val _pdfPathList = ArrayList<String>()
//    private val _pdfPassword = MutableLiveData("")
//    fun setPdfPathListModel (pathList : MutableList<String>){
//        _pdfPathList.addAll(pathList)
//    }
//    fun flushPathList(){
//        _pdfPathList.clear()
//    }
//    fun setPdfPassword(pass: String){
//        _pdfPassword.value = pass
//    }
//    fun getPdfPathList() : ArrayList<String> = _pdfPathList
//    fun getPdfPassword()  = _pdfPassword.value!!


    private val _pdfDetails = MutableLiveData<PdfPaths>()
    fun setPdfDetailsModel(model: PdfPaths){
        _pdfDetails.value = model
    }
    fun getPdfDetailsModel(): PdfPaths = _pdfDetails.value!!

    private val _webURL = MutableLiveData("")
    fun setWebURL(url: String) {
        _webURL.value = url
    }
    fun getWebURL() = _webURL.value
}