package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.newfilemenu

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentNewFileManuBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive

class NewFileMenuFragment : BaseFragment<FragmentNewFileManuBinding, NewFileManuViewModel>() {

    override val viewModel: Class<NewFileManuViewModel>
        get() = NewFileManuViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentNewFileManuBinding = if (mViewModel.view == null)
        FragmentNewFileManuBinding.inflate(inflater, container, false)
    else
        FragmentNewFileManuBinding.bind(mViewModel.view!!)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mViewDataBinding.btnBack.setOnClickListener {
            isAlive {

            findNavController().popBackStack()
            }
        }

        mViewDataBinding.newPdfLayout.setOnClickListener {
            navigate(NewFileMenuFragmentDirections.actionNewFileMenuFragmentToCreatePdfFragment())
        }

        mViewDataBinding.wordPdfLayout.setOnClickListener {
            navigate(NewFileMenuFragmentDirections.actionNewFileMenuFragmentToWordConverterFragment())

        }
        mViewDataBinding.imagePdfLayout.setOnClickListener { navigate(NewFileMenuFragmentDirections.actionNewFileMenuFragmentToImageToPDFFragment()) }
        mViewDataBinding.scanDocPdfLayout.setOnClickListener { showToast(getString(R.string.txt_comming_soon)) }
        mViewDataBinding.linkPdfLayout.setOnClickListener { navigate(NewFileMenuFragmentDirections.actionNewFileMenuFragmentToWebLinkFragment()) }

    }


}