package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.viewpagerbottomsheet.edit

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.artifex.mupdfdemo.ActionType
import dagger.hilt.android.AndroidEntryPoint
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentEditBinding

@AndroidEntryPoint
class EditFragment(private val navigation: (ActionType) -> Unit) :
    BaseFragment<FragmentEditBinding, EditViewModel>() {
    override val viewModel: Class<EditViewModel>
        get() = EditViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentEditBinding = FragmentEditBinding.inflate(inflater, container, false)

    private var documentModel: DocumentsModel? = null


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        documentModel = arguments?.getParcelable("documentModel")

        initViews()
    }

    private fun initViews() {

        mViewDataBinding.apply {

            btnPDFSheetEdtExtImg.setOnClickListener {
                navigation(ActionType.IMAGES_EXPORT)
            }

//            btnPDFSheetEdtExFoc.setOnClickListener { }

            btnPDFSheetEdtExpPDF.setOnClickListener {
                navigation(ActionType.PDF_EXPORT)

            }

            btnPDFSheetEdtFCom.setOnClickListener {
                navigation(ActionType.COMPRESSION)
            }

            btnPDFSheetEdtPAdj.setOnClickListener {
                navigation(ActionType.SORT)
            }

            btnPDFSheetEdtExtPa.setOnClickListener {
                navigation(ActionType.SPLIT)
            }

            btnPDFSheetEdtMerge.setOnClickListener {
                navigation(ActionType.MERGE)

            }

            btnPDFSheetEdtAnnot.setOnClickListener {
                navigation(ActionType.ANNOTATION)
            }

            btnPDFSheetEdtInk.setOnClickListener {
                navigation(ActionType.INK)
            }
        }
    }


}