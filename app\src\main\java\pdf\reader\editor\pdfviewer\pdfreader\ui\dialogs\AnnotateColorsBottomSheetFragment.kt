package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentAnnotateColorsBottomSheetBinding
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.AnnotColors
import kotlin.properties.Delegates


class AnnotateColorsBottomSheetFragment : BottomSheetDialogFragment() {

    private var binding: FragmentAnnotateColorsBottomSheetBinding? = null

    companion object {
        private var callback: ((AnnotColors) -> Unit)? = null
        private var selectedTextColor by Delegates.notNull<Int>()
        private var selectedHColor by Delegates.notNull<Int>()
        private var isDarkTheme by Delegates.notNull<Boolean>()

        fun getInstance(
            selectedTextColor : Int = 0,
            selectedHColor : Int = 0,
            isDarkTheme : Boolean = false,
            callback: ((AnnotColors) -> Unit)
        ): AnnotateColorsBottomSheetFragment {
            this.callback = callback
            this.selectedTextColor = selectedTextColor
            this.selectedHColor = selectedHColor
            this.isDarkTheme = isDarkTheme
            return AnnotateColorsBottomSheetFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        binding = FragmentAnnotateColorsBottomSheetBinding.inflate(inflater,container,false)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        try {
            view.viewTreeObserver.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val dialog = dialog as BottomSheetDialog
                    val bottomSheet =
                        dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                    val behavior = BottomSheetBehavior.from(bottomSheet!!)
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
            })
            callback?.let { bindClickListeners(it) }
            applypreviousColorSettings(selectedTextColor, selectedHColor)
        }catch (ex: Exception){

        }
    }

    private fun applypreviousColorSettings(colorCode: Int, hColorCode: Int){
        when(colorCode){
            0->{binding?.ivBlack?.setImageDrawable(resources.getDrawable(R.drawable.ic_blackfilled))}
            1->{binding?.ivRed?.setImageDrawable(resources.getDrawable(R.drawable.ic_redfilled))}
            2->{binding?.ivYellow?.setImageDrawable(resources.getDrawable(R.drawable.ic_yellowfilled))}
            3->{binding?.ivGreen?.setImageDrawable(resources.getDrawable(R.drawable.ic_greenfilled))}
            4->{binding?.ivBlue?.setImageDrawable(resources.getDrawable(R.drawable.ic_bluefilled))}
        }

        when(hColorCode){
            0->{binding?.ivHBlack?.setImageDrawable(resources.getDrawable(R.drawable.ic_blackfilled))}
            1->{binding?.ivHRed?.setImageDrawable(resources.getDrawable(R.drawable.ic_redfilled))}
            2->{binding?.ivHYellow?.setImageDrawable(resources.getDrawable(R.drawable.ic_yellowfilled))}
            3->{binding?.ivHGreen?.setImageDrawable(resources.getDrawable(R.drawable.ic_greenfilled))}
            4->{binding?.ivHBlue?.setImageDrawable(resources.getDrawable(R.drawable.ic_bluefilled))}
        }


    }

    private fun applypreviousHighlightColorSettings(colorCode: Int){

    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun bindClickListeners(callback: (AnnotColors) -> Unit) {
        //TEXT COLOR
        binding?.ivColorBlack?.setOnClickListener {
            binding?. ivBlack?.setImageDrawable(resources.getDrawable(R.drawable.ic_blackfilled))
            callback(AnnotColors.colorBlack)
        }
        binding?.ivColorRed?.setOnClickListener {
            binding?. ivRed?.setImageDrawable(resources.getDrawable(R.drawable.ic_redfilled))
            callback(AnnotColors.colorRed)
        }
        binding?.ivColorYellow?.setOnClickListener {
            binding?.ivYellow?.setImageDrawable(resources.getDrawable(R.drawable.ic_yellowfilled))
            callback(AnnotColors.colorYellow)
        }
        binding?.ivColorGreen?.setOnClickListener {
            binding?.ivGreen?.setImageDrawable(resources.getDrawable(R.drawable.ic_greenfilled))
            callback(AnnotColors.colorGreen)
        }
        binding?.ivColorBlue?.setOnClickListener {
            binding?.ivBlue?.setImageDrawable(resources.getDrawable(R.drawable.ic_bluefilled))
            callback(AnnotColors.colorBlue)
        }

        //HIGHLIGHT COLOR
        binding?.ivHighlightColorBlack?.setOnClickListener {
            binding?.ivHBlack?.setImageDrawable(resources.getDrawable(R.drawable.ic_blackfilled))
            callback(AnnotColors.colorBlackH)
        }
        binding?.ivHighlightColorRed?.setOnClickListener {
            binding?.ivHRed?.setImageDrawable(resources.getDrawable(R.drawable.ic_redfilled))
            callback(AnnotColors.colorRedH)
        }
        binding?.ivCHighlightolorYellow?.setOnClickListener {
            binding?.ivHYellow?.setImageDrawable(resources.getDrawable(R.drawable.ic_yellowfilled))
            callback(AnnotColors.colorYellowH)
        }
        binding?.ivHighlightColorGreen?.setOnClickListener {
            binding?.ivHGreen?.setImageDrawable(resources.getDrawable(R.drawable.ic_greenfilled))
            callback(AnnotColors.colorGreenH)
        }
        binding?.ivColorHighlightBlue?.setOnClickListener {
            binding?.ivHBlue?.setImageDrawable(resources.getDrawable(R.drawable.ic_bluefilled))
            callback(AnnotColors.colorBlueH)
        }
    }

}