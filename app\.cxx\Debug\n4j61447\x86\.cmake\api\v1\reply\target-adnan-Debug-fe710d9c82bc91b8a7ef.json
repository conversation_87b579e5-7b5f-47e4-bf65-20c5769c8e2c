{"artifacts": [{"path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/build/intermediates/cxx/Debug/n4j61447/obj/x86/libadnan.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 129, "parent": 0}, {"command": 1, "file": 0, "line": 130, "parent": 0}, {"command": 1, "file": 0, "line": 133, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC"}], "defines": [{"backtrace": 2, "define": "FT2_BUILD_LIBRARY"}, {"backtrace": 2, "define": "FT_CONFIG_OPTIONS_H=\"slimftoptions.h\""}, {"define": "adnan_EXPORTS"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/fitz"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/pdf"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/xps"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/cbz"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/img"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/source/tiff"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/generated"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/resources"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec"}, {"backtrace": 2, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg"}], "language": "C", "sourceIndexes": [0], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 2, "id": "mupdfthirdparty::@6890427a1f51a3e7e1df"}, {"backtrace": 2, "id": "mupdfcore::@6890427a1f51a3e7e1df"}], "id": "adnan::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "libmupdfcore.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\24\\liblog.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\24\\libjnigraphics.so", "role": "libraries"}, {"backtrace": 3, "fragment": "-lm", "role": "libraries"}, {"fragment": "libmupdfthirdparty.a", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\libgs.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "C", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "<PERSON>nan", "nameOnDisk": "libadnan.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "mupdf.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}