package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.wordtopdf

import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.webkit.MimeTypeMap
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.loader.app.LoaderManager
import androidx.loader.content.CursorLoader
import androidx.loader.content.Loader
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.Compression
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ConverstionType
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.FileType
import pdf.reader.editor.pdfviewer.pdfreader.shared.*
import java.io.File
import java.text.Collator
import javax.inject.Inject

@HiltViewModel
class WordViewModel @Inject constructor(
    private val repositoryLocal: RepositoryLocal
) : BaseViewModel() {


    private val selectionArgs = MutableLiveData(SELECTION_DOCX)
    private var pdfSortingType = SortOrder.FileSortOrder.FILE_A_Z
    private val documentsList = MutableLiveData<List<DocumentsModel>>()

    fun convertToWord(
        file: File,
        type: FileType,
        compressed: Compression = Compression.RECOMMENDED,
        fileConversion: ConverstionType = ConverstionType.CONVERSION,
        progress: Progress
    ) {
        repositoryLocal.convertFile(file, type, compressed, fileConversion, progress)
    }

    fun loadFiles(context: FragmentActivity) = viewModelScope.launch {
        val obj = object : LoaderManager.LoaderCallbacks<Cursor> {
            override fun onCreateLoader(id: Int, args: Bundle?): Loader<Cursor> {

                try {

                    val projection = arrayOf(
                        MediaStore.Files.FileColumns.DATA,
                        MediaStore.Files.FileColumns.TITLE,
                        MediaStore.Files.FileColumns.DISPLAY_NAME,
                        MediaStore.Files.FileColumns.SIZE,
                        MediaStore.Files.FileColumns.DATE_ADDED,
                        MediaStore.Files.FileColumns.DATE_MODIFIED,
                        MediaStore.Files.FileColumns.MIME_TYPE
                    )
                    return CursorLoader(
                        context,
                        MediaStore.Files.getContentUri("external"),
                        projection,
                        selectionArgs.value?.first,
                        selectionArgs.value?.second,
                        null
                    )
                } catch (e: Exception) {

                    try {

                        val projection = arrayOf(
                            MediaStore.Files.FileColumns.DATA,
                            MediaStore.Files.FileColumns.TITLE,
                            MediaStore.Files.FileColumns.DISPLAY_NAME,
                            MediaStore.Files.FileColumns.SIZE,
                            MediaStore.Files.FileColumns.DATE_ADDED,
                            MediaStore.Files.FileColumns.DATE_MODIFIED,
                            MediaStore.Files.FileColumns.MIME_TYPE
                        )


                        return CursorLoader(
                            context,
                            MediaStore.Files.getContentUri("external"),
                            projection,
                            selectionArgs.value?.first,
                            selectionArgs.value?.second,
                            null
                        )

                    } catch (e: Exception) {
                        return CursorLoader(context)
                    }
                }

            }

            override fun onLoadFinished(loader: Loader<Cursor>, data: Cursor?) {

                data?.let {
                    val arrayList = arrayListOf<DocumentsModel>()

                    CoroutineScope(Dispatchers.IO).launch {

                        try {
                            while (it.moveToNext()) {
                                val model = setModel(it)
                                Log.d("TAG", "onLoadFinished: ${model.absolutePath}")

                                try {

                                    if (model.sizeInDigit > 0 && File(model.absolutePath).exists()) {
//                                        when (model.fileMimeType) {
//                                            DOC, DOCX, DOC_CONSTANT, DOCX_CONSTANT -> ++docxFiles
//                                            PDF, PDF_CONSTANT -> ++pdfFiles
//                                            PPT, PPTX, PPT_MSX, PPT_MS, PPT_SIMPLE, PPT_CONSTANT, PPTX_CONSTANT -> ++pptFiles
//                                            XLS, XLSX, XLS_SIMPLE, XLS_X, XLS_MSX, XLS_CONSTANT, XLSX_CONSTANT -> ++xlsFiles
//                                            TXT, TXT_CONSTANT -> ++txtFiles
//                                        }
                                        arrayList.add(model)
                                    }

                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }

                            println("array size : ${arrayList.size}")

                            //move to top
                            it.moveToPosition(-1)

                            sortAllFiles(arrayList)


                        } catch (e: Exception) {
                            println("HomeViewModel: " + "exception: ${e.message}")
                        }

                    }

                }
            }

            override fun onLoaderReset(loader: Loader<Cursor>) {
            }
        }

        LoaderManager.getInstance(context).initLoader(
            2,
            null,
            obj
        )
    }

    private fun setModel(cursor: Cursor): DocumentsModel {
        val pdfModel = DocumentsModel()
        try {

            pdfModel.absolutePath =
                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA))

            try {
                val name: String =
                    cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME))
//                val filename = name.replace(".pdf", "")
//                val newFName = filename.replace(".PDF", "")
                pdfModel.fileName = name
            } catch (e: Exception) {
                try {
//                    pdfModel.fileName = cursor.getString(
//                        cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.TITLE)
//                    )
                    val name: String =
                        cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.TITLE))

                    pdfModel.fileName = name
                } catch (e: Exception) {
                    pdfModel.fileName = "Unknown"
                }
            }

            pdfModel.sizeInDigit = cursor.getLong(
                cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE)
            )

            pdfModel.fileSize = getReadableSize(
                cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE))
            )
            pdfModel.dateInDigit = cursor.getLong(
                cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
            )

            pdfModel.fileDate = getFormattedDate(
                cursor.getLong(
                    cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
                )
            )

            try {
                pdfModel.fileMimeType = cursor.getString(
                    cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE)
                )
            } catch (e: Exception) {
                val file = Uri.fromFile(File(pdfModel.absolutePath))
                pdfModel.fileMimeType = MimeTypeMap.getFileExtensionFromUrl(file.toString())
            }

//            log("HomeViewModel", "fileMimeType - ${pdfModel.fileMimeType}")

            if (pdfModel.absolutePath.isNotEmpty()) {
                val file = File(pdfModel.absolutePath)
                pdfModel.parentFile = file.parent ?: ""
            }

        } catch (ex: Exception) {
            println("HomeViewModel: " + "exception - ${ex.message}")
            ex.printStackTrace()
        }
        return pdfModel
    }

    fun sortAllFiles(files: List<DocumentsModel>) {
        val collator = Collator.getInstance()
        when (pdfSortingType) {

            SortOrder.FileSortOrder.FILE_A_Z -> {
                val result = files.sortedWith { a1, a2 ->
                    collator.compare(
                        a1.fileName,
                        a2.fileName
                    )
                }

                documentsList.postValue(result)


            }
            SortOrder.FileSortOrder.FILE_Z_A -> {
                val result = files.sortedWith { a1, a2 ->
                    collator.compare(
                        a2.fileName,
                        a1.fileName
                    )
                }

                documentsList.postValue(result)
            }
            SortOrder.FileSortOrder.FILE_DATE_A -> {

                val result = files.sortedBy { it.dateInDigit }
                documentsList.postValue(result)
            }
            SortOrder.FileSortOrder.FILE_DATE_D -> {

                val result = files.sortedByDescending { it.dateInDigit }
                documentsList.postValue(result)
            }
            SortOrder.FileSortOrder.FILE_SIZE_A -> {

                val result = files.sortedBy { it.sizeInDigit }
                documentsList.postValue(result)
            }
            SortOrder.FileSortOrder.FILE_SIZE_Z -> {

                val result = files.sortedByDescending { it.sizeInDigit }
                documentsList.postValue(result)
            }
        }

    }

    fun getDocuments(): MutableLiveData<List<DocumentsModel>> {
        return documentsList
    }
}