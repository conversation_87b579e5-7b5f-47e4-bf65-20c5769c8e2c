{"archive": {}, "artifacts": [{"path": "libmupdfthirdparty.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 1, "file": 0, "line": 140, "parent": 0}, {"command": 2, "file": 0, "line": 128, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC"}], "defines": [{"backtrace": 2, "define": "DARWIN_NO_CARBON"}, {"backtrace": 2, "define": "FT2_BUILD_LIBRARY"}, {"backtrace": 2, "define": "FT_CONFIG_MODULES_H=\"slimftmodules.h\""}, {"backtrace": 2, "define": "FT_CONFIG_OPTIONS_H=\"slimftoptions.h\""}, {"backtrace": 2, "define": "HAVE_STDINT_H"}, {"backtrace": 2, "define": "OPJ_HAVE_STDINT_H"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jbig2dec"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/openjpeg/libopenjpeg"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/jpeg"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/mujs"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/zlib"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/thirdparty/freetype/include"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/freetype"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/jpeg"}, {"backtrace": 3, "path": "C:/Users/<USER>/StudioProjects/pdf-editor9D/app/src/main/jni/scripts/openjpeg"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "mupdfthirdparty::@6890427a1f51a3e7e1df", "name": "mupdfthirdparty", "nameOnDisk": "libmupdfthirdparty.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/mujs/one.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_arith.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_arith_iaid.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_arith_int.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_generic.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_halftone.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_huffman.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_image.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_metadata.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_mmr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_page.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_refinement.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_segment.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_symbol_dict.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jbig2dec/jbig2_text.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/bio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/cidx_manager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/cio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/dwt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/event.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/function_list.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/image.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/invert.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/j2k.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/jp2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/mct.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/mqc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/openjpeg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/opj_clock.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/phix_manager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/pi.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/ppix_manager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/raw.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/t1.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/t1_generate_luts.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/t2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/tcd.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/tgt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/thix_manager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/openjpeg/libopenjpeg/tpix_manager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jaricom.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jcomapi.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdapimin.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdapistd.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdarith.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdatadst.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdatasrc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdcoefct.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdcolor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jddctmgr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdhuff.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdinput.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdmainct.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdmarker.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdmaster.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdmerge.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdpostct.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdsample.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jdtrans.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jerror.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jfdctflt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jfdctfst.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jfdctint.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jidctflt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jidctfst.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jidctint.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jmemmgr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jquant1.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jquant2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/jpeg/jutils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/adler32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/compress.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/crc32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/deflate.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/inffast.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/inflate.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/inftrees.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/trees.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/uncompr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/zlib/zutil.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftbase.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftbbox.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftbitmap.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftfntfmt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftgasp.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftglyph.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftinit.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftstroke.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftsynth.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/ftsystem.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/base/fttype1.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/cff/cff.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/cid/type1cid.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/psaux/psaux.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/pshinter/pshinter.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/psnames/psnames.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/raster/raster.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/smooth/smooth.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/sfnt/sfnt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/truetype/truetype.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/freetype/src/type1/type1.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}