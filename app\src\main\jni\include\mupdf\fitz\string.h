#ifndef MUPDF_FITZ_STRING_H
#define MUPDF_FITZ_STRING_H

#include "../fitz/system.h"

/*
	Safe string functions
*/

/*
	fz_strsep: Given a pointer to a C string (or a pointer to NULL) break
	it at the first occurence of a delimiter char (from a given set).

	stringp: Pointer to a C string pointer (or NULL). Updated on exit to
	point to the first char of the string after the delimiter that was
	found. The string pointed to by stringp will be corrupted by this
	call (as the found delimiter will be overwritten by 0).

	delim: A C string of acceptable delimiter characters.

	Returns a pointer to a C string containing the chars of stringp up
	to the first delimiter char (or the end of the string), or NULL.
*/
char *fz_strsep(char **stringp, const char *delim);

/*
	fz_strlcpy: Copy at most n-1 chars of a string into a destination
	buffer with null termination, returning the real length of the
	initial string (excluding terminator).

	dst: Destination buffer, at least n bytes long.

	src: C string (non-NULL).

	n: Size of dst buffer in bytes.

	Returns the length (excluding terminator) of src.
*/
int fz_strlcpy(char *dst, const char *src, int n);

/*
	fz_strlcat: Concatenate 2 strings, with a maximum length.

	dst: pointer to first string in a buffer of n bytes.

	src: pointer to string to concatenate.

	n: Size (in bytes) of buffer that dst is in.

	Returns the real length that a concatenated dst + src would have been
	(not including terminator).
*/
int fz_strlcat(char *dst, const char *src, int n);

/*
	fz_dirname: extract the directory component from a path.
*/
void fz_dirname(char *dir, const char *path, int dirsize);

/*
	fz_urldecode: decode url escapes.
*/
char *fz_urldecode(char *url);

/*
	fz_cleanname: rewrite path to the shortest string that names the same path.

	Eliminates multiple and trailing slashes, interprets "." and "..".
	Overwrites the string in place.
*/
char *fz_cleanname(char *name);

/*
	fz_chartorune: UTF8 decode a single rune from a sequence of chars.

	rune: Pointer to an int to assign the decoded 'rune' to.

	str: Pointer to a UTF8 encoded string.

	Returns the number of bytes consumed. Does not throw exceptions.
*/
int fz_chartorune(int *rune, const char *str);

/*
	fz_runetochar: UTF8 encode a rune to a sequence of chars.

	str: Pointer to a place to put the UTF8 encoded character.

	rune: Pointer to a 'rune'.

	Returns the number of bytes the rune took to output. Does not throw
	exceptions.
*/
int fz_runetochar(char *str, int rune);

/*
	fz_runelen: Count how many chars are required to represent a rune.

	rune: The rune to encode.

	Returns the number of bytes required to represent this run in UTF8.
*/
int fz_runelen(int rune);

/*
	fz_strtod: Locale-independent implementation of strtod().
*/
double fz_strtod(const char *s, char **es);

/*
	fz_ftoa: Compute decimal integer m, exp such that:
		f = m * 10^exp
		m is as short as possible without losing exactness
	Assumes special cases (NaN, +Inf, -Inf) have been handled.
*/
void fz_ftoa(float f, char *s, int *exp, int *neg, int *ns);

#endif
