package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.webtopdf.weblink

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentEditPdfBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.WebLinkFragmentBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.WebToPdfFragmentBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard

class WebLinkFragment : BaseFragment<WebLinkFragmentBinding, WebLinkViewModel>() {

    override val viewModel: Class<WebLinkViewModel>
        get() = WebLinkViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): WebLinkFragmentBinding = if (mViewModel.view == null)
        WebLinkFragmentBinding.inflate(inflater, container, false)
    else
        WebLinkFragmentBinding.bind(mViewModel.view!!)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mViewDataBinding.btnConvert.setOnClickListener {
            sharedViewModel.setWebURL(mViewDataBinding.etUrl.text.toString()).let {
                if (mViewDataBinding.etUrl.text?.isEmpty() == true) {
                    showToast(getString(R.string.txt_link_warning))
                } else if (!mViewDataBinding.etUrl.text?.contains(".com")!!) {
                    showToast(getString(R.string.txt_com_warning))
                } else {
                    mViewDataBinding.btnConvert.hideKeyboard()
                    navigate(WebLinkFragmentDirections.actionWebLinkFragmentToWebToPdfFragment())
                }
            }
        }

        mViewDataBinding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }


    }


}