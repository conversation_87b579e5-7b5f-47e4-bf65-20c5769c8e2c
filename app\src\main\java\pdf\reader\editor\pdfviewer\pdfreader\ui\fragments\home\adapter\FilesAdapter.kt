package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.home.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.AllFileItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.ItemRecyclerNativeAdLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileMenuHelper
import pdf.reader.editor.pdfviewer.pdfreader.newads.LogType
import pdf.reader.editor.pdfviewer.pdfreader.newads.adsToast
import pdf.reader.editor.pdfviewer.pdfreader.newads.data.NativeAdInfo
import pdf.reader.editor.pdfviewer.pdfreader.newads.isConnectionLimited
import pdf.reader.editor.pdfviewer.pdfreader.newads.isNetworkAvailable
import pdf.reader.editor.pdfviewer.pdfreader.newads.logs
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.PDFFileActions
import java.util.*

class FilesAdapter(
    private val activity: FragmentActivity,
    private val remoteValue: Boolean,
    private val googleConsent: Boolean,
    private val nativeAdUnitId: String,
    private val adPosition: Int,
    private val onPDFClick: (PDFFileActions, DocumentsModel?) -> Unit,
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>(), Filterable {

    private var filterArrayList: ArrayList<DocumentsModel> = ArrayList()
    private var filesList: ArrayList<DocumentsModel> = ArrayList()
    var callBack: ((isNoResult: Boolean) -> Unit)? = null
    private var isSearchView: Boolean = false
    private var isFromAsset: Boolean = false
    fun setSearchView(searchView: Boolean) {
        isSearchView = searchView
        notifyDataSetChanged()
    }

    companion object {
        const val ITEM_VIEW_TYPE = 0
        const val NATIVE_AD_VIEW_TYPE = 1
    }

    // Cache for native ads
    private val adCache = HashMap<Int, NativeAdInfo>()
    override fun getItemViewType(position: Int): Int {
        return if (googleConsent && remoteValue && activity.isNetworkAvailable()
            && !isSearchView && !activity.isConnectionLimited()
        ) {
            if ((position + 1) % adPosition == 0) {
                NATIVE_AD_VIEW_TYPE
            } else {
                ITEM_VIEW_TYPE
            }
        } else {
            ITEM_VIEW_TYPE
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
//        val binding: AllFileItemLayoutBinding =
//            AllFileItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
//
//        return ViewHolder(binding)
        return if (viewType == NATIVE_AD_VIEW_TYPE) {
            NativeAdViewHolder(
                ItemRecyclerNativeAdLayoutBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            val binding: AllFileItemLayoutBinding =
                AllFileItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)

            return ViewHolder(binding)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (getItemViewType(position) == NATIVE_AD_VIEW_TYPE) {
            val adPosition1 = position / adPosition
            val adViewHolder = holder as NativeAdViewHolder
            val cachedAd = adCache[adPosition1]
            logs("adPosition1_ = $adPosition1 notified contains $cachedAd")

//            if (cachedAd == null) {
//                adViewHolder.binding.nativeAdLayout.nativeAdView.hide()
//                adViewHolder.binding.shimmer.shimmer.show()
//
//            }
//            adViewHolder.binding.shimmer.shimmer.show()
            // Bind the cached ad if available
            if (adCache.containsKey(adPosition1)) {
//                activity.lifecycleScope.launch {
//                    delay(500L)
                cachedAd?.nativeAd?.let {
                    logs("ListNative: inside bind if", LogType.INFO)
                    adViewHolder.bind(cachedAd)
                }

//                }
            } else {
                adViewHolder.binding.shimmer.shimmer.show()  // Show shimmer while loading
                logs("ListNative: inside bind else", LogType.INFO)
                loadNativeAd(adViewHolder, adPosition1)
            }


        } else {
            val actualPosition = position - position / adPosition
            val file = filterArrayList[actualPosition]

            (holder as ViewHolder).apply {

                holder.binding.apply {
                    imgPDFThumbnail.setImageResource(R.drawable.ic_pdf_thumbnail)
                    txtFileName.text = file.fileName
                    txtFileDate.text = file.fileDate
                    txtFileSize.text = file.fileSize
                    if (isFromAsset) imgOptions.hide() else imgOptions.show()
                }

                holder.binding.root.setOnClickListener {
                    onPDFClick(PDFFileActions.SELECT, file)
                }
                holder.binding.imgOptions.setOnClickListener(object :
                    FileMenuHelper.OnClickFileMenu(activity) {
                    override val documentModel: DocumentsModel?
                        get() = file
                    override val isRecent: Boolean
                        get() = false

                    override fun onComplete(action: PDFFileActions, file: DocumentsModel?) {
                        <EMAIL>(action, file)
                    }

                    override fun onDelete() {
                        // Calculate the ad position
                        val adPosition1 = absoluteAdapterPosition / adPosition
                        if (adCache[adPosition1] != null) {
                            adCache[adPosition1]?.nativeAd?.destroy()
                            adCache.remove(adPosition1)
                            logs("removed ad cache at: $adPosition1")
                        }
                    }

                })
            }
        }

    }

    private fun loadNativeAd(holder: NativeAdViewHolder, adPosition: Int) {
//        if ( adCache[adPosition])
        if (adCache.containsKey(adPosition)) {
            CoroutineScope(Dispatchers.Main).launch {
                logs("ListNativeAd_Position_${adPosition}_already_loaded")
                val notifyPosition = (adPosition + 1) * <EMAIL> - 1

                notifyItemChanged(notifyPosition)
            }
            return
        }
        logs("ListNativeAd_Position_${adPosition}_Requesting")
        adCache[adPosition] = NativeAdInfo(
            adKey = adPosition,
            adUnitId = nativeAdUnitId,
            adName = "ListNativeAt_${adPosition}",
            remoteConfig = remoteValue,
            canRequestAd = googleConsent
        )
        CoroutineScope(Dispatchers.IO).launch {
            val adLoader = AdLoader.Builder(holder.itemView.context, nativeAdUnitId)
                .forNativeAd { nativeAd ->
                    if (adCache[adPosition]?.nativeAd == null) {
                        adCache[adPosition]?.nativeAd = nativeAd

                        CoroutineScope(Dispatchers.Main).launch {
                            val notifyPosition = (adPosition + 1) * <EMAIL> - 1
                            logs("notifyPosition: $notifyPosition, bindingAdapterPosition: ${holder.bindingAdapterPosition}")

                            notifyItemChanged(notifyPosition)
                        }
                    }
                }
                .withAdListener(object : AdListener() {
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        logs("Failed to load native ad at position $adPosition")
                        logs("ListNativeAd_Position_${adPosition}_FailedToLoad")
                    }

                    override fun onAdLoaded() {
                        super.onAdLoaded()
//                        logs("Loaded native ad at position $adPosition")
                        logs("ListNativeAd_Position_${adPosition}_Loaded")
                        CoroutineScope(Dispatchers.Main).launch {

                            holder.itemView.context.adsToast("List native ad loaded at position $adPosition")
                        }
                    }
                })
                .build()

            adLoader.loadAd(AdRequest.Builder().build())
        }
    }

    override fun getItemCount(): Int = filterArrayList.size + filterArrayList.size / adPosition


    fun setData(filesList: List<DocumentsModel>, isFromAsset: Boolean = false) {
        this.isFromAsset = isFromAsset
        this.filesList.clear()
        this.filterArrayList.clear()
        this.filesList.addAll(filesList)
        this.filterArrayList.addAll(filesList)
        notifyDataSetChanged()


    }

    inner class NativeAdViewHolder(val binding: ItemRecyclerNativeAdLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(nativeAd: NativeAdInfo?) {
            logs("adCache: ${adCache.size}")
            if (nativeAd == null) return
            logs("ListNative: ${nativeAd}_for bind", LogType.INFO)
            binding.shimmer.shimmer.show()
            activity.lifecycleScope.launch {
                binding.shimmer.shimmer.hide()
                binding.nativeAdLayout.nativeAdView.show()
                binding.nativeAdLayout.apply {
                    nativeAdView.headlineView = txtTitle
                    nativeAdView.callToActionView = btnCallToAction

                    btnCallToAction.text = nativeAd.nativeAd?.callToAction
                    txtAdDescription.text = nativeAd.nativeAd?.body
                    (nativeAdView.headlineView as AppCompatTextView).text =
                        nativeAd.nativeAd?.headline
                    imgIcon.setImageDrawable(nativeAd.nativeAd?.icon?.drawable)

                    nativeAd.nativeAd?.let { nativeAdView.setNativeAd(it) }
                    parent.show()
                }


            }


        }
    }

    inner class ViewHolder(val binding: AllFileItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {
        //        val documentModel: DocumentsModel?
//            get() = if (bindingAdapterPosition != RecyclerView.NO_POSITION && bindingAdapterPosition < filterArrayList.size) {
//                filterArrayList[bindingAdapterPosition]
//            } else {
//                null
//            }
//        val documentModel: DocumentsModel
//            get() = filterArrayList[bindingAdapterPosition]

        init {

//            binding.imgOptions.setOnClickListener(object :
//                FileMenuHelper.OnClickFileMenu(activity) {
//                override val documentModel: DocumentsModel?
//                    get() = <EMAIL>
//                override val isRecent: Boolean
//                    get() = false
//
//                override fun onComplete(action: PDFFileActions, file: DocumentsModel?) {
//                    <EMAIL>(action, file)
//                }
//
//                override fun onDelete() {
//                    // Calculate the ad position
//                    val adPosition1 = absoluteAdapterPosition / adPosition
//                    if (adCache[adPosition1] != null) {
//                        adCache[adPosition1]?.destroy()
//                        adCache.remove(adPosition1)
//                        logs("removed ad cache at: $adPosition1")
//                    }
//                }
//
//            })
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val searchContent = constraint.toString().lowercase()
                if (searchContent.isEmpty()) {
                    filterArrayList = ArrayList(filesList)
                    logs("getFilter::isEmpty> ${filterArrayList.size}")

                } else {
                    filterArrayList.clear()
                    for (docModel in filesList) {
                        if (docModel.fileName.lowercase(Locale.ROOT).trim()
                                .contains(searchContent.lowercase(Locale.ROOT).trim())
                        ) {
                            filterArrayList.add(docModel)

                        }
                    }
                    logs("getFilter::isNotEmpty> ${filterArrayList.size}")

                }

                val filterResults = FilterResults()
                filterResults.values = filterArrayList
                return filterResults
            }

            @SuppressLint("NotifyDataSetChanged")
            override fun publishResults(p0: CharSequence?, results: FilterResults?) {
                filterArrayList = ArrayList(results?.values as ArrayList<DocumentsModel>)
                logs("getFilter::publishResults> ${filterArrayList.size}")
                if (filterArrayList.isEmpty()) {
                    callBack?.invoke(true)
                } else {
                    callBack?.invoke(false)
                }
                notifyDataSetChanged()
            }
        }
    }
}