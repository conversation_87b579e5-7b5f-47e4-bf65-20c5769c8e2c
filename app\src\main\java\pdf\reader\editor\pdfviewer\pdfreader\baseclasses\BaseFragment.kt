package pdf.reader.editor.pdfviewer.pdfreader.baseclasses

import android.Manifest
import android.app.Dialog
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProviders
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkStoragePermissionGranted
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isVersionGreaterThanEqualTo
import pdf.reader.editor.pdfviewer.pdfreader.extensions.openSettings
import pdf.reader.editor.pdfviewer.pdfreader.extensions.requestPermission
import pdf.reader.editor.pdfviewer.pdfreader.extensions.shouldShowRationale
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showToast
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.shared.PublicValue
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.main.MainViewModel
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogClass
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.PermissionDialog
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.system.exitProcess

abstract class BaseFragment<T : ViewBinding, V : BaseViewModel> : Fragment() {

    private var mActivity: BaseActivity<*, *>? = null
    lateinit var mViewDataBinding: T
    protected lateinit var mViewModel: V
    protected lateinit var loadingDialog: Dialog
    val sharedViewModel: MainViewModel by activityViewModels()
    abstract val viewModel: Class<V>
    abstract fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): T

    protected lateinit var storage: Storage

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        mViewDataBinding = getViewBinding(inflater, container)
        return mViewDataBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isAlive {
            storage = Storage(it)
            loadingDialog = DialogClass.loadingDialog(it)
            initAllViews()
        }


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewModel = ViewModelProviders.of(this)[viewModel]
//        sharedViewModel = ViewModelProviders.of(requireActivity())[MainViewModel::class.java]
    }

    open fun initAllViews() {}

    open fun getLocalDate(): String {
        val sdf = SimpleDateFormat("dd/M/yyyy")
        val currentDate = sdf.format(Date())
        return currentDate
    }

    open fun getLocalTimeStamp(): String {
        val stamp = System.currentTimeMillis()
        return stamp.toString()
    }

    open fun subscribeToViewLiveData() {
        manageThemeSP()
    }

    /**
     * navigation
     */
    fun navigate(action: NavDirections) {
        findNavController().navigate(action)
    }

    fun isDarkTheme(): Boolean {
        return this.resources.configuration.uiMode and
                Configuration.UI_MODE_NIGHT_MASK == Configuration.UI_MODE_NIGHT_YES
    }

    fun isDarkThemeSP(): String {
        return storage.getPreferredTheme()
    }

    private fun manageThemeSP() {
        if (isDarkThemeSP() == PublicValue.DARK_THEME) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        } else if (isDarkThemeSP() == PublicValue.DEFAULT_THEME) {

        }
    }

    open fun manipulateTheme() {
        if (isDarkTheme()) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            isAlive { activity ->
                activity.window?.decorView?.systemUiVisibility =
                    activity.window?.decorView?.systemUiVisibility?.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                        ?.or(
                            activity.window?.decorView?.systemUiVisibility?.and(
                                View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
                            )!!
                        )!!
            }

            CoroutineScope(IO).launch {
                storage.setPreferredTheme(PublicValue.DARK_THEME)
            }
        } else {
            activity?.window?.decorView?.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
        }
    }

    open fun checkAndAskPermissions() {
        if (activity?.checkStoragePermissionGranted() == false) {
            PermissionDialog.getInstance {
                when (it) {
                    ViewPdfActions.RUNTIME_PERMISSION_ALLOW -> {
                        if (isVersionGreaterThanEqualTo(Build.VERSION_CODES.R))
                            requestPermissionAndroidR()
                        else
                            when {
                                activity?.shouldShowRationale(
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                                ) == true -> {
                                    activity?.openSettings()
                                }
                                else -> {
                                    activity?.requestPermission(
                                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                        PublicValue.KEY_REQUEST_PERMISSIONS
                                    )
                                }
                            }
                    }

                    ViewPdfActions.RUNTIME_PERMISSION_DENAY -> {
                        exitProcess(0)
                    }

                    else -> {}
                }
            }.show(childFragmentManager, "pdfPassword")
        }
    }

    protected open fun requestPermissionAndroidR() {
        try {
            val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
            intent.addCategory("android.intent.category.DEFAULT")
            intent.data =
                Uri.parse(String.format("package:%s", activity?.packageName))
            startActivityForResult(intent, PublicValue.KEY_REQUEST_PERMISSIONS_11)
        } catch (e: Exception) {
            val intent = Intent()
            intent.action = Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION
            startActivityForResult(intent, PublicValue.KEY_REQUEST_PERMISSIONS_11)
        }
    }

    open fun showToast(message: String, length: Int = Toast.LENGTH_SHORT) {
        isAlive {

            it.showToast(message, length)
        }
    }


}