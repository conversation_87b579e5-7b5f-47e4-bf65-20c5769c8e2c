package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.imagetopdf.adapter

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import coil.load
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.SplitItemLayoutBinding


class ImagePickerAdapter(
    var limit: Int,
    private var imageSelectListener: (ArrayList<PhotoModel>) -> Unit
) :
    RecyclerView.Adapter<ImagePickerAdapter.ImageViewHolder>() {


    private val selectedImages = arrayListOf<PhotoModel>()
    private val images: ArrayList<PhotoModel> = ArrayList()


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val binding =
            SplitItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ImageViewHolder(
            binding
        )
    }

    override fun onBindViewHolder(
        viewHolder: ImageViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(viewHolder, position)
        } else {
            when {
                payloads.any { it is ImageSelectedOrUpdated } -> {
                    val image = images[position]
//                    val selectedIndex = findImageIndex(image, selectedImages)
                    viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_selected)
//                    viewHolder.binding.tvNumber.background = ContextCompat.getDrawable(
//                        viewHolder.binding.root.context,
//                        R.drawable.selected_image_background
//                    )

                }
                payloads.any { it is ImageUnselected } -> {
                    viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
//                    viewHolder.binding.txt.text = ""
//                    viewHolder.binding.tvNumber.background = ContextCompat.getDrawable(
//                        viewHolder.binding.root.context,
//                        R.drawable.unselected_image_background
//                    )
//                    setupItemForeground(viewHolder.binding.iv, false)
                }
                else -> {
                    onBindViewHolder(viewHolder, position)
                }
            }
        }
    }

    override fun onBindViewHolder(viewHolder: ImageViewHolder, position: Int) {
        val image = images[position]
        val selectedIndex = findImageIndex(image, selectedImages)
        val isSelected = selectedIndex != -1
        viewHolder.binding.imgThumbnail.load(image.path)
        viewHolder.binding.txtPageNumber.text = (position + 1).toString()
//        setupItemForeground(viewHolder.binding.iv, isSelected)

        if (isSelected) {

            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_selected)
        } else {
            viewHolder.binding.imgSelected.setImageResource(R.drawable.ic_unselected)
        }
        viewHolder.itemView.setOnClickListener {
            selectOrRemoveImage(viewHolder.itemView.context, image, position)
        }
    }

    override fun getItemCount(): Int {
        return images.size
    }

    private fun setupItemForeground(view: View, isSelected: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            view.foreground = if (isSelected) ColorDrawable(
                ContextCompat.getColor(
                    view.context,
                    R.color.transparent
                )
            ) else null
        }
    }

    private fun selectOrRemoveImage(
        context: Context,
        image: PhotoModel,
        position: Int
    ) {

        val selectedIndex = findImageIndex(image, selectedImages)
        if (selectedIndex != -1) {
            selectedImages.removeAt(selectedIndex)
            notifyItemChanged(
                position,
                ImageUnselected()
            )
            val indexes = findImageIndexes(selectedImages, images)
            for (index in indexes) {
                notifyItemChanged(
                    index,
                    ImageSelectedOrUpdated()
                )
            }
        } else {
            if ((limit == 1 && selectedImages.size > 0) || selectedImages.size >= 100) {
                // val message = if (config.limitMessage != null) config.limitMessage!! else String.format(context.resources.getString(R.string.imagepicker_msg_limit_images), config.maxSize)
                // ToastHelper.show(context, message)
                Toast.makeText(context, "Limit exceeded!", Toast.LENGTH_SHORT).show()
                return
            } else {
                selectedImages.add(image)
                notifyItemChanged(
                    position,
                    ImageSelectedOrUpdated()
                )
            }
        }
        imageSelectListener(selectedImages)
    }

    fun setData(images: List<PhotoModel>) {
        this.images.clear()
        this.images.addAll(images)
        notifyDataSetChanged()
    }

    fun setSelectedImages(selectedImages: ArrayList<PhotoModel>) {
        this.selectedImages.clear()
        this.selectedImages.addAll(selectedImages)
        notifyDataSetChanged()

    }

    fun getSelectedImages(): ArrayList<PhotoModel> {
        return selectedImages
    }

    class ImageViewHolder(val binding: SplitItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)

    private fun findImageIndex(image: PhotoModel, images: ArrayList<PhotoModel>): Int {
        for (i in images.indices) {
            if (images[i] == image) {
                return i
            }
        }
        return -1
    }

    private fun findImageIndexes(
        subImages: ArrayList<PhotoModel>,
        images: ArrayList<PhotoModel>
    ): ArrayList<Int> {
        val indexes = arrayListOf<Int>()
        for (image in subImages) {
            for (i in images.indices) {
                if (images[i] == image) {
                    indexes.add(i)
                    break
                }
            }
        }
        return indexes
    }

    class ImageSelectedOrUpdated

    class ImageUnselected
}