package pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseDialog
import pdf.reader.editor.pdfviewer.pdfreader.databinding.DialogGoToPageBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions

class DialogGoToPage : BaseDialog<DialogGoToPageBinding>() {

    companion object {
        //arguments
        private var callback: ((page: Int, ViewPdfActions) -> Unit)? = null
        private var count: Int? = null
        fun getInstance(
            count: Int,
            callback: ((page: Int, ViewPdfActions) -> Unit)
        ): DialogGoToPage {
            this.callback = callback
            this.count = count
            return DialogGoToPage()
        }
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): DialogGoToPageBinding = DialogGoToPageBinding.inflate(inflater, container, false)


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Handler(Looper.getMainLooper()).postDelayed({
            view.showKeyboard()
            binding.etPdfTitle.requestFocus()
        }, 500)

        binding.btnCancel.setOnClickListener {
            it.hideKeyboard()
            dismiss()
        }

        binding.btnGoPage.setOnClickListener {
            try {
                val pageNo = Integer.parseInt(binding.etPdfTitle.text.toString())
                if (pageNo <= count!!) {
                    if (pageNo > 0) {
                        val value = binding.etPdfTitle.text.toString().toInt() - 1
                        callback?.invoke(value, ViewPdfActions.OK_GOTO_PAGE)

                    } else {
                        callback?.invoke(0, ViewPdfActions.VALID_PAGE_SIZE)
                    }
                } else {
                    callback?.invoke(0, ViewPdfActions.PAGE_SIZE_EXCEDED)
                }
                it.hideKeyboard()
                dismiss()
            } catch (ex: Exception) {
                println(ex.localizedMessage)
            }
        }
    }


    override fun onStart() {
        super.onStart()
        setTopAnimation()
    }


}