package com.artifex.mupdfdemo;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.PointF;
import android.graphics.RectF;
import android.util.Log;

import com.itextpdf.kernel.colors.Separation;

import java.util.ArrayList;

import pdf.reader.editor.pdfviewer.pdfreader.R;
import pdf.reader.editor.pdfviewer.pdfreader.local.sharedprefrence.Storage;

public class MuPDFCore {
    private static boolean gs_so_available = false;

    static {
        try {
            System.loadLibrary("adnan");

            if (gprfSupportedInternal()) {
                try {
                    System.loadLibrary("gs");
                    gs_so_available = true;
                } catch (UnsatisfiedLinkError e) {
                    gs_so_available = false;
                }
            }
        } catch (UnsatisfiedLinkError e) {

        }
    }

    private int numPages = -1;
    private float pageWidth;
    private float pageHeight;
    private long globals;
    private byte fileBuffer[];
    private String file_format;
    private boolean isUnencryptedPDF;
    private final boolean wasOpenedFromBuffer;
    private Storage storage;


    private static native boolean gprfSupportedInternal();

    private native long openFile(String filename);

    private native long openBuffer(String magic);

    private native String fileFormatInternal();

    private native boolean isUnencryptedPDFInternal();

    private native int countPagesInternal();

    private native void gotoPageInternal(int localActionPageNum);

    private native float getPageWidth();

    private native float getPageHeight();

    private native void drawPage(Bitmap bitmap,
                                 int pageW, int pageH,
                                 int patchX, int patchY,
                                 int patchW, int patchH,
                                 long cookiePtr);

    private native void updatePageInternal(Bitmap bitmap,
                                           int page,
                                           int pageW, int pageH,
                                           int patchX, int patchY,
                                           int patchW, int patchH,
                                           long cookiePtr);

    private native RectF[] searchPage(String text);

    private native TextChar[][][][] text();

    private native byte[] textAsHtml();

    private native void addMarkupAnnotationInternal(PointF[] quadPoints, int type);

    private native void addInkAnnotationInternal(PointF[][] arcs);

    private native void deleteAnnotationInternal(int annot_index);

    private native int passClickEventInternal(int page, float x, float y);

    private native void setFocusedWidgetChoiceSelectedInternal(String[] selected);

    private native String[] getFocusedWidgetChoiceSelected();

    private native String[] getFocusedWidgetChoiceOptions();

    private native int getFocusedWidgetSignatureState();

    private native String checkFocusedSignatureInternal();

    private native boolean signFocusedSignatureInternal(String keyFile, String password);

    private native int setFocusedWidgetTextInternal(String text);

    private native String getFocusedWidgetTextInternal();

    private native int getFocusedWidgetTypeInternal();

    private native LinkInfo[] getPageLinksInternal(int page);

    private native RectF[] getWidgetAreasInternal(int page);

    private native Annotation[] getAnnotationsInternal(int page);

    private native OutlineItem[] getOutlineInternal();

    private native boolean hasOutlineInternal();

    private native boolean needsPasswordInternal();

    private native boolean authenticatePasswordInternal(String password);

    private native MuPDFAlertInternal waitForAlertInternal();

    private native void replyToAlertInternal(MuPDFAlertInternal alert);

    private native void startAlertsInternal();

    private native void stopAlertsInternal();

    private native void destroying();

    private native boolean hasChangesInternal();

    private native void saveInternal();

    private native long createCookie();

    private native void destroyCookie(long cookie);

    private native void abortCookie(long cookie);

    private native String startProofInternal(int resolution);

    private native void endProofInternal(String filename);

    private native int getNumSepsOnPageInternal(int page);

    private native int controlSepOnPageInternal(int page, int sep, boolean disable);

    private native Separation getSepInternal(int page, int sep);

    public native boolean javascriptSupported();

    public synchronized native void setInkThickness(float inkThickness);

    public synchronized native void setInkOpacity(float inkOpacity);

    public synchronized native void setInkColor(float r, float g, float b);

    public synchronized native void setHighlightColor(float r, float g, float b);

    public synchronized native void setHighlightOpacity(float highlightOpacity);

    public synchronized native void setHighlightHeight(float highlightHeight);

    public synchronized native void setUnderlineColor(float r, float g, float b);

    public synchronized native void setUnderlineOpacity(float underlineOpacity);

    public synchronized native void setUnderlineHeight(float underlineHeight);

    public synchronized native void setStrikeoutColor(float r, float g, float b);

    public synchronized native void setStrikeoutOpacity(float strikeoutOpacity);

    public synchronized native void setStrikeoutHeight(float strikeoutHeight);

    public synchronized native void setTextAnnotIconColor(float r, float g, float b);

    public class Cookie {
        private final long cookiePtr;

        public Cookie() {
            cookiePtr = createCookie();
            if (cookiePtr == 0) {
                Log.d("TAG", "Cookie:  out of memory");
                return;
            }
        }

        public void abort() {
            abortCookie(cookiePtr);
        }

        public void destroy() {
            // We could do this in finalize, but there's no guarantee that
            // a finalize will occur before the muPDF context occurs.
            destroyCookie(cookiePtr);
        }
    }

    public MuPDFCore(Context context, String filename) throws Exception {

        globals = openFile(filename);
        if (globals == 0) {
            throw new Exception(String.format(context.getString(R.string.cannot_open_file_Path), filename));
        }
        file_format = fileFormatInternal();
        isUnencryptedPDF = isUnencryptedPDFInternal();
        wasOpenedFromBuffer = false;
        storage = new Storage(context);
    }

    public MuPDFCore(Context context, byte buffer[], String magic) throws Exception {
        fileBuffer = buffer;
        globals = openBuffer(magic != null ? magic : "");
        if (globals == 0) {
            throw new Exception(context.getString(R.string.cannot_open_buffer));
        }
        file_format = fileFormatInternal();
        isUnencryptedPDF = isUnencryptedPDFInternal();
        wasOpenedFromBuffer = true;
    }

    public int countPages() {
        if (numPages < 0)
            numPages = countPagesSynchronized();

        return numPages;
    }

    public String fileFormat() {
        return file_format;
    }

    public boolean isUnencryptedPDF() {
        return isUnencryptedPDF;
    }

    public boolean wasOpenedFromBuffer() {
        return wasOpenedFromBuffer;
    }

    private synchronized int countPagesSynchronized() {
        return countPagesInternal();
    }

    /* Shim function */
    void gotoPage(int page) {
        if (page > numPages - 1)
            page = numPages - 1;
        else if (page < 0)
            page = 0;
        gotoPageInternal(page);
        this.pageWidth = getPageWidth();
        this.pageHeight = getPageHeight();
    }

    public synchronized PointF getPageSize(int page) {
        gotoPage(page);
        return new PointF(pageWidth, pageHeight);
    }

    private boolean pageSizeLegal(Bitmap bitmap, int i10, int i11, int i12, int i13, int i14, int i15) {
        return bitmap != null && !bitmap.isRecycled() && i10 >= i12 + i14 && i11 >= i13 + i15 && bitmap.getWidth() >= i14 && bitmap.getHeight() >= i15;
    }

    public MuPDFAlert waitForAlert() {
        MuPDFAlertInternal alert = waitForAlertInternal();
        return alert != null ? alert.toAlert() : null;
    }

    public void replyToAlert(MuPDFAlert alert) {
        replyToAlertInternal(new MuPDFAlertInternal(alert));
    }

    public void stopAlerts() {
        stopAlertsInternal();
    }

    public void startAlerts() {
        startAlertsInternal();
    }

    public synchronized void onDestroy() {
        destroying();
        globals = 0;
    }



    public synchronized void drawPage(Bitmap bm, int page,
                                      int pageW, int pageH,
                                      int patchX, int patchY,
                                      int patchW, int patchH,
                                      Cookie cookie) {
//        if (pageSizeLegal(bm, pageW, pageH, patchX, patchY, patchW, patchW)) {
            gotoPage(page);
            drawPage(bm, pageW, pageH, patchX, patchY, patchW, patchH, cookie.cookiePtr);
//        } else {
//            Log.d("TAG", "drawPage: drawPage pageSize not legal");
//        }

    }

    public synchronized void updatePage(Bitmap bm, int page,
                                        int pageW, int pageH,
                                        int patchX, int patchY,
                                        int patchW, int patchH,
                                        Cookie cookie) {
        updatePageInternal(bm, page, pageW, pageH, patchX, patchY, patchW, patchH, cookie.cookiePtr);
    }

    public synchronized PassClickResult passClickEvent(int page, float x, float y) {
        boolean changed = passClickEventInternal(page, x, y) != 0;

        switch (WidgetType.values()[getFocusedWidgetTypeInternal()]) {
            case TEXT:
                return new PassClickResultText(changed, getFocusedWidgetTextInternal());
            case LISTBOX:
            case COMBOBOX:
                return new PassClickResultChoice(changed, getFocusedWidgetChoiceOptions(), getFocusedWidgetChoiceSelected());
            case SIGNATURE:
                return new PassClickResultSignature(changed, getFocusedWidgetSignatureState());
            default:
                return new PassClickResult(changed);
        }

    }

    public synchronized boolean setFocusedWidgetText(int page, String text) {
        boolean success;
        gotoPage(page);
        success = setFocusedWidgetTextInternal(text) != 0 ? true : false;

        return success;
    }

    public synchronized void setFocusedWidgetChoiceSelected(String[] selected) {
        setFocusedWidgetChoiceSelectedInternal(selected);
    }

    public synchronized String checkFocusedSignature() {
        return checkFocusedSignatureInternal();
    }

    public synchronized boolean signFocusedSignature(String keyFile, String password) {
        return signFocusedSignatureInternal(keyFile, password);
    }

    public synchronized LinkInfo[] getPageLinks(int page) {
        return getPageLinksInternal(page);
    }

    public synchronized RectF[] getWidgetAreas(int page) {
        return getWidgetAreasInternal(page);
    }

    public synchronized Annotation[] getAnnoations(int page) {
        return getAnnotationsInternal(page);
    }

    public synchronized RectF[] searchPage(int page, String text) {
        gotoPage(page);
        return searchPage(text);
    }

    public synchronized byte[] html(int page) {
        gotoPage(page);
        return textAsHtml();
    }

    public synchronized TextWord[][] textLines(int page) {
        gotoPage(page);
        TextChar[][][][] chars = text();

        // The text of the page held in a hierarchy (blocks, lines, spans).
        // Currently we don't need to distinguish the blocks level or
        // the spans, and we need to collect the text into words.
        ArrayList<TextWord[]> lns = new ArrayList<TextWord[]>();

        for (TextChar[][][] bl : chars) {
            if (bl == null)
                continue;
            for (TextChar[][] ln : bl) {
                ArrayList<TextWord> wds = new ArrayList<TextWord>();
                TextWord wd = new TextWord();

                for (TextChar[] sp : ln) {
                    for (TextChar tc : sp) {
                        if (tc.c != ' ') {
                            wd.Add(tc);
                        } else if (wd.w.length() > 0) {
                            wds.add(wd);
                            wd = new TextWord();
                        }
                    }
                }

                if (wd.w.length() > 0)
                    wds.add(wd);

                if (wds.size() > 0)
                    lns.add(wds.toArray(new TextWord[wds.size()]));
            }
        }

        return lns.toArray(new TextWord[lns.size()][]);
    }

    public synchronized void addMarkupAnnotation(int page, PointF[] quadPoints, Annotation.Type type) {
        gotoPage(page);
        addMarkupAnnotationInternal(quadPoints, type.ordinal());
    }

    public synchronized void addInkAnnotation(int page, PointF[][] arcs) {
        gotoPage(page);
        addInkAnnotationInternal(arcs);
    }

    public synchronized void deleteAnnotation(int page, int annot_index) {
        gotoPage(page);
        deleteAnnotationInternal(annot_index);
    }

    public synchronized boolean hasOutline() {
        return hasOutlineInternal();
    }

    public synchronized OutlineItem[] getOutline() {
        return getOutlineInternal();
    }

    public synchronized boolean needsPassword() {
        return needsPasswordInternal();
    }

    public synchronized boolean authenticatePassword(String password) {
        return authenticatePasswordInternal(password);
    }

    public synchronized boolean hasChanges() {
        return hasChangesInternal();
    }

    public synchronized void save() {
        saveInternal();
    }

    public synchronized String startProof(int resolution) {
        return startProofInternal(resolution);
    }

    public synchronized void endProof(String filename) {
        endProofInternal(filename);
    }

    public static boolean gprfSupported() {
        if (gs_so_available == false)
            return false;
        return gprfSupportedInternal();
    }

    public boolean canProof() {
        String format = fileFormat();
        if (format.contains("PDF"))
            return true;
        return false;
    }

    public synchronized int getNumSepsOnPage(int page) {
        return getNumSepsOnPageInternal(page);
    }

    public synchronized int controlSepOnPage(int page, int sep, boolean disable) {
        return controlSepOnPageInternal(page, sep, disable);
    }

    public synchronized Separation getSep(int page, int sep) {
        return getSepInternal(page, sep);
    }

    public void onSharedPreferenceChanged() {

        //Set ink thickness
        float inkThickness = storage.getInkThickness();
        setInkThickness(inkThickness);
        //Set colors
        int colorNumber;
        float height;
        float opacity;

        // Ink Setting
        colorNumber = storage.getInkColor();
//        if (colorNumber == 0)
//            setInkColor(1, 0, 0);
//        else
        setInkColor(ColorPalette.getR(colorNumber, Annotation.Type.INK),
                ColorPalette.getG(colorNumber, Annotation.Type.INK), ColorPalette.getB(colorNumber, Annotation.Type.INK));

        // Highlight Setting
        colorNumber = storage.getHighlightColor();
//        if (colorNumber == 0)
//            setHighlightColor(1, 1, 0);
//        else
        setHighlightColor(ColorPalette.getR(colorNumber, Annotation.Type.HIGHLIGHT),
                ColorPalette.getG(colorNumber, Annotation.Type.HIGHLIGHT), ColorPalette.getB(colorNumber, Annotation.Type.HIGHLIGHT));
        opacity = storage.getHighlightOpacity();
        setHighlightOpacity(opacity);
        height = storage.getHighlightHeight();
        setHighlightHeight(height);

        // Underline Setting
        colorNumber = storage.getUnderlineColor();
//        if (colorNumber == 0)
//            setUnderlineColor(0, 0, 1);
//        else
        setUnderlineColor(ColorPalette.getR(colorNumber, Annotation.Type.UNDERLINE),
                ColorPalette.getG(colorNumber, Annotation.Type.UNDERLINE), ColorPalette.getB(colorNumber, Annotation.Type.UNDERLINE));
        opacity = storage.getUnderlineOpacity();
        setUnderlineOpacity(opacity);
        height = storage.getUnderlineHeight();
        setUnderlineHeight(height);

        // Strikeout Setting
        colorNumber = storage.getStrikeoutColor();
//        if (colorNumber == 0)
//            setStrikeoutColor(1, 0, 0);
//        else
        setStrikeoutColor(ColorPalette.getR(colorNumber, Annotation.Type.STRIKEOUT),
                ColorPalette.getG(colorNumber, Annotation.Type.STRIKEOUT), ColorPalette.getB(colorNumber, Annotation.Type.STRIKEOUT));
        opacity = storage.getStrikeoutOpacity();
        setStrikeoutOpacity(opacity);
        height = storage.getStrikeoutHeight();
        setStrikeoutHeight(height);


        // Text Icon Setting
        colorNumber = storage.getTextIconColor();
        setTextAnnotIconColor(ColorPalette.getR(colorNumber, Annotation.Type.TEXT), ColorPalette.getG(colorNumber, Annotation.Type.TEXT), ColorPalette.getB(colorNumber, Annotation.Type.TEXT));
    }

    public void setInkColor() {
        int colorNumber = storage.getInkColor();
        setInkColor(ColorPalette.getR(colorNumber, Annotation.Type.INK),
                ColorPalette.getG(colorNumber, Annotation.Type.INK), ColorPalette.getB(colorNumber, Annotation.Type.INK));
    }

    public void setHighlightColor() {
        int colorNumber = storage.getHighlightColor();
//        if (colorNumber == 0)
//            setHighlightColor(1, 1, 0);
//        else
        setHighlightColor(ColorPalette.getR(colorNumber, Annotation.Type.HIGHLIGHT),
                ColorPalette.getG(colorNumber, Annotation.Type.HIGHLIGHT), ColorPalette.getB(colorNumber, Annotation.Type.HIGHLIGHT));
    }

    public void setUnderlineColor() {
        int colorNumber = storage.getUnderlineColor();
//        if (colorNumber == 0)
//            setUnderlineColor(0, 0, 1);
//        else
        setUnderlineColor(ColorPalette.getR(colorNumber, Annotation.Type.UNDERLINE),
                ColorPalette.getG(colorNumber, Annotation.Type.UNDERLINE), ColorPalette.getB(colorNumber, Annotation.Type.UNDERLINE));
    }

    public void setStrikeoutColor() {
        int colorNumber = storage.getStrikeoutColor();
//        if (colorNumber == 0)
//            setStrikeoutColor(1, 0, 0);
//        else
        setStrikeoutColor(ColorPalette.getR(colorNumber, Annotation.Type.STRIKEOUT),
                ColorPalette.getG(colorNumber, Annotation.Type.STRIKEOUT), ColorPalette.getB(colorNumber, Annotation.Type.STRIKEOUT));
    }

    public void setInkThickness() {
        float thickness = storage.getInkThickness();
        setInkThickness(thickness);
    }

}
