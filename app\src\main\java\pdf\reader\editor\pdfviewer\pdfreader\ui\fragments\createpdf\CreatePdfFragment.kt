package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.createpdf

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.itextpdf.html2pdf.HtmlConverter
import jp.wasabeef.richeditor.RichEditor
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.CreatePdfFragmentBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hideKeyboard
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.managepdf.AnnotationConstants
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.AnnotColors
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.AnnotateColorsBottomSheetFragment
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName
import java.io.File
import java.io.FileOutputStream

class CreatePdfFragment : BaseFragment<CreatePdfFragmentBinding, CreatePdfViewModel>() {

    override val viewModel: Class<CreatePdfViewModel>
        get() = CreatePdfViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater, container: ViewGroup?
    ): CreatePdfFragmentBinding =
        if (mViewModel.view == null) CreatePdfFragmentBinding.inflate(inflater, container, false)
        else CreatePdfFragmentBinding.bind(mViewModel.view!!)


    private lateinit var colorsSheetDialogFragment: AnnotateColorsBottomSheetFragment
    private var mEditor: RichEditor? = null
    private var mPreview: TextView? = null
    var pdfString: String = ""
    var pdfName: String = ""
    var selectedTextColor: Int = 0
    var selectedHighLightColor: Int = 0

    private var iv_bold_red = false
    private var iv_italic_red = false
    private var iv_bullets_red = false
    private var iv_number_red = false
    private var iv_alignLeft_red = false
    private var iv_alignCenter_red = false
    private var iv_alignRight_red = false
    private var iv_highlight_red = false
    private var iv_color_red = false
    private var iv_underline_red = false
    private var iv_strikeThrough_red = false

    private var annotationsBarVisiblity = false
    private var alignmentBarVisiblity = false
    private var annotateBarVisiblity = false
    private var isChanged = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        requireActivity().onBackPressedDispatcher.addCallback(this) {
//            mEditor?.hideKeyboard()
//        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewDataBinding.btnBack.setOnClickListener {
            isAlive {

                findNavController().popBackStack()
                mEditor?.hideKeyboard()
            }
        }
        newPdfWithrichEditor2()
        annotationsActions()
        saveNewPdf()
    }

    private fun annotationsActions() {
        mViewDataBinding.layoutEdit.setOnClickListener {
            if (!annotationsBarVisiblity) {
                selectEditAction()
                mViewDataBinding.layoutAlignmentBar.visibility = View.GONE
                alignmentBarVisiblity = false
                mViewDataBinding.layoutAnnotateBar.visibility = View.GONE
                annotateBarVisiblity = false
                mViewDataBinding.layoutAnnotationsBar.visibility = View.VISIBLE
                annotationsBarVisiblity = true
            } else {
                deSelectAction()
                mViewDataBinding.layoutAnnotationsBar.visibility = View.GONE
                annotationsBarVisiblity = false
            }
        }
        mViewDataBinding.layoutAlignment.setOnClickListener {
            if (!alignmentBarVisiblity) {
                selectAlignmentAction()
                mViewDataBinding.layoutAnnotationsBar.visibility = View.GONE
                annotationsBarVisiblity = false
                mViewDataBinding.layoutAnnotateBar.visibility = View.GONE
                annotateBarVisiblity = false
                mViewDataBinding.layoutAlignmentBar.visibility = View.VISIBLE
                alignmentBarVisiblity = true
            } else {
                deSelectAction()
                mViewDataBinding.layoutAlignmentBar.visibility = View.GONE
                alignmentBarVisiblity = false
            }
        }
        mViewDataBinding.layoutAnnotate.setOnClickListener {
            if (!annotateBarVisiblity) {
                selectAnnotateAction()
                mViewDataBinding.layoutAnnotationsBar.visibility = View.GONE
                annotationsBarVisiblity = false
                mViewDataBinding.layoutAlignmentBar.visibility = View.GONE
                alignmentBarVisiblity = false
                mViewDataBinding.layoutAnnotateBar.visibility = View.VISIBLE
                annotateBarVisiblity = true
            } else {
                deSelectAction()
                mViewDataBinding.layoutAnnotateBar.visibility = View.GONE
                annotateBarVisiblity = false
            }
        }

        mViewDataBinding.ivAnnotColor.setOnClickListener {
            showColorsSheet()
        }
    }

    private fun saveNewPdf() {
        mViewDataBinding.btnSave.setOnClickListener {
            if (pdfString.isEmpty()) {
                showToast(getString(R.string.pdf_save_dialog_nullwarning))
            } else {
                DialogNewPdfName.getInstance(pdfName) { it, fileName ->
                    when (it) {
                        ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                            pdfName = fileName
                            executeNewPdfSaving(fileName)
                        }

                        ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_special_charac),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.INVALID_EXTENSION -> {
                            Toast.makeText(
                                activity,
                                getString(R.string.txt_pdf_invalid_extension),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        ViewPdfActions.NULL_PDF_PASSWORD -> {
                            showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                        }

                        else -> {}
                    }
                }.show(childFragmentManager, "newFileDialog")
            }
            //wordToPdf()
        }
    }

    private fun executeNewPdfSaving(fileName: String) {
        lifecycleScope.launch(IO) {
            try {
                activity?.runOnUiThread {
                    loadingDialog.show()
                }
                val savePath =
                    Environment.getExternalStorageDirectory().absolutePath + AnnotationConstants.NEW_PDF_FILE
                val dir = File(savePath);
                if (!dir.exists()) {
                    dir.mkdirs()
                }
                val p = "$savePath/$fileName.pdf"
                HtmlConverter.convertToPdf(
                    pdfString, FileOutputStream(p)
                )
                activity?.sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            File(p)
                        )
                    )
                ).let {
                    activity?.runOnUiThread {
                        loadingDialog.dismiss()
                        mViewDataBinding.txtRecentTitle.text = fileName
                        showToast("$fileName is saved")
                        isAlive {
                            sharedViewModel.loadFiles(requireActivity())
                            findNavController().popBackStack()
                        }


                    }
//                MyPdfView.openPdfFile(
//                    activity as Activity, "PDF Created",
//                    "Do you want to open the pdf file?", p, requireContext()
//                )
                }
            } catch (e: Exception) {
                activity?.runOnUiThread {
                    loadingDialog.dismiss()
                    showToast("Failed to create File.")
                    isAlive {
                        findNavController().popBackStack()
                        it.sendFirebaseLog("create_screen", "Create Exception")
                    }

                }
            }

        }
    }

    private fun newPdfWithrichEditor2() {
        mEditor = view?.findViewById(R.id.editor);
        mEditor?.requestFocus()
        mEditor?.setEditorHeight(200);
        mEditor?.setEditorFontSize(16);
        if (isDarkTheme()) {
            mEditor?.setEditorFontColor(Color.WHITE);
        } else {
            mEditor?.setEditorFontColor(Color.BLACK);
        }


//        //mEditor.setEditorBackgroundColor(Color.BLUE);
//        //mEditor.setBackgroundColor(Color.BLUE);
//        //mEditor.setBackgroundResource(R.drawable.bg);
        mEditor?.setPadding(10, 10, 10, 10);
        //mEditor.setBackground("https://raw.githubusercontent.com/wasabeef/art/master/chip.jpg");
        mEditor?.setPlaceholder("Insert text here...")
        //mPreview = view?.findViewById(R.id.preview) as TextView
        mEditor?.setOnTextChangeListener { pdfString = it }
        //mPreview?.setText(it)
        mViewDataBinding.ivAnnotUndo.setOnClickListener { mEditor!!.undo() }
        mViewDataBinding.ivAnnotRedo.setOnClickListener { mEditor!!.redo() }
        mViewDataBinding.ivAnnotBold.setOnClickListener {
            iv_bold_red = if (iv_bold_red) {
                mViewDataBinding.ivAnnotBold.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setBold()
                false
            } else {
                mViewDataBinding.ivAnnotBold.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setBold()
                true
            }
        }
        mViewDataBinding.ivAnnotItalic.setOnClickListener(View.OnClickListener {
            iv_italic_red = if (iv_italic_red) {
                mViewDataBinding.ivAnnotItalic.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setItalic()
                false
            } else {
                mViewDataBinding.ivAnnotItalic.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setItalic()
                true
            }
        })

        mViewDataBinding.ivAnnotTextBroken.setOnClickListener(View.OnClickListener {
            iv_strikeThrough_red = if (iv_strikeThrough_red) {
                mViewDataBinding.ivAnnotTextBroken.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setStrikeThrough()
                false
            } else {
                mViewDataBinding.ivAnnotTextBroken.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setStrikeThrough()
                true
            }

        })
        mViewDataBinding.ivAnnotUnderline.setOnClickListener(View.OnClickListener {
            iv_underline_red = if (iv_underline_red) {
                mViewDataBinding.ivAnnotUnderline.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setUnderline()
                false
            } else {
                mViewDataBinding.ivAnnotUnderline.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setUnderline()
                true
            }

        })
        mViewDataBinding.ivAnnotColor.setOnClickListener(object : View.OnClickListener {
            private var isChanged = false
            override fun onClick(v: View) {
                mEditor!!.setTextColor(if (isChanged) Color.BLACK else Color.RED)
                isChanged = !isChanged
            }
        })
        mViewDataBinding.ivAnnotHighlight.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View) {
//
//                mEditor!!.setTextBackgroundColor(if (isChanged) Color.WHITE else Color.YELLOW)
//                isChanged = !isChanged
                showColorsSheet()
            }
        })

        mViewDataBinding.ivAnnotPosition.setOnClickListener(View.OnClickListener { mEditor!!.setIndent() })
        mViewDataBinding.ivAnnotOutdent.setOnClickListener(View.OnClickListener { mEditor!!.setOutdent() })
        mViewDataBinding.ivAnnotAlignmentLeft.setOnClickListener(View.OnClickListener {
            iv_alignLeft_red = if (iv_alignLeft_red) {
                mViewDataBinding.ivAnnotAlignmentLeft.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setAlignLeft()
                false
            } else {
                mViewDataBinding.ivAnnotAlignmentLeft.setColorFilter(resources.getColor(R.color.default_red_light))
                mViewDataBinding.ivAnnotAlignmentRight.setColorFilter(resources.getColor(R.color.default_light_black))
                mViewDataBinding.ivAnnotAlignmentCenter.setColorFilter(resources.getColor(R.color.default_light_black))
                iv_alignCenter_red = false
                iv_alignRight_red = false
                mEditor!!.setAlignLeft()
                true
            }
        })
        mViewDataBinding.ivAnnotAlignmentCenter.setOnClickListener(View.OnClickListener {
            iv_alignCenter_red = if (iv_alignCenter_red) {
                mViewDataBinding.ivAnnotAlignmentCenter.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setAlignCenter()
                false
            } else {
                mViewDataBinding.ivAnnotAlignmentCenter.setColorFilter(resources.getColor(R.color.default_red_light))
                mViewDataBinding.ivAnnotAlignmentRight.setColorFilter(resources.getColor(R.color.default_light_black))
                mViewDataBinding.ivAnnotAlignmentLeft.setColorFilter(resources.getColor(R.color.default_light_black))
                iv_alignRight_red = false
                iv_alignLeft_red = false
                mEditor!!.setAlignCenter()
                true
            }
        })
        mViewDataBinding.ivAnnotAlignmentRight.setOnClickListener(View.OnClickListener {
            iv_alignRight_red = if (iv_alignRight_red) {
                mViewDataBinding.ivAnnotAlignmentRight.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setAlignRight()
                false
            } else {
                mViewDataBinding.ivAnnotAlignmentRight.setColorFilter(resources.getColor(R.color.default_red_light))
                mViewDataBinding.ivAnnotAlignmentLeft.setColorFilter(resources.getColor(R.color.default_light_black))
                mViewDataBinding.ivAnnotAlignmentCenter.setColorFilter(resources.getColor(R.color.default_light_black))
                iv_alignLeft_red = false
                iv_alignCenter_red = false
                mEditor!!.setAlignRight()
                true
            }
        })
        mViewDataBinding.ivAnnotDot.setOnClickListener(View.OnClickListener {
            iv_bullets_red = if (iv_bullets_red) {
                mViewDataBinding.ivAnnotDot.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setBullets()
                false
            } else {
                mViewDataBinding.ivAnnotDot.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setBullets()
                true
            }
            mViewDataBinding.ivAnnotNum.setColorFilter(resources.getColor(R.color.default_light_black))
        })
        mViewDataBinding.ivAnnotNum.setOnClickListener {
            iv_number_red = if (iv_number_red) {
                mViewDataBinding.ivAnnotNum.setColorFilter(resources.getColor(R.color.default_light_black))
                mEditor!!.setNumbers()
                false
            } else {
                mViewDataBinding.ivAnnotNum.setColorFilter(resources.getColor(R.color.default_red_light))
                mEditor!!.setNumbers()
                true
            }
            mViewDataBinding.ivAnnotDot.setColorFilter(resources.getColor(R.color.default_light_black))
        }


//        action_subscript.setOnClickListener(View.OnClickListener { mEditor!!.setSubscript() })
//        action_superscript.setOnClickListener(View.OnClickListener { mEditor!!.setSuperscript() })

//        action_heading1.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                1
//            )
//        })
//
//        action_heading2.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                2
//            )
//        })
//
//        action_heading3.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                3
//            )
//        })
//
//        action_heading4.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                4
//            )
//        })
//
//        action_heading5.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                5
//            )
//        })
//
//        action_heading6.setOnClickListener(View.OnClickListener {
//            mEditor!!.setHeading(
//                6
//            )
//        })
//


        //action_blockquote.setOnClickListener(View.OnClickListener { mEditor!!.setBlockquote() })


//        action_insert_image.setOnClickListener(View.OnClickListener {
//            mEditor!!.insertImage(
//                "https://raw.githubusercontent.com/wasabeef/art/master/chip.jpg",
//                "dachshund", 320
//            )
//        })
//
//        action_insert_youtube.setOnClickListener(View.OnClickListener {
//            mEditor!!.insertYoutubeVideo(
//                "https://www.youtube.com/embed/pS5peqApgUA"
//            )
//        })
//
//        action_insert_audio.setOnClickListener(View.OnClickListener {
//            mEditor!!.insertAudio(
//                "https://file-examples-com.github.io/uploads/2017/11/file_example_MP3_5MG.mp3"
//            )
//        })
//
//        action_insert_video.setOnClickListener(View.OnClickListener {
//            mEditor!!.insertVideo(
//                "https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/1080/Big_Buck_Bunny_1080_10s_10MB.mp4",
//                360
//            )
//        })
//
//        action_insert_link.setOnClickListener(View.OnClickListener {
//            mEditor!!.insertLink(
//                "https://github.com/wasabeef",
//                "wasabeef"
//            )
//        })
//        action_insert_checkbox.setOnClickListener(View.OnClickListener { mEditor!!.insertTodo() })
    }

    private fun wordToPdf() = try {
        println("-------------------------------------->Inter")
        val intent = Intent().setType("*/*").setAction(Intent.ACTION_GET_CONTENT)
        startActivityForResult(Intent.createChooser(intent, "Select a file"), 111)
        println("-------------------------------------->Select File")
    } catch (e: Exception) {
        e.printStackTrace()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 111 && resultCode == RESULT_OK) {
            println("-------------------------------------->File Featched :${File(data?.data.toString())}")

            try {
                var uri: Uri? = data?.data


//                CoroutineScope(Dispatchers.IO).launch {
//                        //val realPathFromURI = requireActivity().getRealPathFromURI(uri)
//
//
//                    val path = async(Dispatchers.IO) { activity?.getRealPathFromURI(uri) }
//                    path.await()
//                        ?.let { activity?.handlerViaRealPath(it) { filename, fileUri, fileSize ->} }
//
//                }


//                var realPath = MyFileUtils.getPath(requireContext(), uri).toString()
//
//
//
//                val docFile: InputStream =
//                    FileInputStream(realPath)
//                val doc = XWPFDocument(docFile)
//                val pdfOptions = PdfOptions.create()
//
//                val savePath = Environment.getExternalStorageDirectory()
//                    .getAbsolutePath() + AnnotationConstants.WORD_TO_PDF_FILE
//                val dir = File(savePath);
//                if (!dir.exists()) {
//                    dir.mkdirs()
//                }
//                val p = "$savePath/Deed.pdf"
//
//                val out: OutputStream =
//                    FileOutputStream(File(p))
//                PdfConverter.getInstance().convert(doc, out, pdfOptions)
//                out.close()
//                println("-------------------------------------->Done")
            } catch (ex: Exception) {
                //showToast(ex.localizedMessage)
                Log.i("init", "exception---------->: ${ex.localizedMessage}")
            }
        }


    }

    private fun showColorsSheet() {
        try {
            colorsSheetDialogFragment = AnnotateColorsBottomSheetFragment.getInstance(
                selectedTextColor, selectedHighLightColor, isDarkTheme()
            ) { color ->
                when (color) {
                    AnnotColors.colorBlack -> {
                        if (isDarkTheme()) {
                            mViewDataBinding.ivAnnotColor.setColorFilter(Color.WHITE)
                            mEditor?.setEditorFontColor(Color.WHITE);
                        } else {
                            mViewDataBinding.ivAnnotColor.setColorFilter(Color.BLACK)
                            mEditor?.setEditorFontColor(Color.BLACK);
                        }
                        colorsSheetDialogFragment.dismiss()
                        selectedTextColor = 0
                    }
                    AnnotColors.colorRed -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotColor.setColorFilter(Color.RED)
                        mEditor?.setEditorFontColor(Color.RED);
                        selectedTextColor = 1
                    }
                    AnnotColors.colorYellow -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotColor.setColorFilter(Color.YELLOW)
                        mEditor?.setEditorFontColor(Color.YELLOW);
                        selectedTextColor = 2
                    }
                    AnnotColors.colorGreen -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotColor.setColorFilter(Color.GREEN)
                        mEditor?.setEditorFontColor(Color.GREEN);
                        selectedTextColor = 3
                    }
                    AnnotColors.colorBlue -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.BLUE)
                        mEditor?.setEditorFontColor(Color.BLUE);
                        selectedTextColor = 4
                    }


                    AnnotColors.colorBlackH -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.BLACK)
                        mEditor!!.setTextBackgroundColor(Color.WHITE)
                        //isChanged = !isChanged
                        selectedHighLightColor = 0
                    }
                    AnnotColors.colorRedH -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.RED)
                        mEditor!!.setTextBackgroundColor(Color.RED)
                        selectedHighLightColor = 1
                    }
                    AnnotColors.colorYellowH -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.YELLOW)
                        mEditor!!.setTextBackgroundColor(Color.YELLOW)
                        selectedHighLightColor = 2
                    }
                    AnnotColors.colorGreenH -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.GREEN)
                        mEditor!!.setTextBackgroundColor(Color.GREEN)
                        selectedHighLightColor = 3
                    }
                    AnnotColors.colorBlueH -> {
                        colorsSheetDialogFragment.dismiss()
                        mViewDataBinding.ivAnnotHighlight.setColorFilter(Color.BLUE)
                        mEditor!!.setTextBackgroundColor(Color.BLUE)
                        selectedHighLightColor = 4
                    }
                }
            }
            colorsSheetDialogFragment?.let {
                val fragmentManager = childFragmentManager
                fragmentManager.executePendingTransactions()
                val addTransaction = fragmentManager.beginTransaction()
                if (it.isAdded) {
                    val removeTransaction: FragmentTransaction = fragmentManager.beginTransaction()
                    removeTransaction.remove(it)
                    removeTransaction.commitNow()
                }
                addTransaction.add(it, "")
                addTransaction.commitNow()
            }
        } catch (ex: Exception) {

        }
    }

    private fun selectEditAction() {
        mViewDataBinding.ivAnnotEdit.setColorFilter(resources.getColor(R.color.default_red_light))
        mViewDataBinding.ivAnnotAlignment.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAlignmentLeft.setColorFilter(resources.getColor(R.color.default_light_black))
    }

    private fun selectAlignmentAction() {
        mViewDataBinding.ivAnnotEdit.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAlignment.setColorFilter(resources.getColor(R.color.default_red_light))
        mViewDataBinding.ivAnnotAnnotate.setColorFilter(resources.getColor(R.color.default_light_black))
    }

    private fun selectAnnotateAction() {
        mViewDataBinding.ivAnnotEdit.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAlignment.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAnnotate.setColorFilter(resources.getColor(R.color.default_red_light))
    }

    private fun deSelectAction() {
        mViewDataBinding.ivAnnotEdit.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAlignment.setColorFilter(resources.getColor(R.color.default_light_black))
        mViewDataBinding.ivAnnotAnnotate.setColorFilter(resources.getColor(R.color.default_light_black))
    }


}