package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.wordtopdf.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.databinding.WordItemLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.hide
import pdf.reader.editor.pdfviewer.pdfreader.extensions.show

class WordAdapter(
    private val onPDFClick: (DocumentsModel) -> Unit
) : RecyclerView.Adapter<WordAdapter.ViewHolder>() {

    private var recentFilesList: ArrayList<DocumentsModel> = ArrayList()
    private var lastSelected = -1


    override fun onCreateViewHolder(parent: <PERSON>Group, viewType: Int): ViewHolder {
        val binding: WordItemLayoutBinding =
            WordItemLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val pdfStoreItems = recentFilesList[position]

        holder.binding.apply {
            txtFileName.text = pdfStoreItems.fileName
            txtFileSize.text = pdfStoreItems.fileSize
            txtFileDate.text = pdfStoreItems.fileDate
        }

        holder.binding.root.setOnClickListener {
            onPDFClick(pdfStoreItems)
            val pos = lastSelected
            lastSelected = position
            notifyItemChanged(pos)
            notifyItemChanged(lastSelected)
        }

        if (position == lastSelected)
            holder.binding.imgSelected
                .setImageResource(R.drawable.ic_selected)
        else
            holder.binding.imgSelected
                .setImageResource(R.drawable.ic_unselected)
    }

    override fun getItemCount(): Int = recentFilesList.size

    fun setData(filesList: List<DocumentsModel>) {
        recentFilesList.clear()
        recentFilesList.addAll(filesList)
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: WordItemLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)

}