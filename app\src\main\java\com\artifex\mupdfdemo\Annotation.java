package com.artifex.mupdfdemo;

import android.graphics.RectF;

public class Annotation extends RectF {
   public enum Type {

        TEXT, LINK, FREETEXT, LINE, SQUAR<PERSON>, CIRCLE, <PERSON><PERSON><PERSON>G<PERSON>, POLYLINE, <PERSON><PERSON><PERSON><PERSON>HT,
        UN<PERSON>RL<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>O<PERSON>, <PERSON>AM<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>K, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ME<PERSON>,
        SOUND, MOVIE, WIDGET, SCREEN, PRINTERMARK, TRAPNET, WATERMARK, A3D, UNKNOWN
    }

    public final Type type;

    public Annotation(float x0, float y0, float x1, float y1, int _type) {
        super(x0, y0, x1, y1);
        type = _type == -1 ? Type.UNKNOWN : Type.values()[_type];
    }
}
