{"buildFiles": ["C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"adnan::@6890427a1f51a3e7e1df": {"artifactName": "<PERSON>nan", "abi": "x86_64", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\build\\intermediates\\cxx\\Debug\\6m3e1q4f\\obj\\x86_64\\libadnan.so", "runtimeFiles": []}, "mupdfcore::@6890427a1f51a3e7e1df": {"artifactName": "mupdfcore", "abi": "x86_64", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\x86_64\\libmupdfcore.a", "runtimeFiles": []}, "mupdfthirdparty::@6890427a1f51a3e7e1df": {"artifactName": "mupdfthirdparty", "abi": "x86_64", "output": "C:\\Users\\<USER>\\StudioProjects\\pdf-editor9D\\app\\.cxx\\Debug\\6m3e1q4f\\x86_64\\libmupdfthirdparty.a", "runtimeFiles": []}}}