package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.home

import android.app.Activity
import com.google.android.gms.ads.nativead.NativeAd
import dagger.hilt.android.lifecycle.HiltViewModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel
import pdf.reader.editor.pdfviewer.pdfreader.extensions.checkInternetConnection
import pdf.reader.editor.pdfviewer.pdfreader.local.database.RepositoryLocal
import pdf.reader.editor.pdfviewer.pdfreader.manager.ads.getNativeAdObject
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(private val local: RepositoryLocal) : BaseViewModel() {
    var isFirstTime = true
    private var isAdReqSent = false
    var nativeAd: NativeAd? = null

    fun loadAd(activity: Activity, isConsentDone: Boolean, onResult: (NativeAd) -> Unit) {
        if (!isAdReqSent) {
            isAdReqSent = true
            if (activity.checkInternetConnection() && local.isHomeNativeEnable && isConsentDone) {

                activity.getNativeAdObject(
                    "activity.getString(R.string.home_native_ad_id)",
                    onResult = {
                        nativeAd = it
                        onResult(nativeAd!!)
                    })
            }

        } else if (nativeAd != null) {
            onResult(nativeAd!!)
        }

    }

//    init {
//        getPDFSortingType()
//    }
//
//    private val searchedText = MutableLiveData("")
//    private val selectionArgs = MutableLiveData(SELECTION_PDF)
//    var pdfSortingType = SortOrder.FileSortOrder.FILE_A_Z
//
//    private val documentsList = MutableLiveData<List<DocumentsModel>>()
//
//    fun loadFiles(context: FragmentActivity) = viewModelScope.launch {
//        val obj = object : LoaderManager.LoaderCallbacks<Cursor> {
//            override fun onCreateLoader(id: Int, args: Bundle?): Loader<Cursor> {
//
//                try {
//                    println("Loading files")
//
//                    val projection = arrayOf(
//                        MediaStore.Files.FileColumns.DATA,
//                        MediaStore.Files.FileColumns.TITLE,
//                        MediaStore.Files.FileColumns.DISPLAY_NAME,
//                        MediaStore.Files.FileColumns.SIZE,
//                        MediaStore.Files.FileColumns.DATE_ADDED,
//                        MediaStore.Files.FileColumns.DATE_MODIFIED,
//                        MediaStore.Files.FileColumns.MIME_TYPE
//                    )
//
////            val order = if (sortBy.value == SORT_BY_NAME) {
////                "${sortBy.value} ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}" +
////                        "," +
////                        "$SORT_BY_TITLE ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}"
////            } else {
////                "${sortBy.value} ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}"
////            }
//
//
//                    return CursorLoader(
//                        context,
//                        MediaStore.Files.getContentUri("external"),
//                        projection,
//                        selectionArgs.value?.first,
//                        selectionArgs.value?.second,
//                        null
//                    )
//                } catch (e: Exception) {
//
//                    try {
//
//                        val projection = arrayOf(
//                            MediaStore.Files.FileColumns.DATA,
//                            MediaStore.Files.FileColumns.TITLE,
//                            MediaStore.Files.FileColumns.DISPLAY_NAME,
//                            MediaStore.Files.FileColumns.SIZE,
//                            MediaStore.Files.FileColumns.DATE_ADDED,
//                            MediaStore.Files.FileColumns.DATE_MODIFIED,
//                            MediaStore.Files.FileColumns.MIME_TYPE
//                        )
//
////                val order = if (sortBy.value == SORT_BY_NAME) {
////                    "${sortBy.value} ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}" +
////                            "," +
////                            "$SORT_BY_TITLE ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}"
////                } else {
////                    "${sortBy.value} ${if (isASC.value == true) " COLLATE NOCASE ASC" else " COLLATE NOCASE DESC"}"
////                }
//
//
//                        return CursorLoader(
//                            context,
//                            MediaStore.Files.getContentUri("external"),
//                            projection,
//                            selectionArgs.value?.first,
//                            selectionArgs.value?.second,
//                            null
//                        )
//
//                    } catch (e: Exception) {
//                        return CursorLoader(context)
//                    }
//                }
//
//            }
//
//            override fun onLoadFinished(loader: Loader<Cursor>, data: Cursor?) {
//
//                println("Loading finished")
//
//                data?.let {
//
//                    println(" files size; ${it.count}")
//
//                    var docxFiles = 0
//                    var pdfFiles = 0
//                    var xlsFiles = 0
//                    var pptFiles = 0
//                    var txtFiles = 0
//                    val arrayList = arrayListOf<DocumentsModel>()
//
//                    CoroutineScope(Dispatchers.IO).launch {
//
//                        try {
//                            while (it.moveToNext()) {
//                                val model = setModel(it)
//
////                        log("HomeViewModel", "fileName: ${model.fileName} at position: ${it.position}")
//
//                                try {
//
//                                    if (model.sizeInDigit > 0 && File(model.absolutePath).exists()) {
//
//                                        val text = searchedText.value?.toString()
//
//                                        if (text?.isNotEmpty() == true) {
//                                            if (model.fileName.contains(text, true)) {
//                                                when (model.fileMimeType) {
//                                                    DOC, DOCX, DOC_CONSTANT, DOCX_CONSTANT -> ++docxFiles
//                                                    PDF, PDF_CONSTANT -> ++pdfFiles
//                                                    PPT, PPTX, PPT_MSX, PPT_MS, PPT_SIMPLE, PPT_CONSTANT, PPTX_CONSTANT -> ++pptFiles
//                                                    XLS, XLSX, XLS_SIMPLE, XLS_X, XLS_MSX, XLS_CONSTANT, XLSX_CONSTANT -> ++xlsFiles
//                                                    TXT, TXT_CONSTANT -> ++txtFiles
//                                                }
//                                                arrayList.add(model)
//                                            }
//                                        } else {
//                                            when (model.fileMimeType) {
//                                                DOC, DOCX, DOC_CONSTANT, DOCX_CONSTANT -> ++docxFiles
//                                                PDF, PDF_CONSTANT -> ++pdfFiles
//                                                PPT, PPTX, PPT_MSX, PPT_MS, PPT_SIMPLE, PPT_CONSTANT, PPTX_CONSTANT -> ++pptFiles
//                                                XLS, XLSX, XLS_SIMPLE, XLS_X, XLS_MSX, XLS_CONSTANT, XLSX_CONSTANT -> ++xlsFiles
//                                                TXT, TXT_CONSTANT -> ++txtFiles
//                                            }
//                                            arrayList.add(model)
//                                        }
//
//                                    }
//
//                                } catch (e: Exception) {
//                                    e.printStackTrace()
//                                }
//                            }
//
//                            println("array size : ${arrayList.size}")
//
//                            //move to top
//                            it.moveToPosition(-1)
//
//                            sortAllFiles(arrayList)
//
//
//                        } catch (e: Exception) {
//                            println("HomeViewModel: " + "exception: ${e.message}")
//                        }
//
//                    }
//
//                }
//            }
//
//            override fun onLoaderReset(loader: Loader<Cursor>) {
//            }
//        }
//
//        LoaderManager.getInstance(context).initLoader(
//            1,
//            null,
//            obj
//        )
//    }
//
//    private fun setModel(cursor: Cursor): DocumentsModel {
//        val pdfModel = DocumentsModel()
//        try {
//
//            pdfModel.absolutePath =
//                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA))
//
//            try {
//                val name: String =
//                    cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME))
//                val filename = name.replace(".pdf", "")
//                val newFName = filename.replace(".PDF", "")
//                pdfModel.fileName = newFName
//            } catch (e: Exception) {
//                try {
////                    pdfModel.fileName = cursor.getString(
////                        cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.TITLE)
////                    )
//                    val name: String =
//                        cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.TITLE))
//                    val filename = name.replace(".pdf", "")
//                    val newFName = filename.replace(".PDF", "")
//                    pdfModel.fileName = newFName
//                } catch (e: Exception) {
//                    pdfModel.fileName = "Unknown"
//                }
//            }
//
//            pdfModel.sizeInDigit = cursor.getLong(
//                cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE)
//            )
//
//            pdfModel.fileSize = getReadableSize(
//                cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE))
//            )
//            pdfModel.dateInDigit = cursor.getLong(
//                cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
//            )
//
//            pdfModel.fileDate = getFormattedDate(
//                cursor.getLong(
//                    cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
//                )
//            )
//
//            try {
//                pdfModel.fileMimeType = cursor.getString(
//                    cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE)
//                )
//            } catch (e: Exception) {
//                val file = Uri.fromFile(File(pdfModel.absolutePath))
//                pdfModel.fileMimeType = MimeTypeMap.getFileExtensionFromUrl(file.toString())
//            }
//
////            log("HomeViewModel", "fileMimeType - ${pdfModel.fileMimeType}")
//
//            if (pdfModel.absolutePath.isNotEmpty()) {
//                val file = File(pdfModel.absolutePath)
//                pdfModel.parentFile = file.parent ?: ""
//            }
//
//        } catch (ex: Exception) {
//            println("HomeViewModel: " + "exception - ${ex.message}")
//            ex.printStackTrace()
//        }
//        return pdfModel
//    }
//
//    fun getDocuments(): MutableLiveData<List<DocumentsModel>> {
//        return documentsList
//    }
//
//    private fun getPDFSortingType() = viewModelScope.launch {
//        dataStoreProvider.observeSelectedPdfSorting.collect {
//            pdfSortingType = it
//            if (documentsList.value != null && documentsList.value?.isNotEmpty() == true) {
//                sortAllFiles(documentsList.value!!)
//            }
//        }
//    }
//
//    fun sortAllFiles(files: List<DocumentsModel>) {
//        val collator = Collator.getInstance()
//        when (pdfSortingType) {
//
//            SortOrder.FileSortOrder.FILE_A_Z -> {
//                val result = files.sortedWith { a1, a2 ->
//                    collator.compare(
//                        a1.fileName,
//                        a2.fileName
//                    )
//                }
//
//                documentsList.postValue(result)
//
//
//            }
//            SortOrder.FileSortOrder.FILE_Z_A -> {
//                val result = files.sortedWith { a1, a2 ->
//                    collator.compare(
//                        a2.fileName,
//                        a1.fileName
//                    )
//                }
//
//                documentsList.postValue(result)
//            }
//            SortOrder.FileSortOrder.FILE_DATE_A -> {
//
//                val result = files.sortedBy { it.dateInDigit }
//                documentsList.postValue(result)
//            }
//            SortOrder.FileSortOrder.FILE_DATE_D -> {
//
//                val result = files.sortedByDescending { it.dateInDigit }
//                documentsList.postValue(result)
//            }
//            SortOrder.FileSortOrder.FILE_SIZE_A -> {
//
//                val result = files.sortedBy { it.sizeInDigit }
//                documentsList.postValue(result)
//            }
//            SortOrder.FileSortOrder.FILE_SIZE_Z -> {
//
//                val result = files.sortedByDescending { it.sizeInDigit }
//                documentsList.postValue(result)
//            }
//        }
//
//    }
//
//    fun setPDFSortingType(order: String) = viewModelScope.launch {
//        dataStoreProvider.setPDFSortingType(order)
//    }
}