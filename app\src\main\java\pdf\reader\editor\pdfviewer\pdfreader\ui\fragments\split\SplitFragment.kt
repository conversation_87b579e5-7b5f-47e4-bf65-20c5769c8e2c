package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.split

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.itextpdf.text.Document
import com.itextpdf.text.pdf.PdfCopy
import com.itextpdf.text.pdf.PdfReader
import com.shockwave.pdfium.PdfPasswordException
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pdf.reader.editor.pdfviewer.pdfreader.R
import pdf.reader.editor.pdfviewer.pdfreader.anchors.DocumentsModel
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseFragment
import pdf.reader.editor.pdfviewer.pdfreader.databinding.FragmentSplitBinding
import pdf.reader.editor.pdfviewer.pdfreader.databinding.SplitSheetLayoutBinding
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getFormattedDate
import pdf.reader.editor.pdfviewer.pdfreader.extensions.getReadableSize
import pdf.reader.editor.pdfviewer.pdfreader.extensions.isAlive
import pdf.reader.editor.pdfviewer.pdfreader.extensions.sendFirebaseLog
import pdf.reader.editor.pdfviewer.pdfreader.extensions.showCreateSheet
import pdf.reader.editor.pdfviewer.pdfreader.manager.FileUtils
import pdf.reader.editor.pdfviewer.pdfreader.manager.MyBottomSheet
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.GridSpacingItemDecoration
import pdf.reader.editor.pdfviewer.pdfreader.rawcollections.enumm.ViewPdfActions
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.tool.ToolsActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.activity.viewer.ViewerActivity
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogNewPdfName
import pdf.reader.editor.pdfviewer.pdfreader.ui.dialogs.DialogPdfPassword
import pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.split.adapter.SplitAdapter
import java.io.File
import java.io.FileOutputStream
import java.io.IOException


@AndroidEntryPoint
class SplitFragment : BaseFragment<FragmentSplitBinding, SplitViewModel>() {

    override val viewModel: Class<SplitViewModel>
        get() = SplitViewModel::class.java

    override fun getViewBinding(
        inflater: LayoutInflater, container: ViewGroup?
    ): FragmentSplitBinding =
        if (mViewModel.view == null) FragmentSplitBinding.inflate(inflater, container, false)
        else FragmentSplitBinding.bind(mViewModel.view!!)

    //    private val args: SplitFragmentArgs by navArgs()
    private var splitAdapter: SplitAdapter? = null
    private var documentsModel: DocumentsModel? = null
    private var selectedPages: List<PhotoModel> = ArrayList()
    private var isSingleFile = true
    private var password: String = ""
    private var fileName = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {

        super.onViewCreated(view, savedInstanceState)
        initViews()

        mViewDataBinding.btnSplitFiles.setOnClickListener {
            if (selectedPages.isNotEmpty()) splitFiles()
            else showToast(getString(R.string.please_select_pages))
        }

        mViewDataBinding.imgBack.setOnClickListener {
            if (activity != null && activity is ToolsActivity)
                activity?.finish()
            else
                isAlive {
                    findNavController().popBackStack()
                }
        }

    }

    private fun initViews() {
//        documentsModel = args.pdfFile
        Log.d("TAG", "initViews: ${sharedViewModel.selectedDoc}")
        documentsModel = sharedViewModel.selectedDoc
        password = documentsModel?.password ?: ""
        splitAdapter = SplitAdapter(0) {
            this.selectedPages = it
        }
        isAlive {
            mViewDataBinding.splitRecyclerView.apply {

                layoutManager = GridLayoutManager(it, 2)
                adapter = splitAdapter
                addItemDecoration(
                    GridSpacingItemDecoration(
                        2, 50, true
                    )
                )
            }
        }


//        mViewDataBinding.searchView.setInputType(InputType.TYPE_CLASS_NUMBER)
//
//        mViewDataBinding.searchView.initToggleListener {
//            if (it) {
//                mViewDataBinding.searchView.showKeyboard()
//            } else {
//                mViewDataBinding.searchView.hideKeyboard()
//                val text = mViewDataBinding.searchView.getEditText().text.trim().toString()
//                if (text.isNotEmpty())
//                    splitAdapter?.filter?.filter("")
//
//            }
//        }
//
//        mViewDataBinding.searchView.setOnSearchListener {
//
//            requireActivity().runOnUiThread {
//                try {
//                    splitAdapter?.filter?.filter(it)
//                } catch (ex: Exception) {
//                    Toast.makeText(
//                        requireContext(),
//                        ex.localizedMessage,
//                        Toast.LENGTH_SHORT
//                    )
//                        .show()
//                }
//            }
//
//
//        }

        loadPages()

    }

    private fun loadPages() {
        isAlive {
            val file = File(documentsModel?.absolutePath!!)
            if (file.exists() && file.length() > 0) {

                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        FileUtils.getPDFPagesThumbnail(
                            documentsModel?.absolutePath!!, it, password
                        ).collect {
                            withContext(Dispatchers.Main) {

                                splitAdapter?.setData(it.second, it.first)
                            }
                        }
                    } catch (e: PdfPasswordException) {
                        withContext(Dispatchers.Main) {
                            DialogPdfPassword.getInstance { action, password ->
                                when (action) {
                                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                                        <EMAIL> = password
                                        loadPages()

                                    }

                                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                                        showToast(getString(R.string.txt_pdf_null_password_warning))
                                    }

                                    ViewPdfActions.PDF_PASSWORD_CANCEL_CLICKED -> {
                                        isAlive {
                                            findNavController().popBackStack()
                                        }


                                    }

                                    else -> {}
                                }
                            }.show(childFragmentManager, "Password Dialog")
                        }
                    } catch (e: IOException) {
                        withContext(Dispatchers.Main) {

                            showToast(getString(R.string.file_is_corrupt_or_not_pdf))
                        }
                    } finally {
                        withContext(Dispatchers.Main) {

                            showToast(getString(R.string.file_is_corrupt_or_not_pdf))
                        }
                    }
                }
            } else showToast(getString(R.string.file_not_exist_or_empty))
        }

    }

    private fun showBottomSheet() {
        val binding: SplitSheetLayoutBinding = SplitSheetLayoutBinding.inflate(
            LayoutInflater.from(activity), null, false
        )

        binding.splitActionGroup.check(if (isSingleFile) R.id.radioSplitSingle else R.id.radioSplitSep)

        binding.imgBack.setOnClickListener {
            MyBottomSheet.getInstance(activity)?.dismissDialog()
        }

        binding.splitActionGroup.setOnCheckedChangeListener { _, checkedId ->

            when (checkedId) {
                R.id.radioSplitSep -> {
                    isSingleFile = false
                }

                R.id.radioSplitSingle -> {
                    isSingleFile = true
                }

            }
        }

        binding.btnSplitExport.setOnClickListener {
            MyBottomSheet.getInstance(activity)?.dismissDialog()

            DialogNewPdfName.getInstance(fileName) { action, fileName ->

                when (action) {
                    ViewPdfActions.VERIFY_PASSWORD_CLICKED -> {
                        this.fileName = fileName
                        splitFiles()
                    }

                    ViewPdfActions.SPECIAL_CHARACTER_VOILATION -> {
                        Toast.makeText(
                            activity, getString(R.string.txt_pdf_special_charac), Toast.LENGTH_LONG
                        ).show()
                    }

                    ViewPdfActions.INVALID_EXTENSION -> {
                        Toast.makeText(
                            activity,
                            getString(R.string.txt_pdf_invalid_extension),
                            Toast.LENGTH_LONG
                        ).show()
                    }

                    ViewPdfActions.NULL_PDF_PASSWORD -> {
                        showToast(getString(R.string.pdf_save_dialog_name_nullwarning))
                    }

                    else -> {}
                }
            }.show(childFragmentManager, "Split Files")


        }

        MyBottomSheet.getInstance(activity)?.setContentView(binding.root, isCancelable = true)
            ?.showDialog()
    }

    private fun splitFiles() {
        try {
            val reader = PdfReader(documentsModel?.absolutePath!!, password.toByteArray())
            val document = Document(reader.getPageSizeWithRotation(1))
            saveSingleFile(document, reader)
        } catch (e: Exception) {
            showToast(getString(R.string.don_t_have_access_to_split))
            activity?.sendFirebaseLog("Split_Screen_Start", "Failed to split")
        }

//        if (isSingleFile)

//        else
//            saveMultipleFiles(document, reader)
    }

    private fun saveSingleFile(document: Document, reader: PdfReader) {
        loadingDialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val splitFile = FileUtils.saveFileToExternalStorage(fileName)
                val writer = PdfCopy(document, FileOutputStream(splitFile))
                document.open()
                selectedPages.forEachIndexed { _, photoModel ->
                    val page = writer.getImportedPage(reader, photoModel.position + 1)
                    writer.addPage(page)
                }
                document.close()
                writer.close()
                activity?.sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                            splitFile
                        )
                    )
                )
                withContext(Dispatchers.Main) {
                    isAlive {
//                        sharedViewModel.loadFiles(requireActivity())
                        loadingDialog.hide()
                        it.showCreateSheet(splitFile?.absolutePath!!) { flag ->
                            if (flag) {
                                isAlive { con ->

                                    val doc = DocumentsModel()
                                    doc.absolutePath = splitFile.absolutePath
                                    doc.fileName = splitFile.name
                                    doc.parentFile = splitFile.parent
                                    doc.sizeInDigit = splitFile.length()
                                    doc.dateInDigit = splitFile.lastModified()
                                    doc.fileDate = con.getFormattedDate(splitFile.lastModified())
                                    doc.fileSize = con.getReadableSize(splitFile.length())

                                    sharedViewModel.selectedDoc = doc

                                    isAlive { activity ->
                                        val intent = Intent(activity, ViewerActivity::class.java)
                                        intent.flags =
                                            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                                        intent.putExtra("pdf_file", doc)
                                        activity.startActivity(intent)
                                        if (activity is ToolsActivity)
                                            activity.finish()
                                        else
                                            findNavController().popBackStack()

                                    }
//                                    if (isFragmentInBackStack(R.id.action_homeFragment_to_pdfViewFragment)) {
//                                        findNavController().popBackStack()
//                                    } else {
//                                        val action =
//                                            SplitFragmentDirections.actionSplitFragmentToPdfViewFragment()
//                                        findNavController().navigate(action)
//                                    }
//                                    con.openFile(splitFile.parent!!)
//                                    findNavController().popBackStack()
                                }
                            } else {

                                findNavController().popBackStack()
                            }


                        }

                    }

                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
//                    sharedViewModel.loadFiles(requireActivity())
                    loadingDialog.hide()
                    showToast(getString(R.string.failed_to_split))
//                    findNavController().popBackStack()
                    activity?.sendFirebaseLog("Split_Screen", "Failed to split")
                }

            }

        }
    }

    private fun saveMultipleFiles(document: Document, reader: PdfReader) {
        val splitPaths = ArrayList<File>()
        selectedPages.forEachIndexed { _, photoModel ->
            val splitFile = FileUtils.saveFileToExternalStorage()
            val writer = PdfCopy(document, FileOutputStream(splitFile))
            document.open()
            val page = writer.getImportedPage(reader, photoModel.position + 1)
            writer.addPage(page)

            writer.close()
            splitPaths.add(splitFile!!)
        }
        document.close()

//            MediaScannerConnection.scanFile(
//                context,
//                splitPaths.toTypedArray(),
//                null
//            ) { _, _ ->
//
//            }

        for (file in splitPaths) {
            activity?.sendBroadcast(
                Intent(
                    Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(
                        file
                    )
                )
            )
        }

        isAlive {
            findNavController().popBackStack()
        }


    }

}