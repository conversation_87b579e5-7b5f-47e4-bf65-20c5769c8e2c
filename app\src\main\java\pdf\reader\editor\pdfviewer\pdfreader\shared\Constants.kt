package pdf.reader.editor.pdfviewer.pdfreader.shared

import android.Manifest
import android.provider.MediaStore
import android.provider.MediaStore.Files.FileColumns.DATA
import android.provider.MediaStore.Files.FileColumns.MIME_TYPE
import android.webkit.MimeTypeMap

const val PDF_DIRECTORY = "PDF Reader"
const val PDF_IMAGES = "/$PDF_DIRECTORY/Images/"
const val PDF_PAGES_IMAGES = "/$PDF_DIRECTORY/Images/Page/"


const val DEFAULT_COLOR = 0
//const val DEFAULT_HIGHLIGHT_COLOR = 0
//const val DEFAULT_UNDERLINE_COLOR = ""
//const val DEFAULT_STRIKEOUT_COLOR = ""
//const val DEFAULT_INK_COLOR = ""

const val DEFAULT_HIGHLIGHT_HEIGHT = 0.5f
const val DEFAULT_UNDERLINE_HEIGHT = 0.075f
const val DEFAULT_STRIKEOUT_HEIGHT = 0.375f

const val DEFAULT_INK_THICKNESS = 5.0f

const val DEFAULT_HIGHLIGHT_OPACITY = 0.5f
const val DEFAULT_UNDERLINE_OPACITY = 1.0f
const val DEFAULT_STRIKEOUT_OPACITY = 1.0f


const val HIGHLIGHT_COLOR = "highlight_color"
const val UNDERLINE_COLOR = "underline_color"
const val STRIKEOUT_COLOR = "strikeout_color"
const val INK_COLOR = "ink_color"
const val TEXT_ICON_COLOR = "text_icon_color"

const val INK_THICKNESS = "ink_thickness"

const val HIGHLIGHT_HEIGHT = "highlight_height"
const val UNDERLIE_HEIGHT = "underline_height"
const val STRIKE_HEIGHT = "strikeout_height"

const val HIGHLIGHT_OPACITY = "highlight_opacity"
const val UNDERLIE_OPACITY = "underline_opacity"
const val STRIKE_OPACITY = "strikeout_opacity"


const val TAG_SELECTION = "SelectionLogs: "
const val TAG_RENAME = "RenameNewBug: "
const val TAG_BUGS_SEPT_7 = "BugsSept7: "
const val TAG_DARK_MODE = "DARK_MODE_LOGS: "
const val TAG_PERMISSION_BUG = "PermissionLogs: "
const val KEEP_SCREEN_ON = "keep_screen_on"
const val AUTO_BRIGHTNESS = "auto_brightness"

const val STORAGE_PERMISSION = Manifest.permission.WRITE_EXTERNAL_STORAGE
const val STORAGE_REQUEST_CODE = 1
const val SETTINGS_REQUEST_CODE = 2
const val LOAD_FILES_CODE = 3
const val MANAGE_STORAGE_REQUEST_CODE = 4

//const val DEFAULT_SORT_TITLE = R.string.txt_sorting_title
//const val DEFAULT_SORT_TITLE = "Sort By Name (A to Z)"
const val DEFAULT_SORT = "defaultSort"

const val TIMESTAMP_24_HOURS = 24 * 60 * 60 * 1000

const val FEEDBACK_EMAIL = "<EMAIL>"
const val PRIVACY_POLICY_URL =
    "https://softapss.blogspot.com/2021/09/document-reader-privacy-policy.html"

const val SORT_BY_NAME = MediaStore.MediaColumns.DISPLAY_NAME
const val SORT_BY_TITLE = MediaStore.MediaColumns.TITLE
const val SORT_BY_FILE_SIZE = MediaStore.Files.FileColumns.SIZE
const val SORT_BY_DATE = MediaStore.Files.FileColumns.DATE_MODIFIED
const val SORT_BY_TYPE = MIME_TYPE

val DOC = MimeTypeMap.getSingleton().getMimeTypeFromExtension("doc") ?: "application/msword"
val DOCX = MimeTypeMap.getSingleton().getMimeTypeFromExtension("docx")
    ?: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
const val DOC_CONSTANT = "%.doc"
const val DOCX_CONSTANT = "%.docx"

const val PDF_CONSTANT = "%.pdf"
val PDF = MimeTypeMap.getSingleton().getMimeTypeFromExtension("pdf") ?: "application/pdf"

val XLS = MimeTypeMap.getSingleton().getMimeTypeFromExtension("xls") ?: "application/vnd.ms-excel"
val XLSX = MimeTypeMap.getSingleton().getMimeTypeFromExtension("xlsx")
    ?: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
const val XLS_CONSTANT = "%.xls"
const val XLSX_CONSTANT = "%.xlsx"
const val XLS_SIMPLE = "application/excel"
const val XLS_X = "application/x-excel"
const val XLS_MSX = "application/x-msexcel"

const val PPT_MS = "application/mspowerpoint"
const val PPT_SIMPLE = "application/powerpoint"
const val PPT_MSX = "application/x-mspowerpoint"
const val PPT_CONSTANT = "%.ppt"
const val PPTX_CONSTANT = "%.pptx"
val PPT =
    MimeTypeMap.getSingleton().getMimeTypeFromExtension("ppt") ?: "application/vnd.ms-powerpoint"
val PPTX = MimeTypeMap.getSingleton().getMimeTypeFromExtension("pptx")
    ?: "application/vnd.openxmlformats-officedocument.presentationml.presentation"
val RTF = MimeTypeMap.getSingleton().getMimeTypeFromExtension("rtf") ?: "text/rtf"

val TXT = MimeTypeMap.getSingleton().getMimeTypeFromExtension("txt") ?: "text/plain"
const val TXT_CONSTANT = "%.txt"


val SELECTION_ALL = Pair(
    "$MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR " +
            "$MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR " +
            "$MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR " +
            "$DATA like ? OR $DATA like ? OR $DATA like ? OR $DATA like ? OR " +
            "$DATA like ? OR $DATA like ? OR $DATA like ? OR $DATA like ?",
    arrayOf(
        DOC, DOCX,
        PDF,
        TXT, XLS, XLSX, XLS_SIMPLE, XLS_X, XLS_MSX,
        PPT, PPTX, PPT_MS, PPT_MSX, PPT_SIMPLE,
        RTF,
        DOC_CONSTANT, DOCX_CONSTANT,
        PDF_CONSTANT, TXT_CONSTANT,
        XLS_CONSTANT, XLSX_CONSTANT,
        PPTX_CONSTANT, PPT_CONSTANT,
    )
)

//val SELECTION_DOCX = Pair(
//    MediaStore.Files.FileColumns.MIME_TYPE + "=? OR " + MediaStore.Files.FileColumns.MIME_TYPE + "=? OR " + MediaStore.Files.FileColumns.DATA + " like ? OR " + MediaStore.Files.FileColumns.DATA + " like ? ",
//    arrayOf(DOC, DOCX, DOC_CONSTANT, DOCX_CONSTANT)
//)

val SELECTION_DOCX = Pair(
    "$MIME_TYPE=? OR $MIME_TYPE=? OR $DATA like ? OR $DATA like ? ",
    arrayOf(DOC, DOCX, DOC_CONSTANT, DOCX_CONSTANT)
)

val SELECTION_PDF = Pair(
    "$MIME_TYPE=?  OR $DATA like ?",
    arrayOf(PDF, PDF_CONSTANT)
)

val SELECTION_XLS = Pair(
    "$MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $DATA like ? OR $DATA like ?",
    arrayOf(XLS, XLSX, XLS_SIMPLE, XLS_X, XLS_MSX, XLS_CONSTANT, XLSX_CONSTANT)
)

val SELECTION_PPT = Pair(
    "$MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $MIME_TYPE=? OR $DATA like ? OR $DATA like ?",
    arrayOf(PPT, PPTX, PPT_MS, PPT_SIMPLE, PPT_MSX, PPT_CONSTANT, PPTX_CONSTANT)
)

val SELECTION_TXT = Pair(
    "$MIME_TYPE=? OR $DATA like ?",
    arrayOf(TXT, TXT_CONSTANT)
)
