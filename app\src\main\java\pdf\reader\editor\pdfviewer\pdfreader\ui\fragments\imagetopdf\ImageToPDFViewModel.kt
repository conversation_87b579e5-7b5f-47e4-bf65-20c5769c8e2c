package pdf.reader.editor.pdfviewer.pdfreader.ui.fragments.imagetopdf

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.jiajunhui.xapp.medialoader.MediaLoader
import com.jiajunhui.xapp.medialoader.bean.PhotoResult
import com.jiajunhui.xapp.medialoader.callback.OnPhotoLoaderCallBack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pdf.reader.editor.pdfviewer.pdfreader.anchors.PhotoModel
import pdf.reader.editor.pdfviewer.pdfreader.baseclasses.BaseViewModel

class ImageToPDFViewModel : BaseViewModel() {


    val imagesList = MutableLiveData<List<PhotoModel>>()


    fun loadImages(

        context: FragmentActivity
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            delay(400)

            MediaLoader.getLoader().loadPhotos(context, object : OnPhotoLoaderCallBack() {
                override fun onResult(result: PhotoResult?) {

                    result?.let { photoResult ->
                        if (photoResult.items != null && photoResult.items.size > 0) {
                            val photosList: ArrayList<PhotoModel> = ArrayList()
                            for (photo in photoResult.items) {

                                val photoModel = PhotoModel(path = photo.path)
                                photosList.add(photoModel)
                            }
                            imagesList.value = photosList
                        }

                    }
                }
            })
        }
    }
}