package pdf.reader.editor.pdfviewer.pdfreader.rawcollections

object AppLanguageData {

    fun getAppLanguageList(): ArrayList<String> {
        val arrayList = ArrayList<String>()
        arrayList.add("Afrikaans")
        arrayList.add("Arabic")
        arrayList.add("Chinese")
        arrayList.add("Czech")
        arrayList.add("Danish")
        arrayList.add("Dutch")
        arrayList.add("English")
        arrayList.add("French")
        arrayList.add("German")
        arrayList.add("Greek")
        arrayList.add("Hindi")
        arrayList.add("Indonesian")
        arrayList.add("Italian")
        arrayList.add("Japanese")
        arrayList.add("Korean")
        arrayList.add("Malay")
        arrayList.add("Norwegian")
        arrayList.add("Persian")
        arrayList.add("Portuguese")
        arrayList.add("Russian")
        arrayList.add("Spanish")
        arrayList.add("Thai")
        arrayList.add("Turkish")
        arrayList.add("Urdu")
        arrayList.add("Vietnamese")
        return arrayList
    }

    fun getAppLanguageCodeList(): ArrayList<String> {
        val arrayList = ArrayList<String>()
        arrayList.add("af")
        arrayList.add("ar")
        arrayList.add("zh")
        arrayList.add("cs")
        arrayList.add("da")
        arrayList.add("nl")
        arrayList.add("en")
        arrayList.add("fr")
        arrayList.add("de")
        arrayList.add("el")
        arrayList.add("hi")
        arrayList.add("id")
        arrayList.add("it")
        arrayList.add("ja")
        arrayList.add("ko")
        arrayList.add("ms")
        arrayList.add("no")
        arrayList.add("fa")
        arrayList.add("pt")
        arrayList.add("ru")
        arrayList.add("es")
        arrayList.add("th")
        arrayList.add("tr")
        arrayList.add("ur")
        arrayList.add("vi")
        return arrayList
    }
}