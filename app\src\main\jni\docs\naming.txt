Functions should be named according to one of the following schemes:

	verb_noun
	verb_noun_with_noun

	noun_attribute
	get_noun_attribute -- when the 'noun_attribute' name conflicts with a type
	set_noun_attribute

Prefixes are mandatory for exported functions, macros, enums, globals and types.

	fz for common code
	pdf, xps, etc., for interpreter specific code

Prefixes are optional (but encouraged) for private functions and types.

Avoid using 'get' as this is a meaningless and redundant filler word.

These words are reserved for reference counting schemes:

	new, find, load, open, keep -- return objects that you are responsible for freeing.

	drop, free, close -- relinquish ownership of the object passed in.

When searching for an object or value, the name used depends on whether
returning the value is passing ownership:

	lookup -- return a value or borrowed pointer

	find -- return an object that the caller is responsible for freeing
